@extends('layouts.app')

@section('title', __('messages.profile_title'))
@section('description', __('messages.profile_description'))

@section('content')
<!-- Profile Header with Cover -->
<section class="relative">
    <!-- Cover Image -->
    <div class="h-48 md:h-56 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-500 relative overflow-hidden">
        <div class="absolute inset-0 bg-black/20"></div>
        <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent"></div>

        <!-- Floating Elements -->
        <div class="absolute top-8 left-8 w-16 h-16 bg-white/10 rounded-full blur-lg"></div>
        <div class="absolute top-16 right-16 w-24 h-24 bg-white/5 rounded-full blur-xl"></div>
        <div class="absolute bottom-8 left-1/3 w-12 h-12 bg-white/10 rounded-full blur-md"></div>
    </div>

    <!-- Profile Info Overlay -->
    <div class="absolute bottom-0 left-0 right-0 p-6">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-end space-x-6 rtl:space-x-reverse">
                <!-- Profile Picture -->
                <div class="relative flex-shrink-0">
                    <div class="w-24 h-24 md:w-28 md:h-28 bg-white rounded-full shadow-xl flex items-center justify-center border-4 border-white">
                        @if($user->profile && $user->profile->avatar)
                            <img src="{{ $user->profile->avatar }}" alt="{{ $user->name }}" class="w-full h-full rounded-full object-cover">
                        @else
                            <span class="text-2xl md:text-3xl font-bold text-gray-600">{{ substr($user->name, 0, 1) }}</span>
                        @endif
                    </div>
                    <!-- Status Indicator -->
                    <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-3 border-white flex items-center justify-center">
                        <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    </div>
                </div>

                <!-- User Info -->
                <div class="flex-1 text-white pb-3">
                    <h1 class="text-2xl md:text-3xl font-bold mb-1">{{ $user->name }}</h1>
                    <p class="text-sm md:text-base text-white/90 mb-2">
                        @if($user->profile && $user->profile->current_job)
                            {{ $user->profile->current_job }}
                        @else
                            {{ __('messages.monori_user') }}
                        @endif
                    </p>
                    <div class="flex items-center space-x-4 rtl:space-x-reverse text-white/80 text-xs md:text-sm">
                        <div class="flex items-center space-x-1 rtl:space-x-reverse">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>
                                @if($user->profile && $user->profile->city)
                                    {{ $user->profile->city }}
                                @else
                                    {{ __('messages.not_specified') }}
                                @endif
                            </span>
                        </div>
                        <div class="flex items-center space-x-1 rtl:space-x-reverse">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ $stats['completed_activities'] }} {{ __('messages.achievements') }}</span>
                        </div>
                        <div class="flex items-center space-x-1 rtl:space-x-reverse">
                            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <span>{{ __('messages.member_since') }} {{ $stats['join_date']->format('Y') }}</span>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex space-x-2 rtl:space-x-reverse pb-3">
                    <a href="{{ route('profile.edit') }}" class="bg-white text-gray-900 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-100 transition-colors duration-200 shadow-lg">
                        {{ __('messages.edit_profile') }}
                    </a>
                    <button onclick="shareProfile()" class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-white/30 transition-colors duration-200 border border-white/30">
                        {{ __('messages.share_profile') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Statistics Section -->
<section class="py-8 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-8">
            <h2 class="text-xl md:text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.my_professional_stats') }}</h2>
            <p class="text-sm text-gray-600 dark:text-gray-300">{{ __('messages.comprehensive_overview') }}</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <!-- Tests Completed -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 border-l-4 border-blue-500 hover:shadow-xl transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-600 dark:text-gray-400">{{ __('messages.completed_tests') }}</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['completed_tests'] }}</p>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="text-blue-600 dark:text-blue-400 text-xs font-medium">
                        @if($stats['completed_tests'] > 0) {{ __('messages.excellent') }} @else {{ __('messages.start_now') }} @endif
                    </span>
                </div>
            </div>

            <!-- CV Improvements -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 border-l-4 border-green-500 hover:shadow-xl transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-600 dark:text-gray-400">{{ __('messages.cv_improvements') }}</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['cv_improvements'] }}</p>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="text-green-600 dark:text-green-400 text-xs font-medium">
                        @if($stats['cv_improvements'] > 0) {{ __('messages.improved') }} @else {{ __('messages.ready_to_improve') }} @endif
                    </span>
                </div>
            </div>

            <!-- Interview Practice -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 border-l-4 border-purple-500 hover:shadow-xl transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-600 dark:text-gray-400">{{ __('messages.interview_practice') }}</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['interview_sessions'] }}</p>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="text-purple-600 dark:text-purple-400 text-xs font-medium">
                        @if($stats['interview_sessions'] > 0) {{ __('messages.trained') }} @else {{ __('messages.start_training') }} @endif
                    </span>
                </div>
            </div>

            <!-- Career Score -->
            <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-4 border-l-4 border-orange-500 hover:shadow-xl transition-shadow duration-200">
                <div class="flex items-center justify-between">
                    <div class="w-10 h-10 bg-orange-500 rounded-lg flex items-center justify-center">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                    </div>
                    <div class="text-right">
                        <p class="text-xs font-medium text-gray-600 dark:text-gray-400">{{ __('messages.career_score') }}</p>
                        <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $stats['overall_progress'] }}%</p>
                    </div>
                </div>
                <div class="mt-3">
                    <span class="text-orange-600 dark:text-orange-400 text-xs font-medium">
                        @if($stats['overall_progress'] >= 80) {{ __('messages.excellent_score') }} @elseif($stats['overall_progress'] >= 60) {{ __('messages.good_score') }} @elseif($stats['overall_progress'] >= 40) {{ __('messages.average_score') }} @else {{ __('messages.start_now') }} @endif
                    </span>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Profile Content -->
<section class="py-12 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Main Content -->
            <div class="lg:col-span-2 space-y-8">
                <!-- About Section -->
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <svg class="w-4 h-4 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        {{ __('messages.about_me') }}
                    </h2>
                    
                    @if($user->profile && $user->profile->bio)
                        <p class="text-gray-600 dark:text-gray-300 leading-relaxed text-sm">
                            {{ $user->profile->bio }}
                        </p>
                    @else
                        <div class="text-center py-6">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            <p class="text-gray-500 dark:text-gray-400 mb-3 text-sm">{{ __('messages.no_bio_added') }}</p>
                            <button class="text-blue-600 dark:text-blue-400 text-sm font-medium hover:text-blue-700 dark:hover:text-blue-300">
                                {{ __('messages.add_bio') }}
                            </button>
                        </div>
                    @endif
                </div>

                <!-- Skills & Expertise -->
                @if($user->profile && $user->profile->skills)
                <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
                    <h2 class="text-lg font-bold text-gray-900 dark:text-white mb-4 flex items-center">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        {{ __('messages.skills_expertise') }}
                    </h2>

                    <div class="grid grid-cols-2 md:grid-cols-3 gap-2">
                        @foreach($user->profile->skills as $skill)
                            <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3 text-center border border-blue-100 dark:border-blue-800">
                                <span class="text-gray-800 dark:text-gray-200 text-sm font-medium">{{ $skill }}</span>
                            </div>
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Recent Activity -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-blue-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        {{ __('messages.recent_activity') }}
                    </h2>

                    <div class="space-y-6 mb-8">
                        @forelse($recentActivities as $activity)
                            <div class="flex items-start space-x-4 rtl:space-x-reverse">
                                <div class="w-10 h-10 bg-{{ $activity->status_badge }}-100 dark:bg-{{ $activity->status_badge }}-900/20 rounded-full flex items-center justify-center flex-shrink-0">
                                    @if($activity->type === 'personality_test')
                                        <svg class="w-5 h-5 text-{{ $activity->status_badge }}-600 dark:text-{{ $activity->status_badge }}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    @elseif($activity->type === 'cv_generation')
                                        <svg class="w-5 h-5 text-{{ $activity->status_badge }}-600 dark:text-{{ $activity->status_badge }}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    @elseif($activity->type === 'interview_training')
                                        <svg class="w-5 h-5 text-{{ $activity->status_badge }}-600 dark:text-{{ $activity->status_badge }}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                        </svg>
                                    @else
                                        <svg class="w-5 h-5 text-{{ $activity->status_badge }}-600 dark:text-{{ $activity->status_badge }}-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                        </svg>
                                    @endif
                                </div>
                                <div class="flex-1">
                                    <p class="text-gray-900 dark:text-white font-medium">{{ $activity->title }}</p>
                                    <p class="text-gray-600 dark:text-gray-400 text-sm">
                                        @if($activity->description)
                                            {{ Str::limit($activity->description, 60) }}
                                        @endif
                                        @if($activity->status === 'completed')
                                            <span class="text-green-600 dark:text-green-400 font-medium">✓ {{ __('messages.completed_status') }}</span>
                                        @elseif($activity->status === 'in_progress')
                                            <span class="text-yellow-600 dark:text-yellow-400 font-medium">⏳ {{ __('messages.in_progress_status') }} ({{ $activity->progress_percentage }}%)</span>
                                        @else
                                            <span class="text-gray-500 dark:text-gray-400">⏸️ {{ __('messages.pending_status') }}</span>
                                        @endif
                                    </p>
                                    <p class="text-gray-500 dark:text-gray-500 text-xs mt-1">{{ $activity->updated_at->diffForHumans() }}</p>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-8">
                                <svg class="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                </svg>
                                <p class="text-gray-500 dark:text-gray-400">{{ __('messages.no_activities_yet') }}</p>
                                <p class="text-gray-400 dark:text-gray-500 text-sm mt-1">{{ __('messages.start_career_journey') }}</p>
                            </div>
                        @endforelse
                    </div>

                    <div class="text-center">
                        <a href="{{ route('profile.activities') }}" class="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium inline-flex items-center">
                            {{ __('messages.view_all_activities') }}
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Recent Achievements -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-yellow-600 mr-3" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        {{ __('messages.recent_achievements') }}
                    </h2>
                    
                    @forelse($recentAchievements as $achievement)
                        <div class="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 rounded-xl mb-4 border border-green-100 dark:border-green-800">
                            <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ $achievement->title }}</h3>
                                <p class="text-gray-600 dark:text-gray-300 text-sm">{{ $achievement->description }}</p>
                                <p class="text-gray-500 dark:text-gray-400 text-xs mt-1">{{ $achievement->completed_at->diffForHumans() }}</p>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            <p class="text-gray-500 dark:text-gray-400 mb-4">{{ __('messages.no_achievements_yet') }}</p>
                            <a href="{{ route('services.personality-analysis') }}" class="text-blue-600 dark:text-blue-400 font-medium hover:text-blue-700 dark:hover:text-blue-300">
                                {{ __('messages.start_career_journey_now') }}
                            </a>
                        </div>
                    @endforelse
                </div>

                <!-- AI Service Results -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                    <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg class="w-6 h-6 text-indigo-600 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        {{ __('messages.ai_service_results') }}
                    </h2>

                    @forelse($recentServiceResults as $result)
                        <div class="flex items-center space-x-4 rtl:space-x-reverse p-4 bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl mb-4 border border-indigo-100 dark:border-indigo-800">
                            <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-purple-500 rounded-xl flex items-center justify-center">
                                @if($result->service_type === 'personality_analysis')
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                @elseif($result->service_type === 'cv_improvement')
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                @else
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                @endif
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 dark:text-white">{{ $result->title }}</h4>
                                <p class="text-gray-600 dark:text-gray-400 text-sm">
                                    {{ Str::limit($result->description, 60) }}
                                    @if($result->score)
                                        <span class="text-indigo-600 dark:text-indigo-400 font-medium">• {{ $result->score }}%</span>
                                    @endif
                                </p>
                                <p class="text-gray-500 dark:text-gray-500 text-xs mt-1">{{ $result->created_at->diffForHumans() }}</p>
                            </div>
                            <a href="{{ route('ai-service.result', $result->id) }}" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 text-sm font-medium">
                                {{ __('messages.view_result') }}
                            </a>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            <p class="text-gray-500 dark:text-gray-400 mb-4">{{ __('messages.no_ai_results_yet') }}</p>
                            <a href="{{ route('ai-service.personality-analysis') }}" class="text-indigo-600 dark:text-indigo-400 font-medium hover:text-indigo-700 dark:hover:text-indigo-300">
                                {{ __('messages.try_ai_services') }}
                            </a>
                        </div>
                    @endforelse

                    <div class="text-center mt-6">
                        <a href="{{ route('ai-service.history') }}" class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-800 dark:hover:text-indigo-300 text-sm font-medium inline-flex items-center">
                            {{ __('messages.view_all_results') }}
                            <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="space-y-8">
                <!-- Stats Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        {{ __('messages.my_statistics') }}
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                            <span class="text-gray-700 dark:text-gray-300">{{ __('messages.completed_activities') }}</span>
                            <span class="font-bold text-blue-600 dark:text-blue-400">{{ $stats['completed_activities'] }}</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                            <span class="text-gray-700 dark:text-gray-300">{{ __('messages.completed_assessments') }}</span>
                            <span class="font-bold text-green-600 dark:text-green-400">{{ $stats['completed_assessments'] }}</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                            <span class="text-gray-700 dark:text-gray-300">{{ __('messages.average_results') }}</span>
                            <span class="font-bold text-purple-600 dark:text-purple-400">{{ round($stats['average_score']) }}%</span>
                        </div>
                        <div class="flex items-center justify-between p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg">
                            <span class="text-gray-700 dark:text-gray-300">{{ __('messages.completion_rate') }}</span>
                            <span class="font-bold text-orange-600 dark:text-orange-400">{{ $completionRate }}%</span>
                        </div>
                    </div>
                </div>

                <!-- Progress Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg class="w-5 h-5 text-purple-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                        {{ __('messages.monthly_progress') }}
                    </h3>

                    <div class="space-y-4">
                        <!-- Personality Tests Progress -->
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>{{ __('messages.personality_tests') }}</span>
                                <span>{{ $progressData['personality_tests']['completed'] }}/{{ $progressData['personality_tests']['total'] }}</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-blue-500 h-2 rounded-full" style="width: {{ $progressData['personality_tests']['percentage'] }}%"></div>
                            </div>
                        </div>

                        <!-- CV Generation Progress -->
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>{{ __('messages.cv_improvement') }}</span>
                                <span>{{ $progressData['cv_generation']['completed'] }}/{{ $progressData['cv_generation']['total'] }}</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-green-500 h-2 rounded-full" style="width: {{ $progressData['cv_generation']['percentage'] }}%"></div>
                            </div>
                        </div>

                        <!-- Interview Training Progress -->
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>{{ __('messages.interview_training') }}</span>
                                <span>{{ $progressData['interview_training']['completed'] }}/{{ $progressData['interview_training']['total'] }}</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-purple-500 h-2 rounded-full" style="width: {{ $progressData['interview_training']['percentage'] }}%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">{{ __('messages.quick_actions') }}</h3>

                    <div class="space-y-3">
                        @forelse($quickActions as $action)
                            <div class="block w-full bg-{{ $action->status_badge }}-50 dark:bg-{{ $action->status_badge }}-900/20 text-{{ $action->status_badge }}-700 dark:text-{{ $action->status_badge }}-300 px-4 py-3 rounded-lg text-sm">
                                <div class="flex items-center justify-between">
                                    <span class="font-medium">{{ $action->title }}</span>
                                    @if($action->status === 'in_progress')
                                        <span class="text-xs bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 px-2 py-1 rounded-full">
                                            {{ $action->progress_percentage }}%
                                        </span>
                                    @elseif($action->status === 'pending')
                                        <span class="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 px-2 py-1 rounded-full">
                                            {{ __('messages.pending_action') }}
                                        </span>
                                    @endif
                                </div>
                                @if($action->description)
                                    <p class="text-xs mt-1 opacity-75">{{ Str::limit($action->description, 50) }}</p>
                                @endif
                            </div>
                        @empty
                            <a href="{{ route('services.personality-analysis') }}" class="block w-full bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-4 py-3 rounded-lg text-sm font-medium hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors duration-200">
                                {{ __('messages.new_personality_test') }}
                            </a>
                            <a href="{{ route('services.cv-improvement') }}" class="block w-full bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-4 py-3 rounded-lg text-sm font-medium hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors duration-200">
                                {{ __('messages.cv_analysis') }}
                            </a>
                            <a href="{{ route('services.interview-simulation') }}" class="block w-full bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-4 py-3 rounded-lg text-sm font-medium hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors duration-200">
                                {{ __('messages.training_interview') }}
                            </a>
                        @endforelse
                    </div>
                </div>

                <!-- Contact Info -->
                @if($user->profile)
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                        <svg class="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                        </svg>
                        {{ __('messages.contact_info') }}
                    </h3>
                    
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 rtl:space-x-reverse">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300">{{ $user->email }}</span>
                        </div>
                        
                        @if($user->profile->phone)
                        <div class="flex items-center space-x-3 rtl:space-x-reverse">
                            <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            <span class="text-gray-600 dark:text-gray-300">{{ $user->profile->phone }}</span>
                        </div>
                        @endif
                        
                        @if($user->profile->linkedin_url)
                        <div class="flex items-center space-x-3 rtl:space-x-reverse">
                            <svg class="w-5 h-5 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.338 16.338H13.67V12.16c0-.995-.017-2.277-1.387-2.277-1.39 0-1.601 1.086-1.601 2.207v4.248H8.014v-8.59h2.559v1.174h.037c.356-.675 1.227-1.387 2.526-1.387 2.703 0 3.203 1.778 3.203 4.092v4.711zM5.005 6.575a1.548 1.548 0 11-.003-3.096 1.548 1.548 0 01.003 3.096zm-1.337 9.763H6.34v-8.59H3.667v8.59zM17.668 1H2.328C1.595 1 1 1.581 1 2.298v15.403C1 18.418 1.595 19 2.328 19h15.34c.734 0 1.332-.582 1.332-1.299V2.298C19 1.581 18.402 1 17.668 1z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ $user->profile->linkedin_url }}" target="_blank" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300">LinkedIn</a>
                        </div>
                        @endif
                        
                        @if($user->profile->github_url)
                        <div class="flex items-center space-x-3 rtl:space-x-reverse">
                            <svg class="w-5 h-5 text-gray-800 dark:text-gray-200" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z" clip-rule="evenodd"></path>
                            </svg>
                            <a href="{{ $user->profile->github_url }}" target="_blank" class="text-gray-800 dark:text-gray-200 hover:text-gray-600 dark:hover:text-gray-400">GitHub</a>
                        </div>
                        @endif
                    </div>
                </div>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Share Modal -->
<div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md w-full mx-4">
        <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">{{ __('messages.share_profile_modal_title') }}</h3>
        <p class="text-gray-600 dark:text-gray-300 mb-6">{{ __('messages.share_profile_modal_desc') }}</p>

        <div class="space-y-4">
            <div>
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.share_link') }}</label>
                <div class="flex">
                    <input type="text" id="shareUrl" readonly class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-l-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white text-sm">
                    <button onclick="copyShareUrl()" class="px-4 py-2 bg-blue-600 text-white rounded-r-lg hover:bg-blue-700 transition-colors duration-200">
                        {{ __('messages.copy') }}
                    </button>
                </div>
            </div>

            <div class="flex space-x-3 rtl:space-x-reverse">
                <button onclick="shareOnWhatsApp()" class="flex-1 bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors duration-200">
                    {{ __('messages.whatsapp') }}
                </button>
                <button onclick="shareOnTwitter()" class="flex-1 bg-blue-400 text-white px-4 py-2 rounded-lg hover:bg-blue-500 transition-colors duration-200">
                    {{ __('messages.twitter') }}
                </button>
                <button onclick="shareOnLinkedIn()" class="flex-1 bg-blue-700 text-white px-4 py-2 rounded-lg hover:bg-blue-800 transition-colors duration-200">
                    {{ __('messages.linkedin') }}
                </button>
            </div>
        </div>

        <div class="mt-6 flex justify-end">
            <button onclick="closeShareModal()" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                {{ __('messages.close') }}
            </button>
        </div>
    </div>
</div>

<script>
function shareProfile() {
    fetch('{{ route("profile.share") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('shareUrl').value = data.share_url;
            document.getElementById('shareModal').classList.remove('hidden');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __('messages.share_error') }}');
    });
}

function closeShareModal() {
    document.getElementById('shareModal').classList.add('hidden');
}

function copyShareUrl() {
    const shareUrl = document.getElementById('shareUrl');
    shareUrl.select();
    shareUrl.setSelectionRange(0, 99999);
    navigator.clipboard.writeText(shareUrl.value);

    // Show feedback
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = '{{ __('messages.copied') }}';
    button.classList.add('bg-green-600');

    setTimeout(() => {
        button.textContent = originalText;
        button.classList.remove('bg-green-600');
    }, 2000);
}

function shareOnWhatsApp() {
    const url = document.getElementById('shareUrl').value;
    const text = '{{ __('messages.share_text') }}';
    window.open(`https://wa.me/?text=${encodeURIComponent(text + ' ' + url)}`, '_blank');
}

function shareOnTwitter() {
    const url = document.getElementById('shareUrl').value;
    const text = '{{ __('messages.share_text') }}';
    window.open(`https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(url)}`, '_blank');
}

function shareOnLinkedIn() {
    const url = document.getElementById('shareUrl').value;
    window.open(`https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(url)}`, '_blank');
}

// Close modal when clicking outside
document.getElementById('shareModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeShareModal();
    }
});
</script>
@endsection
