<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'لوحة التحكم') - MonOri AI</title>
    <meta name="description" content="@yield('description', 'لوحة التحكم الخاصة بمنصة MonOri AI')">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Styles -->
    @if(file_exists(public_path('build/manifest.json')))
        <link rel="stylesheet" href="{{ asset('build/assets/app-CAktcPGf.css') }}">
        <script src="{{ asset('build/assets/app-rMtNrB6F.js') }}" defer></script>
    @else
        @vite(['resources/css/app.css', 'resources/js/app.js'])
    @endif

    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <!-- Theme Script -->
    <script>
        (function() {
            const theme = localStorage.getItem('theme') || 'dark';
            document.documentElement.classList.add(theme);
        })();
    </script>

    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }

        .dark body {
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 100%);
        }

        .sidebar-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .card-hover {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .card-hover:hover {
            transform: translateY(-12px) scale(1.03);
            box-shadow: 0 35px 60px rgba(0,0,0,0.2);
        }

        .shadow-3xl {
            box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
        }

        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .dark .glass-effect {
            background: rgba(0, 0, 0, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .floating-animation {
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .pulse-glow {
            animation: pulse-glow 2s ease-in-out infinite alternate;
        }

        @keyframes pulse-glow {
            from { box-shadow: 0 0 20px rgba(59, 130, 246, 0.4); }
            to { box-shadow: 0 0 30px rgba(59, 130, 246, 0.8); }
        }

        .sidebar-item {
            position: relative;
            overflow: hidden;
        }

        .sidebar-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
            transition: left 0.5s;
        }

        .sidebar-item:hover::before {
            left: 100%;
        }

        .custom-scrollbar::-webkit-scrollbar {
            width: 6px;
        }

        .custom-scrollbar::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 10px;
        }

        .custom-scrollbar::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }
    </style>
</head>

<body class="bg-gray-100 dark:bg-gray-900 font-sans antialiased">
    <div class="min-h-screen" x-data="{ sidebarOpen: false }">

        <!-- Sidebar -->
        <div class="fixed inset-y-0 right-0 z-50 w-80 bg-gradient-to-b from-indigo-900 via-purple-900 to-pink-900 shadow-2xl transform transition-all duration-300 ease-in-out lg:translate-x-0"
             :class="sidebarOpen ? 'translate-x-0' : 'translate-x-full lg:translate-x-0'">
            
            <!-- Logo -->
            <div class="flex items-center justify-center h-24 px-6 border-b border-white/20">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-3xl flex items-center justify-center mb-3 mx-auto shadow-lg">
                        <span class="text-2xl font-bold text-white">M</span>
                    </div>
                    <h1 class="text-2xl font-bold text-white mb-1">MonOri AI</h1>
                    <span class="px-4 py-1 text-xs bg-gradient-to-r from-blue-400 to-purple-400 text-white rounded-full font-semibold shadow-lg">
                        لوحة التحكم الذكية
                    </span>
                </div>
            </div>

            <!-- Navigation -->
            <nav class="flex-1 px-6 py-8 space-y-3 overflow-y-auto">
                <div class="mb-6">
                    <p class="text-xs font-semibold text-gray-400 uppercase tracking-wider mb-3">القائمة الرئيسية</p>
                </div>

                <a href="{{ route('admin.dashboard') }}"
                   class="group flex items-center px-4 py-4 text-gray-300 rounded-2xl hover:bg-white/10 transition-all duration-200 {{ request()->routeIs('admin.dashboard') ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border-r-4 border-blue-400' : '' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-blue-500 to-blue-600 mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-semibold">لوحة التحكم</p>
                        <p class="text-xs text-gray-400">الإحصائيات العامة</p>
                    </div>
                </a>

                <a href="{{ route('admin.users') }}"
                   class="group flex items-center px-4 py-4 text-gray-300 rounded-2xl hover:bg-white/10 transition-all duration-200 {{ request()->routeIs('admin.users') ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border-r-4 border-blue-400' : '' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-green-500 to-green-600 mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-semibold">المستخدمون</p>
                        <p class="text-xs text-gray-400">إدارة المستخدمين</p>
                    </div>
                </a>

                <a href="{{ route('admin.analytics') }}"
                   class="group flex items-center px-4 py-4 text-gray-300 rounded-2xl hover:bg-white/10 transition-all duration-200 {{ request()->routeIs('admin.analytics') ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border-r-4 border-blue-400' : '' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-semibold">التحليلات</p>
                        <p class="text-xs text-gray-400">إحصائيات مفصلة</p>
                    </div>
                </a>

                <a href="{{ route('admin.settings') }}"
                   class="group flex items-center px-4 py-4 text-gray-300 rounded-2xl hover:bg-white/10 transition-all duration-200 {{ request()->routeIs('admin.settings') ? 'bg-gradient-to-r from-blue-500/20 to-purple-500/20 text-white border-r-4 border-blue-400' : '' }}">
                    <div class="flex items-center justify-center w-10 h-10 rounded-xl bg-gradient-to-br from-orange-500 to-orange-600 mr-4 group-hover:scale-110 transition-transform">
                        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                        </svg>
                    </div>
                    <div>
                        <p class="font-semibold">الإعدادات</p>
                        <p class="text-xs text-gray-400">إعدادات النظام</p>
                    </div>
                </a>
            </nav>

            <!-- User Info -->
            <div class="px-6 py-6 border-t border-white/10 mt-auto">
                <div class="bg-white/5 rounded-2xl p-4 backdrop-blur-sm">
                    <div class="flex items-center mb-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                            <span class="text-white text-lg font-bold">{{ substr(auth()->user()->name ?? 'A', 0, 1) }}</span>
                        </div>
                        <div class="mr-3">
                            <p class="text-sm font-semibold text-white">{{ auth()->user()->name ?? 'Admin' }}</p>
                            <p class="text-xs text-gray-300">مدير النظام</p>
                        </div>
                    </div>

                    <div class="flex items-center justify-between">
                        <!-- Theme Toggle -->
                        <button onclick="toggleTheme()" class="p-2 text-gray-300 hover:text-white rounded-xl hover:bg-white/10 transition-all">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                            </svg>
                        </button>

                        <!-- Back to Website -->
                        <a href="{{ route('home') }}" class="p-2 text-gray-300 hover:text-white rounded-xl hover:bg-white/10 transition-all" title="العودة للموقع">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
                            </svg>
                        </a>

                        <!-- Logout -->
                        <form method="POST" action="{{ route('logout') }}" class="inline">
                            @csrf
                            <button type="submit" class="p-2 text-gray-300 hover:text-red-400 rounded-xl hover:bg-white/10 transition-all" title="تسجيل الخروج">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content -->
        <div class="lg:mr-80 min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 dark:from-gray-900 dark:to-indigo-900">
            <!-- Top Header -->
            <header class="bg-white dark:bg-gray-800 shadow-lg border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
                <div class="flex items-center justify-between px-8 py-6">
                    <!-- Mobile menu button -->
                    <button @click="sidebarOpen = !sidebarOpen" class="lg:hidden p-3 rounded-2xl text-gray-600 hover:text-gray-900 hover:bg-gray-100 dark:hover:bg-gray-700 transition-all">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                        </svg>
                    </button>

                    <div class="flex-1">
                        <h1 class="text-4xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
                            @yield('page-title', 'لوحة التحكم')
                        </h1>
                        <p class="text-gray-600 dark:text-gray-400 mt-2 text-lg">@yield('page-description', 'مرحباً بك في لوحة التحكم الذكية')</p>
                    </div>

                    <div class="flex items-center space-x-4">
                        <!-- Notifications -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 relative">
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82a3 3 0 00-4.24 0L2.82 5.83a3 3 0 000 4.24l2.01 2.01a3 3 0 004.24 0l2.01-2.01a3 3 0 000-4.24L10.07 2.82z"></path>
                                </svg>
                                <span id="notification-badge" class="absolute top-0 right-0 w-2 h-2 bg-red-500 rounded-full animate-pulse"></span>
                            </button>

                            <!-- Notifications Dropdown -->
                            <div x-show="open" @click.away="open = false" x-transition class="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 z-50">
                                <div class="p-4 border-b border-gray-200 dark:border-gray-700">
                                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white">الإشعارات</h3>
                                </div>
                                <div id="notifications-container" class="max-h-96 overflow-y-auto">
                                    <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82a3 3 0 00-4.24 0L2.82 5.83a3 3 0 000 4.24l2.01 2.01a3 3 0 004.24 0l2.01-2.01a3 3 0 000-4.24L10.07 2.82z"></path>
                                        </svg>
                                        <p>جاري تحميل الإشعارات...</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Current Time -->
                        <div class="text-sm text-gray-600 dark:text-gray-400">
                            <span id="current-time"></span>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <main class="min-h-screen">
                <div class="px-8 py-8">
                    @if(session('success'))
                        <div class="mb-8 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-6 py-4 rounded-2xl shadow-lg">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                {{ session('success') }}
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-8 bg-gradient-to-r from-red-500 to-pink-500 text-white px-6 py-4 rounded-2xl shadow-lg">
                            <div class="flex items-center">
                                <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                </svg>
                                {{ session('error') }}
                            </div>
                        </div>
                    @endif

                    @yield('content')
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        // Theme toggle
        function toggleTheme() {
            const html = document.documentElement;
            const isDark = html.classList.contains('dark');
            
            if (isDark) {
                html.classList.remove('dark');
                html.classList.add('light');
                localStorage.setItem('theme', 'light');
            } else {
                html.classList.remove('light');
                html.classList.add('dark');
                localStorage.setItem('theme', 'dark');
            }
        }

        // Update current time
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString('ar-MA', {
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
            document.getElementById('current-time').textContent = timeString;
        }

        setInterval(updateTime, 1000);
        updateTime();

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('[x-data]');
            const sidebarButton = document.querySelector('button[\\@click="sidebarOpen = !sidebarOpen"]');

            if (window.innerWidth < 1024 && !sidebar.contains(event.target) && !sidebarButton.contains(event.target)) {
                Alpine.store('sidebarOpen', false);
            }
        });

        // Notifications functionality
        async function fetchNotifications() {
            try {
                const response = await fetch('/admin/notifications', {
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    updateNotifications(data);
                }
            } catch (error) {
                console.error('Error fetching notifications:', error);
            }
        }

        function updateNotifications(data) {
            const container = document.getElementById('notifications-container');
            const badge = document.getElementById('notification-badge');

            if (!container) return;

            // Update badge
            if (data.unread_count > 0) {
                badge.style.display = 'block';
                badge.textContent = data.unread_count > 9 ? '9+' : data.unread_count;
            } else {
                badge.style.display = 'none';
            }

            // Build notifications HTML
            let notificationsHtml = '';

            // New transactions
            if (data.new_transactions && data.new_transactions.length > 0) {
                data.new_transactions.forEach(transaction => {
                    notificationsHtml += `
                        <div class="p-3 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                    </svg>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">معاملة جديدة</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">${transaction.user?.name || 'مستخدم'} - ${parseFloat(transaction.amount).toFixed(2)} درهم</p>
                                </div>
                                <span class="text-xs text-gray-400">${formatTimeAgo(transaction.created_at)}</span>
                            </div>
                        </div>
                    `;
                });
            }

            // New users
            if (data.new_users && data.new_users.length > 0) {
                data.new_users.forEach(user => {
                    notificationsHtml += `
                        <div class="p-3 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">مستخدم جديد</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">${user.name} انضم للمنصة</p>
                                </div>
                                <span class="text-xs text-gray-400">${formatTimeAgo(user.created_at)}</span>
                            </div>
                        </div>
                    `;
                });
            }

            // Failed transactions
            if (data.failed_transactions && data.failed_transactions.length > 0) {
                data.failed_transactions.forEach(transaction => {
                    notificationsHtml += `
                        <div class="p-3 border-b border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                    <svg class="w-4 h-4 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                </div>
                                <div class="ml-3 flex-1">
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">معاملة فاشلة</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">${transaction.user?.name || 'مستخدم'} - ${parseFloat(transaction.amount).toFixed(2)} درهم</p>
                                </div>
                                <span class="text-xs text-gray-400">${formatTimeAgo(transaction.created_at)}</span>
                            </div>
                        </div>
                    `;
                });
            }

            if (notificationsHtml === '') {
                notificationsHtml = `
                    <div class="p-4 text-center text-gray-500 dark:text-gray-400">
                        <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5v-5zM10.07 2.82a3 3 0 00-4.24 0L2.82 5.83a3 3 0 000 4.24l2.01 2.01a3 3 0 004.24 0l2.01-2.01a3 3 0 000-4.24L10.07 2.82z"></path>
                        </svg>
                        <p>لا توجد إشعارات جديدة</p>
                    </div>
                `;
            }

            container.innerHTML = notificationsHtml;
        }

        function formatTimeAgo(dateString) {
            const date = new Date(dateString);
            const now = new Date();
            const diffInSeconds = Math.floor((now - date) / 1000);

            if (diffInSeconds < 60) return 'الآن';
            if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)} دقيقة`;
            if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)} ساعة`;
            return `${Math.floor(diffInSeconds / 86400)} يوم`;
        }

        // Initialize notifications
        document.addEventListener('DOMContentLoaded', function() {
            fetchNotifications();
            setInterval(fetchNotifications, 60000); // Update every minute
        });
    </script>

    @stack('scripts')
</body>
</html>
