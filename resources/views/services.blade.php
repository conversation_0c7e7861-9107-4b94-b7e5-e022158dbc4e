@extends('layouts.app')

@section('title', __('messages.nav_services') . ' - MonOri AI')
@section('description', __('messages.services_subtitle'))

@section('content')
<style>
.service-levels {
    margin: 1rem 0;
}

.level-option {
    transition: all 0.3s ease;
    cursor: pointer;
}

.level-option:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.level-option.popular {
    position: relative;
    border-width: 2px !important;
}

.level-badge {
    position: absolute;
    top: -8px;
    left: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 0.75rem;
    padding: 4px 8px;
    border-radius: 4px;
    font-weight: 600;
    z-index: 10;
}

.btn-primary-compact {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-decoration: none;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    text-align: center;
}

.btn-primary-compact:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}
</style>

<!-- Services Hero Section -->
<section class="py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                <span class="gradient-text">{{ __('messages.services_title') }}</span>
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {{ __('messages.services_subtitle') }}
            </p>
        </div>
    </div>
</section>

<!-- Services Grid Section -->
<section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Personality Analysis -->
            <div class="card-hover bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-8 rounded-2xl">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">{{ __('messages.personality_analysis_title') }}</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">{{ __('messages.personality_analysis_description') }}</p>

                <!-- Service Level Options -->
                <div class="grid grid-cols-1 gap-4 mb-6">
                    <!-- Free Level -->
                    <div class="p-4 border-2 border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.basic_level') }}</span>
                            <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">{{ __('messages.free') }}</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('messages.basic_personality_desc') }}</p>
                        <a href="{{ route('services.personality-analysis-free') }}" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-center font-medium transition-colors inline-block">
                            {{ __('messages.start_free') }}
                        </a>
                    </div>

                    <!-- Professional Level -->
                    <div class="p-4 border-2 border-purple-200 dark:border-purple-700 bg-purple-50 dark:bg-purple-900/20 rounded-lg relative">
                        <div class="absolute -top-2 left-4 bg-purple-500 text-white text-xs px-2 py-1 rounded">{{ __('messages.recommended') }}</div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.professional_level') }}</span>
                            <span class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ __('messages.personality_pro_price') }}</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('messages.professional_personality_desc') }}</p>
                        <button onclick="redirectToPayment('personality', 'professional')"
                                class="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg text-center font-medium transition-colors">
                            {{ __('messages.get_professional') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- CV Generation -->
            <div class="card-hover bg-gradient-to-br from-green-50 to-blue-50 dark:from-green-900/20 dark:to-blue-900/20 p-8 rounded-2xl">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">{{ __('messages.cv_generation_title') }}</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">{{ __('messages.cv_generation_description') }}</p>

                <!-- Service Level Options -->
                <div class="grid grid-cols-1 gap-4 mb-6">
                    <!-- Free Level -->
                    <div class="p-4 border-2 border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.basic_level') }}</span>
                            <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">{{ __('messages.free') }}</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('messages.basic_cv_desc') }}</p>
                        <a href="{{ route('cv.builder') }}" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-center font-medium transition-colors inline-block">
                            {{ __('messages.start_free') }}
                        </a>
                    </div>

                    <!-- Professional Level -->
                    <div class="p-4 border-2 border-blue-200 dark:border-blue-700 bg-blue-50 dark:bg-blue-900/20 rounded-lg relative">
                        <div class="absolute -top-2 left-4 bg-blue-500 text-white text-xs px-2 py-1 rounded">{{ __('messages.recommended') }}</div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.professional_level') }}</span>
                            <span class="text-lg font-bold text-blue-600 dark:text-blue-400">{{ __('messages.cv_pro_price') }}</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('messages.professional_cv_desc') }}</p>
                        <button onclick="redirectToPayment('cv', 'professional')"
                                class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-center font-medium transition-colors">
                            {{ __('messages.get_professional') }}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Interview Simulation -->
            <div class="card-hover bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-8 rounded-2xl">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">{{ __('messages.interview_simulation') }}</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">{{ __('messages.interview_description') }}</p>

                <!-- Service Level Options -->
                <div class="grid grid-cols-1 gap-4 mb-6">
                    <!-- Free Level -->
                    <div class="p-4 border-2 border-green-200 dark:border-green-700 bg-green-50 dark:bg-green-900/20 rounded-lg">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.basic_level') }}</span>
                            <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">{{ __('messages.free') }}</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('messages.basic_interview_desc') }}</p>
                        <a href="{{ route('services.interview-simulation') }}" class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-center font-medium transition-colors inline-block">
                            @if(app()->getLocale() === 'ar')
                                ابدأ التدريب التفاعلي
                            @elseif(app()->getLocale() === 'fr')
                                Commencer l'entraînement interactif
                            @else
                                Start Interactive Training
                            @endif
                        </a>
                    </div>

                    <!-- Professional Level -->
                    <div class="p-4 border-2 border-purple-200 dark:border-purple-700 bg-purple-50 dark:bg-purple-900/20 rounded-lg relative">
                        <div class="absolute -top-2 left-4 bg-purple-500 text-white text-xs px-2 py-1 rounded">{{ __('messages.recommended') }}</div>
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.professional_level') }}</span>
                            <span class="text-lg font-bold text-purple-600 dark:text-purple-400">{{ __('messages.interview_pro_price') }}</span>
                        </div>
                        <p class="text-sm text-gray-600 dark:text-gray-400 mb-3">{{ __('messages.professional_interview_desc') }}</p>
                        <button onclick="redirectToPayment('interview', 'professional')"
                                class="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg text-center font-medium transition-colors">
                            {{ __('messages.get_professional') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Payment Modal -->
<div id="paymentModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md w-full mx-4 transform transition-all">
        <div class="flex justify-between items-center mb-6">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ __('messages.payment_modal_title') }}</h3>
            <button onclick="closePaymentModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                </svg>
            </button>
        </div>

        <!-- Service Details -->
        <div class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.service_details') }}</h4>
            <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-300">{{ __('messages.selected_service') }}:</span>
                    <span id="modalServiceName" class="font-medium text-gray-900 dark:text-white"></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-300">{{ __('messages.selected_level') }}:</span>
                    <span id="modalLevelName" class="font-medium text-gray-900 dark:text-white"></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-300">{{ __('messages.price') }}:</span>
                    <span id="modalPrice" class="font-bold text-blue-600 dark:text-blue-400"></span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-600 dark:text-gray-300">{{ __('messages.duration') }}:</span>
                    <span id="modalDuration" class="text-gray-900 dark:text-white"></span>
                </div>
            </div>
            <p id="modalDescription" class="text-sm text-gray-600 dark:text-gray-400 mt-3"></p>
        </div>

        <!-- Payment Methods -->
        <div class="mb-6">
            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.payment_method') }}</h4>
            <div class="space-y-3">
                <button class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    <span class="text-gray-900 dark:text-white">{{ __('messages.pay_with_card') }}</span>
                </button>
                <button class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg hover:border-blue-500 dark:hover:border-blue-400 transition-colors flex items-center justify-center space-x-2">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-.635 4.005c-.08.52-.527.901-1.05.901z"/>
                    </svg>
                    <span class="text-gray-900 dark:text-white">{{ __('messages.pay_with_paypal') }}</span>
                </button>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="flex space-x-3">
            <button onclick="closePaymentModal()" class="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                {{ __('messages.cancel_payment') }}
            </button>
            <button onclick="processPayment()" class="flex-1 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg hover:from-blue-600 hover:to-purple-700 transition-colors">
                {{ __('messages.proceed_payment') }}
            </button>
        </div>
    </div>
</div>

<script>
let selectedService = {};

function openPaymentModal(serviceType, level, levelName, price, duration, description) {
    // Check if user is logged in
    @auth
        selectedService = {
            type: serviceType,
            level: level,
            name: getServiceName(serviceType),
            levelName: levelName,
            price: price,
            duration: duration,
            description: description
        };

        // Update modal content
        document.getElementById('modalServiceName').textContent = selectedService.name;
        document.getElementById('modalLevelName').textContent = levelName;
        document.getElementById('modalPrice').textContent = price;
        document.getElementById('modalDuration').textContent = duration;
        document.getElementById('modalDescription').textContent = description;

        // Show modal
        document.getElementById('paymentModal').classList.remove('hidden');
        document.body.style.overflow = 'hidden';
    @else
        // Redirect to login if not authenticated
        alert('{{ __('messages.login_to_continue') }}');
        window.location.href = '{{ route('login') }}';
    @endauth
}

function closePaymentModal() {
    document.getElementById('paymentModal').classList.add('hidden');
    document.body.style.overflow = 'auto';
}

function getServiceName(serviceType) {
    const serviceNames = {
        'personality': '{{ __('messages.personality_analysis_title') }}',
        'cv': '{{ __('messages.cv_generation_title') }}',
        'interview': '{{ __('messages.interview_simulation') }}'
    };
    return serviceNames[serviceType] || '';
}

function processPayment() {
    // Show loading state
    const button = event.target;
    const originalText = button.textContent;
    button.textContent = '{{ __('messages.processing_payment') }}';
    button.disabled = true;

    // Here you would integrate with your payment processor
    // For now, we'll simulate a payment process
    setTimeout(() => {
        alert('{{ __('messages.payment_success') }}');
        closePaymentModal();

        // Reset button
        button.textContent = originalText;
        button.disabled = false;

        // Redirect to service page or dashboard
        // window.location.href = '/dashboard';
    }, 2000);
}

// New function to redirect to dynamic payment page
function redirectToPayment(serviceType, level) {
    // Check if user is logged in
    @auth
        // Get service ID based on type
        const serviceIds = {
            'personality': 3, // Personal Career Coaching (closest to personality analysis)
            'cv': 1,          // Professional CV Creation
            'interview': 4    // Interview Training Session
        };

        const serviceId = serviceIds[serviceType];
        if (serviceId) {
            // Redirect to dynamic payment page
            const url = `{{ route('payment.service') }}?service_id=${serviceId}&service_type=${serviceType}&level=${level}`;
            window.location.href = url;
        } else {
            alert('{{ __('messages.service_not_found') }}');
        }
    @else
        // Redirect to login if not authenticated
        alert('{{ __('messages.login_to_continue') }}');
        window.location.href = '{{ route('login') }}';
    @endauth
}

// Close modal when clicking outside
document.getElementById('paymentModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closePaymentModal();
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closePaymentModal();
    }
});
</script>
@endsection
