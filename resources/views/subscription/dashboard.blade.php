@extends('layouts.app')

@section('title', __('messages.subscription_dashboard') . ' - MonOri AI')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">{{ __('messages.subscription_dashboard') }}</h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">{{ __('messages.manage_subscription_usage') }}</p>
        </div>

        @if($currentSubscription)
        <!-- Current Subscription -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-8">
            <div class="p-6">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-xl font-semibold text-gray-900 dark:text-white">{{ __('messages.your_current_subscription') }}</h2>
                    <span class="px-3 py-1 bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400 rounded-full text-sm font-medium">
                        {{ __('messages.active') }}
                    </span>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white">{{ $currentSubscription->subscriptionPlan->localized_name }}</h3>
                        <p class="text-gray-600 dark:text-gray-400 text-sm mt-1">{{ $currentSubscription->subscriptionPlan->localized_description }}</p>
                        <div class="mt-3">
                            <span class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($currentSubscription->amount_paid, 2) }}</span>
                            <span class="text-gray-600 dark:text-gray-400 ml-1">{{ __('messages.currency') }}</span>
                            <span class="text-sm text-gray-500 dark:text-gray-400">/{{ $currentSubscription->billing_cycle === 'yearly' ? __('messages.year') : __('messages.month') }}</span>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.subscription_dates') }}</h4>
                        <div class="space-y-1 text-sm">
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">{{ __('messages.subscription_start') }}:</span>
                                <span class="text-gray-900 dark:text-white">{{ $currentSubscription->starts_at->format('Y/m/d') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">{{ __('messages.subscription_end') }}:</span>
                                <span class="text-gray-900 dark:text-white">{{ $currentSubscription->ends_at->format('Y/m/d') }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600 dark:text-gray-400">{{ __('messages.days_remaining') }}:</span>
                                <span class="text-green-600 dark:text-green-400 font-medium">{{ $currentSubscription->days_remaining }} {{ __('messages.days') }}</span>
                            </div>
                        </div>
                    </div>

                    <div>
                        <h4 class="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">إجراءات</h4>
                        <div class="space-y-2">
                            <button class="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg text-sm transition-colors">
                                ترقية الخطة
                            </button>
                            <button class="w-full bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-lg text-sm transition-colors">
                                إلغاء الاشتراك
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Usage Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            @php
                $usage = $currentSubscription->usage_current ?? [];
                $limits = $currentSubscription->usage_limits ?? [];
                $percentages = $currentSubscription->usage_percentage ?? [];
            @endphp

            <!-- AI Requests -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">طلبات الذكاء الاصطناعي</h3>
                    <svg class="w-5 h-5 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">{{ $usage['ai_requests'] ?? 0 }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">من {{ $limits['ai_requests'] ?? 0 }}</div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-blue-600 h-2 rounded-full" style="width: {{ $percentages['ai_requests'] ?? 0 }}%"></div>
                </div>
            </div>

            <!-- CV Generations -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">إنشاء السير الذاتية</h3>
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">{{ $usage['cv_generations'] ?? 0 }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">من {{ $limits['cv_generations'] ?? 0 }}</div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-green-600 h-2 rounded-full" style="width: {{ $percentages['cv_generations'] ?? 0 }}%"></div>
                </div>
            </div>

            <!-- PDF Exports -->
            @if($limits['pdf_export'] ?? false)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">تصدير PDF</h3>
                    <svg class="w-5 h-5 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">{{ $usage['pdf_exports'] ?? 0 }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">غير محدود</div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-purple-600 h-2 rounded-full" style="width: 100%"></div>
                </div>
            </div>
            @endif

            <!-- Voice Interviews -->
            @if($limits['voice_interviews'] ?? false)
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-sm font-medium text-gray-700 dark:text-gray-300">المقابلات الصوتية</h3>
                    <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                    </svg>
                </div>
                <div class="text-2xl font-bold text-gray-900 dark:text-white mb-1">{{ $usage['voice_interviews'] ?? 0 }}</div>
                <div class="text-sm text-gray-600 dark:text-gray-400 mb-3">غير محدود</div>
                <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                    <div class="bg-orange-600 h-2 rounded-full" style="width: 100%"></div>
                </div>
            </div>
            @endif
        </div>

        @else
        <!-- No Subscription -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-8 text-center">
            <svg class="w-16 h-16 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
            </svg>
            <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">لا يوجد اشتراك نشط</h2>
            <p class="text-gray-600 dark:text-gray-400 mb-6">اشترك في إحدى خططنا للاستفادة من جميع ميزات MonOri AI</p>
            <a href="{{ route('pricing') }}" class="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                اختر خطة الاشتراك
            </a>
        </div>
        @endif

        <!-- Recent Orders -->
        @if($recentOrders && count($recentOrders) > 0)
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="p-6">
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-6">طلباتك الأخيرة</h2>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                        <thead class="bg-gray-50 dark:bg-gray-700">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الخدمة</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">المبلغ</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">الحالة</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">التاريخ</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                            @foreach($recentOrders as $order)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-gray-900 dark:text-white">{{ $order->paidService->name }}</div>
                                    <div class="text-sm text-gray-500 dark:text-gray-400">{{ $order->order_number }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ number_format($order->amount, 2) }} {{ __('messages.currency') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ $order->status_badge['class'] }}">
                                        {{ $order->status_badge['text'] }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                    {{ $order->created_at->format('Y/m/d') }}
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>
</div>
@endsection
