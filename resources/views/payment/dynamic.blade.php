@extends('layouts.app')

@section('title', __('messages.payment_page'))

@section('content')
<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Service Details Card -->
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50">
                    <h2 class="text-2xl font-bold text-white mb-6">{{ __('messages.service_details') }}</h2>
                    
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-{{ $service->icon ?? 'star' }} text-white text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white">{{ $service->name }}</h3>
                            <p class="text-gray-400">{{ $service->description }}</p>
                        </div>
                    </div>

                    <div class="space-y-4 mb-6">
                        <h4 class="text-lg font-semibold text-white">{{ __('messages.included_features') }}</h4>
                        @if($service->features)
                            @foreach(json_decode($service->features, true) as $feature)
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-3"></i>
                                    <span>{{ $feature }}</span>
                                </div>
                            @endforeach
                        @endif
                    </div>

                    <div class="border-t border-gray-700 pt-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-400">{{ __('messages.delivery_time') }}</span>
                            <span class="text-white">{{ $service->delivery_time ?? '24 hours' }}</span>
                        </div>
                        <div class="flex justify-between items-center text-2xl font-bold">
                            <span class="text-gray-400">{{ __('messages.total_amount') }}</span>
                            <span class="text-blue-400">{{ number_format($service->price, 2) }} MAD</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Information Card -->
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50">
                    <h2 class="text-2xl font-bold text-white mb-6">{{ __('messages.payment_information') }}</h2>
                    
                    <!-- Payment Method Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-300 mb-3">{{ __('messages.payment_method') }}</label>
                        <div class="space-y-3">
                            <label class="flex items-center p-4 bg-gray-700/50 rounded-xl cursor-pointer hover:bg-gray-700/70 transition-colors">
                                <input type="radio" name="payment_method" value="stripe" class="text-blue-500 focus:ring-blue-500" checked>
                                <div class="ml-3 flex items-center">
                                    <i class="fas fa-credit-card text-blue-400 mr-2"></i>
                                    <span class="text-white">{{ __('messages.pay_with_card') }}</span>
                                </div>
                            </label>
                            <label class="flex items-center p-4 bg-gray-700/50 rounded-xl cursor-pointer hover:bg-gray-700/70 transition-colors">
                                <input type="radio" name="payment_method" value="paypal" class="text-blue-500 focus:ring-blue-500">
                                <div class="ml-3 flex items-center">
                                    <i class="fab fa-paypal text-blue-400 mr-2"></i>
                                    <span class="text-white">{{ __('messages.pay_with_paypal') }}</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{{ __('messages.customer_information') }}</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.full_name') }}</label>
                                <input type="text" id="customer_name" value="{{ $user->name }}" 
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.email') }}</label>
                                <input type="email" id="customer_email" value="{{ $user->email }}" 
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- Card Payment Form -->
                    <div id="card-payment-form" class="payment-form">
                        <h3 class="text-lg font-semibold text-white mb-4">{{ __('messages.card_details') }}</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.card_number') }}</label>
                                <input type="text" id="card_number" placeholder="1234 5678 9012 3456" maxlength="19"
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.expiry_date') }}</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <select id="expiry_month" class="px-3 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            @for($i = 1; $i <= 12; $i++)
                                                <option value="{{ $i }}">{{ sprintf('%02d', $i) }}</option>
                                            @endfor
                                        </select>
                                        <select id="expiry_year" class="px-3 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            @for($i = date('Y'); $i <= date('Y') + 10; $i++)
                                                <option value="{{ $i }}">{{ $i }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.cvv') }}</label>
                                    <input type="text" id="cvv" placeholder="123" maxlength="4"
                                           class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.cardholder_name') }}</label>
                                <input type="text" id="cardholder_name" placeholder="{{ __('messages.name_on_card') }}"
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- PayPal Payment Form -->
                    <div id="paypal-payment-form" class="payment-form hidden">
                        <h3 class="text-lg font-semibold text-white mb-4">{{ __('messages.paypal_details') }}</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.paypal_email') }}</label>
                                <input type="email" id="paypal_email" placeholder="<EMAIL>"
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mt-6">
                        <label class="flex items-start">
                            <input type="checkbox" id="terms_accepted" class="mt-1 text-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-300">
                                {{ __('messages.i_agree_to_the') }} 
                                <a href="#" class="text-blue-400 hover:text-blue-300">{{ __('messages.terms_and_conditions') }}</a> 
                                {{ __('messages.and') }} 
                                <a href="#" class="text-blue-400 hover:text-blue-300">{{ __('messages.privacy_policy') }}</a>
                            </span>
                        </label>
                    </div>

                    <!-- Payment Button -->
                    <button id="pay-now-btn" class="w-full mt-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="btn-text">{{ __('messages.pay_now') }} {{ number_format($service->price, 2) }} MAD</span>
                        <span id="btn-loading" class="hidden">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            {{ __('messages.processing') }}...
                        </span>
                    </button>

                    <!-- Security Notice -->
                    <div class="mt-4 flex items-center justify-center text-sm text-gray-400">
                        <i class="fas fa-lock mr-2 text-green-400"></i>
                        {{ __('messages.all_payments_secured') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-8 max-w-md mx-4">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">{{ __('messages.payment_successful') }}</h3>
            <p class="text-gray-400 mb-6">{{ __('messages.payment_success_message') }}</p>
            <button onclick="window.location.href='{{ route('dashboard') }}'" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                {{ __('messages.go_to_dashboard') }}
            </button>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div id="error-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-8 max-w-md mx-4">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-times text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">{{ __('messages.payment_failed') }}</h3>
            <p id="error-message" class="text-gray-400 mb-6"></p>
            <button onclick="hideModal('error-modal')" 
                    class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors">
                {{ __('messages.try_again') }}
            </button>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const cardForm = document.getElementById('card-payment-form');
    const paypalForm = document.getElementById('paypal-payment-form');
    const payButton = document.getElementById('pay-now-btn');
    const cardNumberInput = document.getElementById('card_number');
    
    // Payment method switching
    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            if (this.value === 'stripe') {
                cardForm.classList.remove('hidden');
                paypalForm.classList.add('hidden');
            } else {
                cardForm.classList.add('hidden');
                paypalForm.classList.remove('hidden');
            }
        });
    });
    
    // Card number formatting
    cardNumberInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');
        value = value.replace(/(\d{4})(?=\d)/g, '$1 ');
        this.value = value;
    });
    
    // CVV input restriction
    document.getElementById('cvv').addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '');
    });
    
    // Payment processing
    payButton.addEventListener('click', function() {
        processPayment();
    });
    
    function processPayment() {
        const paymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
        const customerName = document.getElementById('customer_name').value;
        const customerEmail = document.getElementById('customer_email').value;
        const termsAccepted = document.getElementById('terms_accepted').checked;
        
        // Validation
        if (!customerName || !customerEmail) {
            showError('{{ __("messages.please_fill_required_fields") }}');
            return;
        }
        
        if (!termsAccepted) {
            showError('{{ __("messages.please_accept_terms") }}');
            return;
        }
        
        // Show loading
        showLoading(true);
        
        // Create transaction first
        fetch('{{ route("payment.process", $service) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                payment_method: paymentMethod,
                customer_name: customerName,
                customer_email: customerEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Process payment based on method
                if (paymentMethod === 'stripe') {
                    processStripePayment(data.transaction_id);
                } else {
                    processPayPalPayment(data.transaction_id);
                }
            } else {
                showError(data.message || '{{ __("messages.transaction_failed") }}');
                showLoading(false);
            }
        })
        .catch(error => {
            showError('{{ __("messages.network_error") }}');
            showLoading(false);
        });
    }
    
    function processStripePayment(transactionId) {
        const cardNumber = document.getElementById('card_number').value.replace(/\s/g, '');
        const expiryMonth = document.getElementById('expiry_month').value;
        const expiryYear = document.getElementById('expiry_year').value;
        const cvv = document.getElementById('cvv').value;
        const cardholderName = document.getElementById('cardholder_name').value;
        
        if (!cardNumber || !cvv || !cardholderName) {
            showError('{{ __("messages.please_fill_card_details") }}');
            showLoading(false);
            return;
        }
        
        fetch(`/payment/stripe/${transactionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                card_number: cardNumber,
                expiry_month: expiryMonth,
                expiry_year: expiryYear,
                cvv: cvv,
                cardholder_name: cardholderName
            })
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                showSuccess();
            } else {
                showError(data.message || '{{ __("messages.payment_failed") }}');
            }
        })
        .catch(error => {
            showLoading(false);
            showError('{{ __("messages.payment_error") }}');
        });
    }
    
    function processPayPalPayment(transactionId) {
        const paypalEmail = document.getElementById('paypal_email').value;
        
        if (!paypalEmail) {
            showError('{{ __("messages.please_enter_paypal_email") }}');
            showLoading(false);
            return;
        }
        
        fetch(`/payment/paypal/${transactionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                paypal_email: paypalEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                showSuccess();
            } else {
                showError(data.message || '{{ __("messages.payment_failed") }}');
            }
        })
        .catch(error => {
            showLoading(false);
            showError('{{ __("messages.payment_error") }}');
        });
    }
    
    function showLoading(show) {
        const btnText = document.getElementById('btn-text');
        const btnLoading = document.getElementById('btn-loading');
        const payButton = document.getElementById('pay-now-btn');
        
        if (show) {
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            payButton.disabled = true;
        } else {
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
            payButton.disabled = false;
        }
    }
    
    function showSuccess() {
        document.getElementById('success-modal').classList.remove('hidden');
        document.getElementById('success-modal').classList.add('flex');
    }
    
    function showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
        document.getElementById('error-modal').classList.add('flex');
    }
});

function hideModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.getElementById(modalId).classList.remove('flex');
}
</script>
@endsection
