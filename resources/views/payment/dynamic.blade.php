@extends('layouts.app')

@section('title', __('messages.payment_page'))

@section('styles')
<style>
    .payment-form {
        transition: all 0.3s ease;
        opacity: 1;
        transform: translateY(0);
    }

    .payment-form.hidden {
        display: none;
    }

    .payment-method-option {
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
    }

    .payment-method-option::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
        transition: left 0.5s;
    }

    .payment-method-option:hover::before {
        left: 100%;
    }

    .payment-method-option:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15);
    }

    #pay-now-btn {
        position: relative;
        overflow: hidden;
    }

    #pay-now-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
        transition: left 0.5s;
    }

    #pay-now-btn:hover::before {
        left: 100%;
    }

    .security-notice {
        animation: pulse 2s infinite;
    }

    @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
    }
</style>
@endsection

@section('content')
<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                
                <!-- Service Details Card -->
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50">
                    <h2 class="text-2xl font-bold text-white mb-6">{{ __('messages.service_details') }}</h2>
                    
                    <div class="flex items-center mb-6">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mr-4">
                            <i class="fas fa-{{ $service->icon ?? 'star' }} text-white text-2xl"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-semibold text-white">{{ $service->name }}</h3>
                            <p class="text-gray-400">{{ $service->description }}</p>
                        </div>
                    </div>

                    <div class="space-y-4 mb-6">
                        <h4 class="text-lg font-semibold text-white">{{ __('messages.included_features') }}</h4>
                        @if($service->features)
                            @foreach(json_decode($service->features, true) as $feature)
                                <div class="flex items-center text-gray-300">
                                    <i class="fas fa-check text-green-400 mr-3"></i>
                                    <span>{{ $feature }}</span>
                                </div>
                            @endforeach
                        @endif
                    </div>

                    <div class="border-t border-gray-700 pt-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-400">{{ __('messages.delivery_time') }}</span>
                            <span class="text-white">{{ $service->delivery_time ?? '24 hours' }}</span>
                        </div>
                        <div class="flex justify-between items-center text-2xl font-bold">
                            <span class="text-gray-400">{{ __('messages.total_amount') }}</span>
                            <span class="text-blue-400">{{ number_format($service->price, 2) }} MAD</span>
                        </div>
                    </div>
                </div>

                <!-- Payment Information Card -->
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50">
                    <h2 class="text-2xl font-bold text-white mb-6">{{ __('messages.payment_information') }}</h2>
                    
                    <!-- Payment Method Selection -->
                    <div class="mb-6">
                        <label class="block text-sm font-medium text-gray-300 mb-3">{{ __('messages.payment_method') }}</label>
                        <div class="space-y-3">
                            <label class="payment-method-option flex items-center p-4 bg-gray-700/50 rounded-xl cursor-pointer hover:bg-gray-700/70 transition-all duration-300 border-2 border-transparent" data-method="stripe">
                                <input type="radio" name="payment_method" value="stripe" class="text-blue-500 focus:ring-blue-500" checked>
                                <div class="ml-3 flex items-center justify-between w-full">
                                    <div class="flex items-center">
                                        <i class="fas fa-credit-card text-blue-400 mr-3 text-lg"></i>
                                        <div>
                                            <span class="text-white font-medium">{{ __('messages.pay_with_card') }}</span>
                                            <div class="text-gray-400 text-sm">Visa, Mastercard, Amex</div>
                                        </div>
                                    </div>
                                    <div class="flex space-x-2">
                                        <i class="fab fa-cc-visa text-blue-500 text-xl"></i>
                                        <i class="fab fa-cc-mastercard text-red-500 text-xl"></i>
                                        <i class="fab fa-cc-amex text-blue-600 text-xl"></i>
                                    </div>
                                </div>
                            </label>
                            <label class="payment-method-option flex items-center p-4 bg-gray-700/50 rounded-xl cursor-pointer hover:bg-gray-700/70 transition-all duration-300 border-2 border-transparent" data-method="paypal">
                                <input type="radio" name="payment_method" value="paypal" class="text-blue-500 focus:ring-blue-500">
                                <div class="ml-3 flex items-center justify-between w-full">
                                    <div class="flex items-center">
                                        <i class="fab fa-paypal text-blue-400 mr-3 text-lg"></i>
                                        <div>
                                            <span class="text-white font-medium">{{ __('messages.pay_with_paypal') }}</span>
                                            <div class="text-gray-400 text-sm">{{ __('messages.secure_paypal_payment') }}</div>
                                        </div>
                                    </div>
                                    <div class="text-blue-500 font-bold text-xl">PayPal</div>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{{ __('messages.customer_information') }}</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.full_name') }}</label>
                                <input type="text" id="customer_name" value="{{ $user->name }}" 
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.email') }}</label>
                                <input type="email" id="customer_email" value="{{ $user->email }}" 
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- Card Payment Form -->
                    <div id="card-payment-form" class="payment-form">
                        <h3 class="text-lg font-semibold text-white mb-4">{{ __('messages.card_details') }}</h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.card_number') }}</label>
                                <div class="relative">
                                    <input type="text" id="card_number" placeholder="1234 5678 9012 3456" maxlength="19"
                                           class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12">
                                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-credit-card text-gray-400"></i>
                                    </div>
                                </div>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.expiry_date') }}</label>
                                    <div class="grid grid-cols-2 gap-2">
                                        <select id="expiry_month" class="px-3 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            @for($i = 1; $i <= 12; $i++)
                                                <option value="{{ $i }}">{{ sprintf('%02d', $i) }}</option>
                                            @endfor
                                        </select>
                                        <select id="expiry_year" class="px-3 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                                            @for($i = date('Y'); $i <= date('Y') + 10; $i++)
                                                <option value="{{ $i }}">{{ $i }}</option>
                                            @endfor
                                        </select>
                                    </div>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.cvv') }}</label>
                                    <div class="relative">
                                        <input type="text" id="cvv" placeholder="123" maxlength="4"
                                               class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12">
                                        <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                            <i class="fas fa-lock text-gray-400"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.cardholder_name') }}</label>
                                <input type="text" id="cardholder_name" placeholder="{{ __('messages.name_on_card') }}"
                                       class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                            </div>
                        </div>
                    </div>

                    <!-- PayPal Payment Form -->
                    <div id="paypal-payment-form" class="payment-form hidden">
                        <h3 class="text-lg font-semibold text-white mb-4 flex items-center">
                            <i class="fab fa-paypal text-blue-400 mr-2"></i>
                            {{ __('messages.paypal_details') }}
                        </h3>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.paypal_email') }}</label>
                                <div class="relative">
                                    <input type="email" id="paypal_email" placeholder="<EMAIL>"
                                           class="w-full px-4 py-3 bg-gray-700/50 border border-gray-600 rounded-xl text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent pr-12">
                                    <div class="absolute right-3 top-1/2 transform -translate-y-1/2">
                                        <i class="fas fa-envelope text-gray-400"></i>
                                    </div>
                                </div>
                                <p class="text-gray-400 text-sm mt-2">{{ __('messages.paypal_email_note') }}</p>
                            </div>
                            <div class="bg-blue-500/10 border border-blue-500/20 rounded-lg p-4">
                                <div class="flex items-start">
                                    <i class="fas fa-info-circle text-blue-400 mr-2 mt-0.5"></i>
                                    <div class="text-sm text-blue-300">
                                        {{ __('messages.paypal_redirect_note') }}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="mt-6">
                        <label class="flex items-start">
                            <input type="checkbox" id="terms_accepted" class="mt-1 text-blue-500 focus:ring-blue-500">
                            <span class="ml-2 text-sm text-gray-300">
                                {{ __('messages.i_agree_to_the') }} 
                                <a href="#" class="text-blue-400 hover:text-blue-300">{{ __('messages.terms_and_conditions') }}</a> 
                                {{ __('messages.and') }} 
                                <a href="#" class="text-blue-400 hover:text-blue-300">{{ __('messages.privacy_policy') }}</a>
                            </span>
                        </label>
                    </div>

                    <!-- Payment Button -->
                    <button id="pay-now-btn" class="w-full mt-6 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-4 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                        <span id="btn-text">{{ __('messages.pay_now') }} {{ number_format($service->price, 2) }} MAD</span>
                        <span id="btn-loading" class="hidden">
                            <i class="fas fa-spinner fa-spin mr-2"></i>
                            {{ __('messages.processing') }}...
                        </span>
                    </button>

                    <!-- Security Notice -->
                    <div class="mt-4 flex items-center justify-center text-sm text-gray-400 security-notice">
                        <i class="fas fa-shield-alt mr-2 text-green-400"></i>
                        {{ __('messages.all_payments_secured') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Success Modal -->
<div id="success-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-8 max-w-md mx-4">
        <div class="text-center">
            <div class="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-check text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">{{ __('messages.payment_successful') }}</h3>
            <p class="text-gray-400 mb-6">{{ __('messages.payment_success_message') }}</p>
            <button onclick="window.location.href='{{ route('dashboard') }}'" 
                    class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors">
                {{ __('messages.go_to_dashboard') }}
            </button>
        </div>
    </div>
</div>

<!-- Error Modal -->
<div id="error-modal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-gray-800 rounded-2xl p-8 max-w-md mx-4">
        <div class="text-center">
            <div class="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <i class="fas fa-times text-white text-2xl"></i>
            </div>
            <h3 class="text-xl font-bold text-white mb-2">{{ __('messages.payment_failed') }}</h3>
            <p id="error-message" class="text-gray-400 mb-6"></p>
            <button onclick="hideModal('error-modal')" 
                    class="bg-red-500 hover:bg-red-600 text-white px-6 py-2 rounded-lg transition-colors">
                {{ __('messages.try_again') }}
            </button>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const paymentMethodRadios = document.querySelectorAll('input[name="payment_method"]');
    const cardForm = document.getElementById('card-payment-form');
    const paypalForm = document.getElementById('paypal-payment-form');
    const payButton = document.getElementById('pay-now-btn');
    const cardNumberInput = document.getElementById('card_number');
    
    // Payment method switching with animations
    function updatePaymentMethodUI() {
        const selectedMethod = document.querySelector('input[name="payment_method"]:checked').value;
        const paymentOptions = document.querySelectorAll('.payment-method-option');

        // Update option styling
        paymentOptions.forEach(option => {
            const input = option.querySelector('input[type="radio"]');
            if (input.checked) {
                option.classList.add('border-blue-500', 'bg-blue-500/10');
                option.classList.remove('border-transparent');
            } else {
                option.classList.remove('border-blue-500', 'bg-blue-500/10');
                option.classList.add('border-transparent');
            }
        });

        // Show/hide payment forms with smooth transition
        if (selectedMethod === 'stripe') {
            paypalForm.style.opacity = '0';
            paypalForm.style.transform = 'translateY(-10px)';

            setTimeout(() => {
                paypalForm.classList.add('hidden');
                cardForm.classList.remove('hidden');
                cardForm.style.opacity = '0';
                cardForm.style.transform = 'translateY(10px)';

                setTimeout(() => {
                    cardForm.style.transition = 'all 0.3s ease';
                    cardForm.style.opacity = '1';
                    cardForm.style.transform = 'translateY(0)';
                }, 50);
            }, 150);
        } else {
            cardForm.style.opacity = '0';
            cardForm.style.transform = 'translateY(-10px)';

            setTimeout(() => {
                cardForm.classList.add('hidden');
                paypalForm.classList.remove('hidden');
                paypalForm.style.opacity = '0';
                paypalForm.style.transform = 'translateY(10px)';

                setTimeout(() => {
                    paypalForm.style.transition = 'all 0.3s ease';
                    paypalForm.style.opacity = '1';
                    paypalForm.style.transform = 'translateY(0)';
                }, 50);
            }, 150);
        }
    }

    paymentMethodRadios.forEach(radio => {
        radio.addEventListener('change', updatePaymentMethodUI);
    });

    // Initialize on page load
    updatePaymentMethodUI();
    
    // Card number formatting with card type detection
    cardNumberInput.addEventListener('input', function() {
        let value = this.value.replace(/\D/g, '');

        // Detect card type and apply appropriate formatting
        let formattedValue = '';
        if (value.startsWith('4')) {
            // Visa: 4xxx xxxx xxxx xxxx
            formattedValue = value.replace(/(\d{4})(?=\d)/g, '$1 ');
        } else if (value.startsWith('5') || value.startsWith('2')) {
            // Mastercard: 5xxx xxxx xxxx xxxx
            formattedValue = value.replace(/(\d{4})(?=\d)/g, '$1 ');
        } else if (value.startsWith('3')) {
            // Amex: 3xxx xxxxxx xxxxx
            formattedValue = value.replace(/(\d{4})(\d{6})(\d{5})/, '$1 $2 $3');
        } else {
            // Default formatting
            formattedValue = value.replace(/(\d{4})(?=\d)/g, '$1 ');
        }

        this.value = formattedValue;

        // Update card icon based on card type
        updateCardIcon(value);
    });

    function updateCardIcon(cardNumber) {
        const cardIconContainer = document.querySelector('#card_number').parentElement.querySelector('.absolute i');
        if (!cardIconContainer) return;

        if (cardNumber.startsWith('4')) {
            cardIconContainer.className = 'fab fa-cc-visa text-blue-500 text-xl';
        } else if (cardNumber.startsWith('5') || cardNumber.startsWith('2')) {
            cardIconContainer.className = 'fab fa-cc-mastercard text-red-500 text-xl';
        } else if (cardNumber.startsWith('34') || cardNumber.startsWith('37')) {
            cardIconContainer.className = 'fab fa-cc-amex text-blue-600 text-xl';
        } else if (cardNumber.startsWith('6011') || cardNumber.startsWith('65')) {
            cardIconContainer.className = 'fab fa-cc-discover text-orange-500 text-xl';
        } else if (cardNumber.length > 0) {
            cardIconContainer.className = 'fas fa-credit-card text-blue-400 text-lg';
        } else {
            cardIconContainer.className = 'fas fa-credit-card text-gray-400 text-lg';
        }
    }

    // Luhn algorithm for card number validation
    function isValidCardNumber(cardNumber) {
        const digits = cardNumber.replace(/\D/g, '');
        let sum = 0;
        let isEven = false;

        for (let i = digits.length - 1; i >= 0; i--) {
            let digit = parseInt(digits.charAt(i));

            if (isEven) {
                digit *= 2;
                if (digit > 9) {
                    digit -= 9;
                }
            }

            sum += digit;
            isEven = !isEven;
        }

        return (sum % 10) === 0;
    }
    
    // CVV input restriction
    document.getElementById('cvv').addEventListener('input', function() {
        this.value = this.value.replace(/\D/g, '');
    });
    
    // Payment processing
    payButton.addEventListener('click', function() {
        processPayment();
    });
    
    function processPayment() {
        const paymentMethod = document.querySelector('input[name="payment_method"]:checked').value;
        const customerName = document.getElementById('customer_name').value;
        const customerEmail = document.getElementById('customer_email').value;
        const termsAccepted = document.getElementById('terms_accepted').checked;
        
        // Validation
        if (!customerName || !customerEmail) {
            showError('{{ __("messages.please_fill_required_fields") }}');
            return;
        }
        
        if (!termsAccepted) {
            showError('{{ __("messages.please_accept_terms") }}');
            return;
        }
        
        // Show loading
        showLoading(true);
        
        // Create transaction first
        fetch('{{ route("payment.process", $service) }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                payment_method: paymentMethod,
                customer_name: customerName,
                customer_email: customerEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Process payment based on method
                if (paymentMethod === 'stripe') {
                    processStripePayment(data.transaction_id);
                } else {
                    processPayPalPayment(data.transaction_id);
                }
            } else {
                showError(data.message || '{{ __("messages.transaction_failed") }}');
                showLoading(false);
            }
        })
        .catch(error => {
            showError('{{ __("messages.network_error") }}');
            showLoading(false);
        });
    }
    
    function processStripePayment(transactionId) {
        const cardNumber = document.getElementById('card_number').value.replace(/\s/g, '');
        const expiryMonth = document.getElementById('expiry_month').value;
        const expiryYear = document.getElementById('expiry_year').value;
        const cvv = document.getElementById('cvv').value;
        const cardholderName = document.getElementById('cardholder_name').value;
        
        // Enhanced card validation
        if (!cardNumber || cardNumber.length < 13) {
            showError('{{ __("messages.invalid_card_number") }}');
            showLoading(false);
            return;
        }

        if (!cvv || cvv.length < 3) {
            showError('{{ __("messages.invalid_cvv") }}');
            showLoading(false);
            return;
        }

        if (!cardholderName || cardholderName.trim().length < 2) {
            showError('{{ __("messages.invalid_cardholder_name") }}');
            showLoading(false);
            return;
        }

        // Basic Luhn algorithm check for card number
        if (!isValidCardNumber(cardNumber)) {
            showError('{{ __("messages.invalid_card_number") }}');
            showLoading(false);
            return;
        }
        
        fetch(`/payment/stripe/${transactionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                card_number: cardNumber,
                expiry_month: expiryMonth,
                expiry_year: expiryYear,
                cvv: cvv,
                cardholder_name: cardholderName
            })
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                showSuccess();
            } else {
                showError(data.message || '{{ __("messages.payment_failed") }}');
            }
        })
        .catch(error => {
            showLoading(false);
            showError('{{ __("messages.payment_error") }}');
        });
    }
    
    function processPayPalPayment(transactionId) {
        const paypalEmail = document.getElementById('paypal_email').value;
        
        if (!paypalEmail) {
            showError('{{ __("messages.please_enter_paypal_email") }}');
            showLoading(false);
            return;
        }
        
        fetch(`/payment/paypal/${transactionId}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': '{{ csrf_token() }}'
            },
            body: JSON.stringify({
                paypal_email: paypalEmail
            })
        })
        .then(response => response.json())
        .then(data => {
            showLoading(false);
            if (data.success) {
                showSuccess();
            } else {
                showError(data.message || '{{ __("messages.payment_failed") }}');
            }
        })
        .catch(error => {
            showLoading(false);
            showError('{{ __("messages.payment_error") }}');
        });
    }
    
    function showLoading(show) {
        const btnText = document.getElementById('btn-text');
        const btnLoading = document.getElementById('btn-loading');
        const payButton = document.getElementById('pay-now-btn');
        
        if (show) {
            btnText.classList.add('hidden');
            btnLoading.classList.remove('hidden');
            payButton.disabled = true;
        } else {
            btnText.classList.remove('hidden');
            btnLoading.classList.add('hidden');
            payButton.disabled = false;
        }
    }
    
    function showSuccess() {
        document.getElementById('success-modal').classList.remove('hidden');
        document.getElementById('success-modal').classList.add('flex');
    }
    
    function showError(message) {
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-modal').classList.remove('hidden');
        document.getElementById('error-modal').classList.add('flex');
    }
});

function hideModal(modalId) {
    document.getElementById(modalId).classList.add('hidden');
    document.getElementById(modalId).classList.remove('flex');
}
</script>
@endsection
