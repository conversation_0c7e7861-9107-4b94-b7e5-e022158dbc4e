@extends('layouts.app')

@section('title', __('messages.payment_failed'))

@section('content')
<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 flex items-center justify-center py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-2xl mx-auto">
            <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-8 border border-gray-700/50 text-center">
                
                <!-- Error Icon -->
                <div class="w-24 h-24 bg-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                    <i class="fas fa-times text-white text-4xl"></i>
                </div>
                
                <!-- Error Message -->
                <h1 class="text-3xl font-bold text-white mb-4">{{ __('messages.payment_failed') }}</h1>
                <p class="text-gray-300 text-lg mb-8">{{ __('messages.payment_failed_message') }}</p>
                
                <!-- Transaction Details -->
                <div class="bg-gray-700/50 rounded-xl p-6 mb-8">
                    <h2 class="text-xl font-semibold text-white mb-4">{{ __('messages.transaction_details') }}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                        <div>
                            <span class="text-gray-400">{{ __('messages.transaction_id') }}:</span>
                            <span class="text-white font-mono">{{ $transaction->transaction_id }}</span>
                        </div>
                        <div>
                            <span class="text-gray-400">{{ __('messages.amount') }}:</span>
                            <span class="text-white font-semibold">{{ $transaction->formatted_amount }}</span>
                        </div>
                        <div>
                            <span class="text-gray-400">{{ __('messages.payment_method') }}:</span>
                            <span class="text-white capitalize">
                                @if($transaction->payment_method === 'stripe')
                                    <i class="fas fa-credit-card mr-1"></i>{{ __('messages.credit_card') }}
                                @else
                                    <i class="fab fa-paypal mr-1"></i>PayPal
                                @endif
                            </span>
                        </div>
                        <div>
                            <span class="text-gray-400">{{ __('messages.failed_at') }}:</span>
                            <span class="text-white">{{ $transaction->failed_at->format('d/m/Y H:i') }}</span>
                        </div>
                        <div class="md:col-span-2">
                            <span class="text-gray-400">{{ __('messages.service') }}:</span>
                            <span class="text-white">{{ $transaction->paidService->name }}</span>
                        </div>
                        @if($transaction->failure_reason)
                        <div class="md:col-span-2">
                            <span class="text-gray-400">{{ __('messages.failure_reason') }}:</span>
                            <span class="text-red-400">{{ $transaction->failure_reason }}</span>
                        </div>
                        @endif
                    </div>
                </div>
                
                <!-- Error Information -->
                <div class="bg-red-500/20 border border-red-500/30 rounded-xl p-6 mb-8">
                    <div class="flex items-center justify-center mb-4">
                        <i class="fas fa-exclamation-triangle text-red-400 text-2xl mr-3"></i>
                        <h3 class="text-lg font-semibold text-white">{{ __('messages.what_went_wrong') }}</h3>
                    </div>
                    <p class="text-gray-300 mb-4">{{ __('messages.payment_failure_explanation') }}</p>
                    <div class="text-sm text-gray-400">
                        <p>{{ __('messages.common_failure_reasons') }}:</p>
                        <ul class="list-disc list-inside mt-2 space-y-1">
                            <li>{{ __('messages.insufficient_funds') }}</li>
                            <li>{{ __('messages.card_declined') }}</li>
                            <li>{{ __('messages.expired_card') }}</li>
                            <li>{{ __('messages.incorrect_details') }}</li>
                        </ul>
                    </div>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('payment.show', $transaction->paidService) }}" 
                       class="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-redo mr-2"></i>
                        {{ __('messages.try_again') }}
                    </a>
                    
                    <a href="{{ route('services.index') }}" 
                       class="bg-gray-700 hover:bg-gray-600 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300">
                        <i class="fas fa-arrow-left mr-2"></i>
                        {{ __('messages.back_to_services') }}
                    </a>
                </div>
                
                <!-- Support Information -->
                <div class="mt-8 pt-6 border-t border-gray-700">
                    <p class="text-gray-400 mb-4">{{ __('messages.need_help') }}</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="{{ route('contact') }}" 
                           class="text-blue-400 hover:text-blue-300 transition-colors">
                            <i class="fas fa-envelope mr-2"></i>
                            {{ __('messages.contact_support') }}
                        </a>
                        <a href="tel:+212123456789" 
                           class="text-blue-400 hover:text-blue-300 transition-colors">
                            <i class="fas fa-phone mr-2"></i>
                            {{ __('messages.call_support') }}
                        </a>
                    </div>
                </div>
                
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
// Auto-redirect to services after 60 seconds
setTimeout(() => {
    if (confirm('{{ __("messages.redirect_to_services_confirm") }}')) {
        window.location.href = '{{ route("services.index") }}';
    }
}, 60000);
</script>
@endsection
