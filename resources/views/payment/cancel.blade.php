@extends('layouts.app')

@section('title', 'تم إلغاء الدفع')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-2xl mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 text-center">
            <!-- Cancel Icon -->
            <div class="w-20 h-20 bg-yellow-100 dark:bg-yellow-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
                <svg class="w-10 h-10 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>

            <!-- Cancel Message -->
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">تم إلغاء الدفع</h1>
            <p class="text-xl text-gray-600 dark:text-gray-400 mb-8">لم يتم إتمام عملية الدفع</p>

            <!-- Information -->
            <div class="bg-yellow-50 dark:bg-yellow-900/20 rounded-xl p-6 mb-8">
                <h3 class="font-bold text-yellow-900 dark:text-yellow-100 mb-4">ماذا حدث؟</h3>
                <div class="text-yellow-800 dark:text-yellow-200 space-y-2 text-right">
                    <p>• تم إلغاء عملية الدفع من قبلك</p>
                    <p>• لم يتم خصم أي مبلغ من حسابك</p>
                    <p>• يمكنك المحاولة مرة أخرى في أي وقت</p>
                </div>
            </div>

            <!-- Reasons -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mb-8 text-right">
                <h3 class="font-bold text-gray-900 dark:text-white mb-4 text-center">أسباب شائعة لإلغاء الدفع</h3>
                <div class="space-y-3 text-gray-600 dark:text-gray-400">
                    <div class="flex items-start">
                        <span class="text-red-500 mr-2">•</span>
                        <span>تغيير الرأي حول الشراء</span>
                    </div>
                    <div class="flex items-start">
                        <span class="text-red-500 mr-2">•</span>
                        <span>مشاكل في معلومات الدفع</span>
                    </div>
                    <div class="flex items-start">
                        <span class="text-red-500 mr-2">•</span>
                        <span>الحاجة لمراجعة التفاصيل مرة أخرى</span>
                    </div>
                    <div class="flex items-start">
                        <span class="text-red-500 mr-2">•</span>
                        <span>مشاكل تقنية في المتصفح</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center mb-8">
                <a href="{{ route('pricing') }}" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    المحاولة مرة أخرى
                </a>
                
                <a href="{{ route('home') }}" class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    العودة للرئيسية
                </a>
            </div>

            <!-- Help Section -->
            <div class="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6">
                <h3 class="font-bold text-blue-900 dark:text-blue-100 mb-4">تحتاج مساعدة؟</h3>
                <div class="text-blue-800 dark:text-blue-200 space-y-2">
                    <p>إذا كنت تواجه مشاكل في الدفع، يمكننا مساعدتك:</p>
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mt-4">
                        <a href="{{ route('contact') }}" class="text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                            تواصل معنا
                        </a>
                        
                        <a href="tel:+212123456789" class="text-blue-600 dark:text-blue-400 hover:underline flex items-center justify-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
                            </svg>
                            اتصل بنا
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
