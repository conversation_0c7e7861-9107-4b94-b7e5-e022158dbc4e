@extends('layouts.app')

@section('title', __('messages.payment_successful') . ' - MonOri AI')
@section('description', __('messages.payment_completed_successfully'))

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Success Header -->
        <div class="text-center mb-8">
            <div class="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                <svg class="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            </div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.payment_successful') }}</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ __('messages.payment_completed_successfully') }}</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Order Details -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.order_details') }}</h2>
                
                <div class="space-y-4">
                    <!-- Order Number -->
                    <div class="flex justify-between items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <span class="text-gray-600 dark:text-gray-400">{{ __('messages.order_number') }}</span>
                        <span class="font-mono font-bold text-blue-600 dark:text-blue-400">{{ $order->order_number ?? 'ORD-' . strtoupper(Str::random(8)) }}</span>
                    </div>

                    <!-- Service Details -->
                    <div class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center mr-3">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">
                                    {{ $order->paidService->name ?? __('messages.professional_service') }}
                                </h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ __('messages.professional_level') }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Payment Information -->
                    <div class="space-y-3">
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">{{ __('messages.amount_paid') }}</span>
                            <span class="font-semibold text-gray-900 dark:text-white">
                                {{ number_format($order->amount ?? 79, 2) }} {{ $order->currency ?? 'MAD' }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">{{ __('messages.payment_date') }}</span>
                            <span class="text-gray-900 dark:text-white">
                                {{ ($order->paid_at ?? now())->format('Y-m-d H:i') }}
                            </span>
                        </div>
                        <div class="flex justify-between">
                            <span class="text-gray-600 dark:text-gray-400">{{ __('messages.status') }}</span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                                {{ __('messages.paid') }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Next Steps -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.what_happens_next') }}</h2>
                
                <div class="space-y-6">
                    <!-- Timeline -->
                    <div class="space-y-4">
                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-4 mt-1">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ __('messages.payment_confirmed') }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('messages.payment_confirmed_desc') }}</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-4 mt-1">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ __('messages.processing_started') }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">{{ __('messages.processing_started_desc') }}</p>
                            </div>
                        </div>

                        <div class="flex items-start">
                            <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center mr-4 mt-1">
                                <span class="text-xs font-bold text-white">3</span>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 dark:text-white">{{ __('messages.delivery_expected') }}</h3>
                                <p class="text-sm text-gray-600 dark:text-gray-400">
                                    {{ __('messages.delivery_expected_desc') }}
                                    <span class="font-medium">{{ ($order->due_date ?? now()->addHours(2))->format('Y-m-d H:i') }}</span>
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6 space-y-3">
                        <a href="{{ route('dashboard') }}" class="w-full bg-blue-500 hover:bg-blue-600 text-white py-3 px-4 rounded-lg text-center font-medium transition-colors inline-block">
                            {{ __('messages.go_to_dashboard') }}
                        </a>
                        <a href="{{ route('services') }}" class="w-full border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 py-3 px-4 rounded-lg text-center font-medium hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors inline-block">
                            {{ __('messages.explore_more_services') }}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
