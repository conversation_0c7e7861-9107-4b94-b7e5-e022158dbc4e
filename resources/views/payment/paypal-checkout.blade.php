@extends('layouts.app')

@section('title', 'الدفع عبر PayPal')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-md mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">إتمام الدفع</h1>
                <p class="text-gray-600 dark:text-gray-400">دفع آمن عبر PayPal</p>
            </div>

            <!-- Payment Details -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mb-6">
                <h3 class="font-semibold text-gray-900 dark:text-white mb-4">تفاصيل الدفعة</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الخدمة:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ $payment->description }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">المبلغ:</span>
                        <span class="font-bold text-xl text-gray-900 dark:text-white">{{ number_format($payment->amount, 2) }} {{ $payment->currency }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">طريقة الدفع:</span>
                        <span class="font-medium text-gray-900 dark:text-white">PayPal</span>
                    </div>
                </div>
            </div>

            <!-- PayPal Button Container -->
            <div id="paypal-button-container" class="mb-6"></div>

            <!-- Loading State -->
            <div id="loading-state" class="text-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600 dark:text-gray-400">جاري تحميل PayPal...</p>
            </div>

            <!-- Security Notice -->
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span class="text-sm text-green-800 dark:text-green-200">
                        دفع آمن ومحمي بواسطة PayPal
                    </span>
                </div>
            </div>

            <!-- Cancel Link -->
            <div class="text-center mt-6">
                <a href="{{ route('pricing') }}" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm">
                    إلغاء والعودة للأسعار
                </a>
            </div>
        </div>
    </div>
</div>

<!-- PayPal SDK -->
<script src="https://www.paypal.com/sdk/js?client-id={{ env('PAYPAL_CLIENT_ID') }}&currency=USD"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hide loading state once PayPal loads
    document.getElementById('loading-state').style.display = 'none';
    
    // Render PayPal button
    paypal.Buttons({
        style: {
            layout: 'vertical',
            color: 'blue',
            shape: 'rect',
            label: 'paypal'
        },
        
        createOrder: function(data, actions) {
            return actions.order.create({
                purchase_units: [{
                    amount: {
                        value: '{{ $payment->amount }}',
                        currency_code: 'USD' // PayPal typically uses USD
                    },
                    description: '{{ $payment->description }}'
                }]
            });
        },
        
        onApprove: function(data, actions) {
            return actions.order.capture().then(function(details) {
                // Payment successful
                console.log('Payment completed by ' + details.payer.name.given_name);
                
                // Redirect to success page
                window.location.href = '{{ route("payment.success", ["payment_id" => $payment->id]) }}';
            });
        },
        
        onError: function(err) {
            console.error('PayPal error:', err);
            alert('حدث خطأ في معالجة الدفع. يرجى المحاولة مرة أخرى.');
        },
        
        onCancel: function(data) {
            console.log('Payment cancelled:', data);
            window.location.href = '{{ route("payment.cancel") }}';
        }
        
    }).render('#paypal-button-container');
});
</script>
@endsection
