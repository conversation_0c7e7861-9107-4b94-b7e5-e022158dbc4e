@extends('layouts.app')

@section('title', 'الدفع عبر Stripe')

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-md mx-auto">
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                </div>
                <h1 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">إتمام الدفع</h1>
                <p class="text-gray-600 dark:text-gray-400">دفع آمن عبر Stripe</p>
            </div>

            <!-- Payment Details -->
            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-6 mb-6">
                <h3 class="font-semibold text-gray-900 dark:text-white mb-4">تفاصيل الدفعة</h3>
                
                <div class="space-y-3">
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">الخدمة:</span>
                        <span class="font-medium text-gray-900 dark:text-white">{{ $payment->description }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">المبلغ:</span>
                        <span class="font-bold text-xl text-gray-900 dark:text-white">{{ number_format($payment->amount, 2) }} {{ $payment->currency }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="text-gray-600 dark:text-gray-400">طريقة الدفع:</span>
                        <span class="font-medium text-gray-900 dark:text-white">Stripe</span>
                    </div>
                </div>
            </div>

            <!-- Stripe Payment Form -->
            <form id="payment-form" class="space-y-6">
                @csrf
                
                <!-- Card Element -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                        معلومات البطاقة
                    </label>
                    <div id="card-element" class="p-4 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700">
                        <!-- Stripe Elements will create form elements here -->
                    </div>
                    <div id="card-errors" role="alert" class="text-red-600 text-sm mt-2"></div>
                </div>

                <!-- Submit Button -->
                <button type="submit" id="submit-button" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center">
                    <svg id="loading-spinner" class="hidden animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    <span id="button-text">ادفع {{ number_format($payment->amount, 2) }} {{ $payment->currency }}</span>
                </button>
            </form>

            <!-- Security Notice -->
            <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <div class="flex items-center">
                    <svg class="w-5 h-5 text-green-600 dark:text-green-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                    <span class="text-sm text-green-800 dark:text-green-200">
                        دفع آمن ومشفر بتقنية SSL
                    </span>
                </div>
            </div>

            <!-- Cancel Link -->
            <div class="text-center mt-6">
                <a href="{{ route('pricing') }}" class="text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200 text-sm">
                    إلغاء والعودة للأسعار
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Stripe JS -->
<script src="https://js.stripe.com/v3/"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Stripe
    const stripe = Stripe('{{ env("STRIPE_KEY") }}');
    const elements = stripe.elements();

    // Create card element
    const cardElement = elements.create('card', {
        style: {
            base: {
                fontSize: '16px',
                color: document.documentElement.classList.contains('dark') ? '#ffffff' : '#424770',
                '::placeholder': {
                    color: document.documentElement.classList.contains('dark') ? '#9ca3af' : '#aab7c4',
                },
            },
        },
    });

    cardElement.mount('#card-element');

    // Handle form submission
    const form = document.getElementById('payment-form');
    const submitButton = document.getElementById('submit-button');
    const buttonText = document.getElementById('button-text');
    const loadingSpinner = document.getElementById('loading-spinner');

    form.addEventListener('submit', async (event) => {
        event.preventDefault();

        // Disable submit button and show loading
        submitButton.disabled = true;
        buttonText.textContent = 'جاري المعالجة...';
        loadingSpinner.classList.remove('hidden');

        // Create payment method
        const {error, paymentMethod} = await stripe.createPaymentMethod({
            type: 'card',
            card: cardElement,
        });

        if (error) {
            // Show error to customer
            showError(error.message);
            
            // Re-enable submit button
            submitButton.disabled = false;
            buttonText.textContent = 'ادفع {{ number_format($payment->amount, 2) }} {{ $payment->currency }}';
            loadingSpinner.classList.add('hidden');
        } else {
            // Send payment method to server
            handlePaymentMethod(paymentMethod);
        }
    });

    function showError(message) {
        const errorElement = document.getElementById('card-errors');
        errorElement.textContent = message;
    }

    function handlePaymentMethod(paymentMethod) {
        // For now, simulate successful payment
        // In real implementation, send to server for processing
        setTimeout(() => {
            window.location.href = '{{ route("payment.success", ["payment_id" => $payment->id]) }}';
        }, 2000);
    }

    // Handle real-time validation errors from the card Element
    cardElement.on('change', ({error}) => {
        const displayError = document.getElementById('card-errors');
        if (error) {
            displayError.textContent = error.message;
        } else {
            displayError.textContent = '';
        }
    });
});
</script>
@endsection
