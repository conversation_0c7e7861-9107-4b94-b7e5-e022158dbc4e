@extends('layouts.app')

@section('title', __('messages.payment_for_service') . ' - MonOri AI')
@section('description', __('messages.secure_payment_process'))

@section('content')
<div class="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.complete_payment') }}</h1>
            <p class="text-gray-600 dark:text-gray-400">{{ __('messages.secure_payment_description') }}</p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Service Details -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.service_details') }}</h2>
                
                <div class="space-y-4">
                    <div class="flex items-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                        <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mr-4">
                            @if($service->type === 'custom')
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                            @elseif($service->type === 'cv_creation')
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                            @else
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                </svg>
                            @endif
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 dark:text-white">
                                @if(app()->getLocale() === 'ar')
                                    {{ $service->name_ar }}
                                @elseif(app()->getLocale() === 'fr')
                                    {{ $service->name_fr }}
                                @else
                                    {{ $service->name_en }}
                                @endif
                            </h3>
                            <p class="text-sm text-gray-600 dark:text-gray-400">
                                @if(app()->getLocale() === 'ar')
                                    {{ $service->description_ar }}
                                @elseif(app()->getLocale() === 'fr')
                                    {{ $service->description_fr }}
                                @else
                                    {{ $service->description_en }}
                                @endif
                            </p>
                        </div>
                    </div>

                    <!-- Features -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <h4 class="font-medium text-gray-900 dark:text-white mb-3">{{ __('messages.included_features') }}</h4>
                        <ul class="space-y-2">
                            @foreach($service->features as $feature => $included)
                                @if($included)
                                    <li class="flex items-center text-sm text-gray-600 dark:text-gray-400">
                                        <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                        {{ __('messages.feature_' . $feature) }}
                                    </li>
                                @endif
                            @endforeach
                        </ul>
                    </div>

                    <!-- Delivery Time -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="flex justify-between items-center">
                            <span class="text-gray-600 dark:text-gray-400">{{ __('messages.delivery_time') }}</span>
                            <span class="font-medium text-gray-900 dark:text-white">
                                {{ $service->delivery_time_hours }} {{ __('messages.hours') }}
                            </span>
                        </div>
                    </div>

                    <!-- Price -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-4">
                        <div class="flex justify-between items-center">
                            <span class="text-lg font-medium text-gray-900 dark:text-white">{{ __('messages.total_amount') }}</span>
                            <span class="text-2xl font-bold text-blue-600 dark:text-blue-400">
                                {{ number_format($service->price, 2) }} {{ $service->currency }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Form -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.payment_information') }}</h2>
                
                <form id="payment-form" class="space-y-6">
                    @csrf
                    <input type="hidden" name="service_id" value="{{ $service->id }}">
                    
                    <!-- Payment Method -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">{{ __('messages.payment_method') }}</label>
                        <div class="space-y-3">
                            <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <input type="radio" name="payment_method" value="stripe" class="mr-3" checked>
                                <div class="flex items-center">
                                    <svg class="w-6 h-6 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                    </svg>
                                    <span class="text-gray-900 dark:text-white">{{ __('messages.pay_with_card') }}</span>
                                </div>
                            </label>
                            
                            <label class="flex items-center p-4 border border-gray-300 dark:border-gray-600 rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                                <input type="radio" name="payment_method" value="paypal" class="mr-3">
                                <div class="flex items-center">
                                    <svg class="w-6 h-6 mr-3" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h7.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-.635 4.005c-.08.52-.527.901-1.05.901z"/>
                                    </svg>
                                    <span class="text-gray-900 dark:text-white">{{ __('messages.pay_with_paypal') }}</span>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Customer Information -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">{{ __('messages.customer_information') }}</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.full_name') }}</label>
                                <input type="text" value="{{ $user->name }}" readonly class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.email') }}</label>
                                <input type="email" value="{{ $user->email }}" readonly class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-gray-50 dark:bg-gray-700 text-gray-900 dark:text-white">
                            </div>
                        </div>
                    </div>

                    <!-- Terms and Conditions -->
                    <div class="border-t border-gray-200 dark:border-gray-700 pt-6">
                        <label class="flex items-start">
                            <input type="checkbox" id="terms" class="mt-1 mr-3" required>
                            <span class="text-sm text-gray-600 dark:text-gray-400">
                                {{ __('messages.agree_to_terms_1') }}
                                <a href="#" class="text-blue-600 hover:text-blue-700">{{ __('messages.terms_and_conditions') }}</a>
                                {{ __('messages.agree_to_terms_2') }}
                                <a href="#" class="text-blue-600 hover:text-blue-700">{{ __('messages.privacy_policy') }}</a>
                            </span>
                        </label>
                    </div>

                    <!-- Submit Button -->
                    <div class="pt-6">
                        <button type="submit" id="pay-button" class="w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white py-4 px-6 rounded-lg text-lg font-semibold hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed">
                            <span id="button-text">{{ __('messages.pay_now') }} {{ number_format($service->price, 2) }} {{ $service->currency }}</span>
                            <span id="loading-text" class="hidden">{{ __('messages.processing_payment') }}...</span>
                        </button>
                    </div>
                </form>

                <!-- Security Notice -->
                <div class="mt-6 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        <span class="text-sm text-green-700 dark:text-green-300">{{ __('messages.secure_payment_notice') }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('payment-form').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const button = document.getElementById('pay-button');
    const buttonText = document.getElementById('button-text');
    const loadingText = document.getElementById('loading-text');
    const termsCheckbox = document.getElementById('terms');
    
    if (!termsCheckbox.checked) {
        alert('{{ __('messages.please_accept_terms') }}');
        return;
    }
    
    // Disable button and show loading
    button.disabled = true;
    buttonText.classList.add('hidden');
    loadingText.classList.remove('hidden');
    
    const formData = new FormData(this);
    
    fetch('{{ route('payment.process-service') }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            window.location.href = data.redirect_url;
        } else {
            alert(data.message);
            // Re-enable button
            button.disabled = false;
            buttonText.classList.remove('hidden');
            loadingText.classList.add('hidden');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __('messages.payment_error') }}');
        // Re-enable button
        button.disabled = false;
        buttonText.classList.remove('hidden');
        loadingText.classList.add('hidden');
    });
});
</script>
@endsection
