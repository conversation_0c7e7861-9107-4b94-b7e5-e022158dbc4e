<!-- Floating Customer Service Robot -->
<div id="floatingRobot" class="fixed bottom-6 right-6 z-50" style="display: block !important; width: 80px; height: 80px;">
    <!-- Robot Button -->
    <div id="robotButton" class="relative cursor-pointer group w-full h-full">
        <!-- Robot Container -->
        <div class="w-full h-full rounded-full shadow-2xl transform transition-all duration-300 hover:scale-110 hover:shadow-3xl" style="background: linear-gradient(135deg, #3B82F6, #8B5CF6, #4F46E5);"
            <!-- Robot Body -->
            <div class="relative w-full h-full rounded-full overflow-hidden">
                <!-- Robot Face Background -->
                <div class="absolute inset-2 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full">
                    <!-- Robot Eyes -->
                    <div class="absolute top-3 left-3 w-2 h-2 bg-white rounded-full">
                        <div id="robotLeftEye" class="w-1 h-1 bg-blue-800 rounded-full mt-0.5 ml-0.5 transition-all duration-300"></div>
                    </div>
                    <div class="absolute top-3 right-3 w-2 h-2 bg-white rounded-full">
                        <div id="robotRightEye" class="w-1 h-1 bg-blue-800 rounded-full mt-0.5 ml-0.5 transition-all duration-300"></div>
                    </div>
                    
                    <!-- Robot Mouth -->
                    <div id="robotMouth" class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-3 h-1.5 bg-white rounded-full transition-all duration-300">
                        <div class="w-full h-full bg-gray-800 rounded-full transform scale-75"></div>
                    </div>
                    
                    <!-- Robot Antenna -->
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-0.5 h-3 bg-gray-300 rounded-full">
                        <div id="robotAntenna" class="absolute -top-0.5 left-1/2 transform -translate-x-1/2 w-1.5 h-1.5 bg-red-400 rounded-full animate-pulse"></div>
                    </div>
                </div>
                
                <!-- Robot Arms -->
                <div id="robotLeftArm" class="absolute top-6 -left-2 w-3 h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full transform rotate-45 transition-all duration-500"></div>
                <div id="robotRightArm" class="absolute top-6 -right-2 w-3 h-1 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full transform -rotate-45 transition-all duration-500"></div>
                
                <!-- Speaking Animation Rings -->
                <div id="speakingRings" class="absolute inset-0 opacity-0 transition-opacity duration-300">
                    <div class="absolute inset-1 border-2 border-white/30 rounded-full animate-ping"></div>
                    <div class="absolute inset-2 border-2 border-white/20 rounded-full animate-ping" style="animation-delay: 0.2s"></div>
                </div>
                
                <!-- Floating Animation -->
                <div class="absolute -inset-2 bg-gradient-to-r from-blue-400/20 to-purple-600/20 rounded-full animate-pulse"></div>
            </div>
        </div>
        
        <!-- Notification Badge -->
        <div id="notificationBadge" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold animate-bounce hidden">
            !
        </div>
        
        <!-- Hover Text -->
        <div class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            مساعد ذكي - اسأل أي سؤال!
            <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
    </div>
    
    <!-- Chat Window -->
    <div id="chatWindow" class="absolute bottom-20 right-0 w-80 h-96 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform scale-0 origin-bottom-right transition-all duration-300 opacity-0">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-white/20 rounded-full mr-3 flex items-center justify-center">
                        <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full relative">
                            <div class="absolute top-1 left-1 w-1 h-1 bg-white rounded-full"></div>
                            <div class="absolute top-1 right-1 w-1 h-1 bg-white rounded-full"></div>
                            <div class="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-2 h-0.5 bg-white rounded-full"></div>
                        </div>
                    </div>
                    <div>
                        <h3 class="font-semibold text-sm">مساعد MonOri الذكي</h3>
                        <p class="text-xs text-white/80">متصل الآن</p>
                    </div>
                </div>
                <button id="closeChatBtn" class="text-white/80 hover:text-white transition-colors">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Chat Messages -->
        <div id="chatMessages" class="h-64 p-4 overflow-y-auto space-y-3">
            <div class="flex items-start">
                <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0"></div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-900 dark:text-white text-sm">مرحباً! أنا مساعدك الذكي في MonOri AI. كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex space-x-2 rtl:space-x-reverse">
                <input type="text" id="chatInput" placeholder="اكتب سؤالك هنا..." 
                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm">
                <button id="sendChatBtn" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="px-4 pb-4">
            <div class="flex flex-wrap gap-2">
                <button class="quick-action-btn text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors" data-question="ما هي خدمات MonOri AI؟">
                    خدماتنا
                </button>
                <button class="quick-action-btn text-xs bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full hover:bg-purple-200 dark:hover:bg-purple-900/40 transition-colors" data-question="كيف أنشئ سيرة ذاتية؟">
                    إنشاء CV
                </button>
                <button class="quick-action-btn text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors" data-question="ما هي أسعار الخدمات؟">
                    الأسعار
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Custom animations for the robot */
@keyframes robotFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-3px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(2deg); }
    75% { transform: translateY(-3px) rotate(1deg); }
}

@keyframes robotWave {
    0%, 100% { transform: rotate(45deg); }
    25% { transform: rotate(60deg); }
    50% { transform: rotate(90deg); }
    75% { transform: rotate(60deg); }
}

@keyframes robotBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes robotBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes robotShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

@keyframes robotPulse {
    0%, 100% { box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.7); }
    50% { box-shadow: 0 0 0 10px rgba(59, 130, 246, 0); }
}

#floatingRobot #robotButton {
    animation: robotFloat 4s ease-in-out infinite;
}

#floatingRobot:hover #robotButton {
    animation: robotFloat 4s ease-in-out infinite, robotPulse 2s infinite;
}

#floatingRobot:hover #robotLeftArm {
    animation: robotWave 1.5s ease-in-out infinite;
}

#floatingRobot:hover #robotRightArm {
    animation: robotWave 1.5s ease-in-out infinite reverse;
    animation-delay: 0.3s;
}

#floatingRobot #robotLeftEye,
#floatingRobot #robotRightEye {
    animation: robotBlink 5s ease-in-out infinite;
}

#floatingRobot.speaking #robotButton {
    animation: robotFloat 4s ease-in-out infinite, robotBounce 0.5s ease-in-out infinite;
}

#floatingRobot.thinking #robotButton {
    animation: robotFloat 4s ease-in-out infinite, robotShake 0.3s ease-in-out infinite;
}

/* Responsive adjustments */
@media (max-width: 640px) {
    #chatWindow {
        width: calc(100vw - 2rem);
        right: -1rem;
    }
}
</style>

<script>
class FloatingRobot {
    constructor() {
        this.isOpen = false;
        this.isTyping = false;
        this.messages = [];
        
        this.initializeElements();
        this.setupEventListeners();
        this.startIdleAnimations();
    }
    
    initializeElements() {
        this.robotButton = document.getElementById('robotButton');
        this.chatWindow = document.getElementById('chatWindow');
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendBtn = document.getElementById('sendChatBtn');
        this.closeBtn = document.getElementById('closeChatBtn');
        this.quickActionBtns = document.querySelectorAll('.quick-action-btn');
        
        this.robotMouth = document.getElementById('robotMouth');
        this.speakingRings = document.getElementById('speakingRings');
        this.leftEye = document.getElementById('robotLeftEye');
        this.rightEye = document.getElementById('robotRightEye');
    }
    
    setupEventListeners() {
        this.robotButton.addEventListener('click', () => this.toggleChat());
        this.closeBtn.addEventListener('click', () => this.closeChat());
        this.sendBtn.addEventListener('click', () => this.sendMessage());
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.sendMessage();
        });
        
        this.quickActionBtns.forEach(btn => {
            btn.addEventListener('click', async () => {
                const question = btn.dataset.question;
                this.addMessage('user', question);
                this.setRobotExpression('thinking');
                this.showTypingIndicator();

                try {
                    const response = await this.getAIResponse(question);
                    this.hideTypingIndicator();
                    this.addMessage('robot', response);
                    this.setRobotExpression('speaking');
                    this.speakText(response);

                    setTimeout(() => {
                        this.setRobotExpression('happy');
                    }, 2000);
                } catch (error) {
                    this.hideTypingIndicator();
                    this.addMessage('robot', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
                    this.setRobotExpression('neutral');
                }
            });
        });
    }
    
    toggleChat() {
        if (this.isOpen) {
            this.closeChat();
        } else {
            this.openChat();
        }
    }
    
    openChat() {
        this.isOpen = true;
        this.chatWindow.classList.remove('scale-0', 'opacity-0');
        this.chatWindow.classList.add('scale-100', 'opacity-100');
        this.chatInput.focus();
        this.setRobotExpression('happy');
    }
    
    closeChat() {
        this.isOpen = false;
        this.chatWindow.classList.add('scale-0', 'opacity-0');
        this.chatWindow.classList.remove('scale-100', 'opacity-100');
        this.setRobotExpression('neutral');
    }
    
    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;
        
        this.addMessage('user', message);
        this.chatInput.value = '';
        this.setRobotExpression('thinking');
        
        // Show typing indicator
        this.showTypingIndicator();
        
        try {
            const response = await this.getAIResponse(message);
            this.hideTypingIndicator();
            this.addMessage('robot', response);
            this.setRobotExpression('speaking');
            
            // Speak the response if supported
            this.speakText(response);
            
            setTimeout(() => {
                this.setRobotExpression('happy');
            }, 2000);
        } catch (error) {
            this.hideTypingIndicator();
            this.addMessage('robot', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
            this.setRobotExpression('neutral');
        }
    }
    
    addMessage(sender, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start mb-3';
        
        if (sender === 'robot') {
            messageDiv.innerHTML = `
                <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0"></div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-900 dark:text-white text-sm">${text}</p>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="flex items-start justify-end w-full">
                    <div class="bg-blue-600 text-white rounded-lg p-3 max-w-xs mr-2">
                        <p class="text-sm">${text}</p>
                    </div>
                    <div class="w-6 h-6 bg-gray-400 rounded-full flex-shrink-0"></div>
                </div>
            `;
        }
        
        this.chatMessages.appendChild(messageDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'flex items-start mb-3';
        typingDiv.innerHTML = `
            <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0"></div>
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
            </div>
        `;
        
        this.chatMessages.appendChild(typingDiv);
        this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
    }
    
    hideTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    async getAIResponse(message) {
        try {
            const response = await fetch('/customer-service/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    message: message,
                    language: document.documentElement.lang || 'ar'
                })
            });

            const data = await response.json();

            if (data.success) {
                return data.response;
            } else {
                throw new Error(data.message || 'خطأ في الخدمة');
            }
        } catch (error) {
            console.error('AI Response Error:', error);
            return 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
        }
    }
    
    setRobotExpression(expression) {
        const robotContainer = document.getElementById('floatingRobot');

        // Remove all expression classes
        robotContainer.classList.remove('speaking', 'thinking', 'happy', 'neutral');

        switch(expression) {
            case 'speaking':
                robotContainer.classList.add('speaking');
                this.speakingRings.style.opacity = '1';
                this.robotMouth.style.transform = 'scaleY(1.5) scaleX(1.2)';
                this.robotMouth.style.backgroundColor = '#EF4444';
                this.leftEye.style.backgroundColor = '#10B981';
                this.rightEye.style.backgroundColor = '#10B981';
                break;
            case 'thinking':
                robotContainer.classList.add('thinking');
                this.speakingRings.style.opacity = '0';
                this.robotMouth.style.transform = 'scaleY(0.8) scaleX(0.8)';
                this.robotMouth.style.backgroundColor = '#F59E0B';
                this.leftEye.style.backgroundColor = '#F59E0B';
                this.rightEye.style.backgroundColor = '#F59E0B';
                break;
            case 'happy':
                robotContainer.classList.add('happy');
                this.speakingRings.style.opacity = '0';
                this.robotMouth.style.transform = 'scaleY(1.3) scaleX(1.1)';
                this.robotMouth.style.backgroundColor = '#10B981';
                this.leftEye.style.backgroundColor = '#10B981';
                this.rightEye.style.backgroundColor = '#10B981';
                break;
            case 'excited':
                this.speakingRings.style.opacity = '0.5';
                this.robotMouth.style.transform = 'scaleY(1.4) scaleX(1.3)';
                this.robotMouth.style.backgroundColor = '#8B5CF6';
                this.leftEye.style.backgroundColor = '#8B5CF6';
                this.rightEye.style.backgroundColor = '#8B5CF6';
                break;
            default:
                robotContainer.classList.add('neutral');
                this.speakingRings.style.opacity = '0';
                this.robotMouth.style.transform = 'scaleY(1) scaleX(1)';
                this.robotMouth.style.backgroundColor = '#6B7280';
                this.leftEye.style.backgroundColor = '#1E40AF';
                this.rightEye.style.backgroundColor = '#1E40AF';
        }
    }
    
    speakText(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.9;
            
            utterance.onstart = () => {
                this.setRobotExpression('speaking');
            };
            
            utterance.onend = () => {
                this.setRobotExpression('happy');
            };
            
            speechSynthesis.speak(utterance);
        }
    }
    
    startIdleAnimations() {
        // Random movements and expressions
        setInterval(() => {
            if (!this.isOpen && !this.isTyping) {
                const expressions = ['neutral', 'happy', 'excited'];
                const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
                this.setRobotExpression(randomExpression);

                setTimeout(() => {
                    this.setRobotExpression('neutral');
                }, 2000);
            }
        }, 8000);

        // Random arm movements
        setInterval(() => {
            if (!this.isOpen && !this.isTyping) {
                const leftArm = document.getElementById('robotLeftArm');
                const rightArm = document.getElementById('robotRightArm');

                // Random arm positions
                const leftRotation = Math.random() * 90 + 45; // 45-135 degrees
                const rightRotation = -(Math.random() * 90 + 45); // -45 to -135 degrees

                leftArm.style.transform = `rotate(${leftRotation}deg)`;
                rightArm.style.transform = `rotate(${rightRotation}deg)`;

                setTimeout(() => {
                    leftArm.style.transform = 'rotate(45deg)';
                    rightArm.style.transform = 'rotate(-45deg)';
                }, 1500);
            }
        }, 12000);

        // Notification badge animation
        setInterval(() => {
            if (!this.isOpen && Math.random() < 0.3) {
                const badge = document.getElementById('notificationBadge');
                badge.classList.remove('hidden');

                setTimeout(() => {
                    badge.classList.add('hidden');
                }, 3000);
            }
        }, 30000);
    }
}

// Initialize the floating robot when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    new FloatingRobot();
});
</script>
