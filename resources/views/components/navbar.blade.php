<nav class="bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 sticky top-0 z-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-14">
            <!-- Logo -->
            <div class="flex items-center">
                <a href="{{ url('/') }}" class="flex items-center space-x-2 rtl:space-x-reverse hover-lift group">
                    <div class="w-8 h-8 flex items-center justify-center transition-transform duration-300 group-hover:scale-110 group-hover:rotate-12">
                        <img src="{{ asset('images/logo.svg') }}" alt="MonOri AI Logo" class="w-8 h-8 drop-shadow-sm">
                    </div>
                    <span class="text-lg font-bold gradient-text group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors duration-300">MonOri AI</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex items-center space-x-6 rtl:space-x-reverse">
                <a href="{{ route('home') }}" class="text-compact-small text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('home') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_home') }}
                </a>

                <!-- Services Dropdown -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center space-x-1 rtl:space-x-reverse text-compact-small text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('services*') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                        <span>{{ __('messages.nav_services') }}</span>
                        <svg class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Services Dropdown Menu -->
                    <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute left-0 mt-2 w-56 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-50">

                        <a href="{{ route('services') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                <svg class="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                                <span>
                                    @if(app()->getLocale() === 'ar')
                                        جميع الخدمات
                                    @elseif(app()->getLocale() === 'fr')
                                        Tous les services
                                    @else
                                        All Services
                                    @endif
                                </span>
                            </div>
                        </a>

                        <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>

                        <a href="{{ route('services.interview-simulation') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                <svg class="w-4 h-4 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                                </svg>
                                <span>
                                    @if(app()->getLocale() === 'ar')
                                        تدريب مقابلة العمل
                                    @elseif(app()->getLocale() === 'fr')
                                        Entraînement d'entretien
                                    @else
                                        Interview Training
                                    @endif
                                </span>
                            </div>
                        </a>

                        <a href="{{ route('cv.builder') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <span>
                                    @if(app()->getLocale() === 'ar')
                                        منشئ السيرة الذاتية
                                    @elseif(app()->getLocale() === 'fr')
                                        Créateur de CV
                                    @else
                                        CV Builder
                                    @endif
                                </span>
                            </div>
                        </a>

                        <a href="{{ route('services.personality-analysis') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            <div class="flex items-center space-x-3 rtl:space-x-reverse">
                                <svg class="w-4 h-4 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364-.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                                <span>
                                    @if(app()->getLocale() === 'ar')
                                        تحليل الشخصية
                                    @elseif(app()->getLocale() === 'fr')
                                        Analyse de personnalité
                                    @else
                                        Personality Analysis
                                    @endif
                                </span>
                            </div>
                        </a>
                    </div>
                </div>
                <a href="{{ route('pricing') }}" class="text-compact-small text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('pricing') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_pricing') }}
                </a>

                <a href="{{ route('about') }}" class="text-compact-small text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('about') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_about') }}
                </a>
                <a href="{{ route('contact') }}" class="text-compact-small text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('contact') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_contact') }}
                </a>
            </div>

            <!-- Right Side Actions -->
            <div class="flex items-center space-x-4 rtl:space-x-reverse">
                <!-- Language Selector -->
                <div class="relative" x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center space-x-1 rtl:space-x-reverse px-2 py-1 text-xs font-medium text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                        <span>
                            @if(app()->getLocale() === 'ar')
                                العربية
                            @elseif(app()->getLocale() === 'fr')
                                Français
                            @else
                                English
                            @endif
                        </span>
                        <svg class="w-3 h-3 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <!-- Dropdown Menu -->
                    <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 mt-1 w-24 bg-white dark:bg-gray-800 rounded-md shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50">

                        @if(app()->getLocale() !== 'ar')
                        <a href="{{ route('set-language', 'ar') }}" class="block px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            العربية
                        </a>
                        @endif

                        @if(app()->getLocale() !== 'en')
                        <a href="{{ route('set-language', 'en') }}" class="block px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            English
                        </a>
                        @endif

                        @if(app()->getLocale() !== 'fr')
                        <a href="{{ route('set-language', 'fr') }}" class="block px-3 py-1 text-xs text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                            Français
                        </a>
                        @endif
                    </div>
                </div>

                <!-- Admin Link (only for admins) -->
                @auth
                    @if(auth()->user()->isAdmin())
                        <a href="{{ route('admin.dashboard') }}" class="p-2 text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200" title="Admin Dashboard">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                            </svg>
                        </a>
                    @endif
                @endauth

                <!-- Theme Toggle -->
                <button onclick="toggleTheme()" class="p-2 text-gray-700 dark:text-gray-300 light:text-gray-700 hover:text-blue-600 dark:hover:text-blue-400 light:hover:text-blue-600 transition-colors duration-200" title="{{ __('messages.toggle_theme') }}">
                    <!-- Sun icon for light mode (shows when in dark mode) -->
                    <svg class="w-5 h-5 block dark:block light:hidden" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path>
                    </svg>
                    <!-- Moon icon for dark mode (shows when in light mode) -->
                    <svg class="w-5 h-5 hidden light:block" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                    </svg>
                </button>

                <!-- CTA Buttons / User Menu -->
                <div class="hidden md:flex items-center space-x-3 rtl:space-x-reverse">
                    @auth
                        <!-- User Profile Dropdown -->
                        <div class="relative" x-data="{ open: false }">
                            <button @click="open = !open" class="hover-lift flex items-center space-x-2 rtl:space-x-reverse text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                                <div class="w-7 h-7 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-medium">{{ substr(Auth::user()->name, 0, 1) }}</span>
                                </div>
                                <span class="text-compact-small font-medium">{{ Str::limit(Auth::user()->name, 15) }}</span>
                                <svg class="icon-small transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>

                            <!-- Dropdown Menu -->
                            <div x-show="open" @click.away="open = false" x-transition:enter="transition ease-out duration-100" x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100" x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100" x-transition:leave-end="transform opacity-0 scale-95" class="absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50">

                                <a href="{{ route('profile') }}" class="block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                    <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        <span>{{ __('messages.profile') }}</span>
                                    </div>
                                </a>
                                <div class="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="block w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200">
                                        <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                            </svg>
                                            <span>{{ __('messages.logout') }}</span>
                                        </div>
                                    </button>
                                </form>
                            </div>
                        </div>
                    @else
                        <!-- Login/Register Buttons -->
                        <a href="{{ route('login') }}" class="text-compact-small text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            {{ __('messages.login') }}
                        </a>
                        <a href="{{ route('register') }}" class="btn-primary-compact">
                            {{ __('messages.get_started') }}
                        </a>
                    @endauth
                </div>

                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" onclick="toggleMobileMenu()" class="md:hidden p-2 text-gray-700 dark:text-gray-300 hover-lift">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div id="mobile-menu" class="hidden md:hidden py-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex flex-col space-y-4">
                <a href="{{ route('home') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('home') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_home') }}
                </a>

                <!-- Services Mobile Dropdown -->
                <div x-data="{ open: false }">
                    <button @click="open = !open" class="flex items-center justify-between w-full text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('services*') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                        <span>{{ __('messages.nav_services') }}</span>
                        <svg class="w-4 h-4 transition-transform duration-200" :class="{ 'rotate-180': open }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>

                    <div x-show="open" x-transition class="mt-2 ml-4 space-y-2">
                        <a href="{{ route('services') }}" class="block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            @if(app()->getLocale() === 'ar')
                                جميع الخدمات
                            @elseif(app()->getLocale() === 'fr')
                                Tous les services
                            @else
                                All Services
                            @endif
                        </a>
                        <a href="{{ route('services.interview-simulation') }}" class="block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            @if(app()->getLocale() === 'ar')
                                تدريب مقابلة العمل
                            @elseif(app()->getLocale() === 'fr')
                                Entraînement d'entretien
                            @else
                                Interview Training
                            @endif
                        </a>
                        <a href="{{ route('cv.builder') }}" class="block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            @if(app()->getLocale() === 'ar')
                                منشئ السيرة الذاتية
                            @elseif(app()->getLocale() === 'fr')
                                Créateur de CV
                            @else
                                CV Builder
                            @endif
                        </a>
                        <a href="{{ route('services.personality-analysis') }}" class="block text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            @if(app()->getLocale() === 'ar')
                                تحليل الشخصية
                            @elseif(app()->getLocale() === 'fr')
                                Analyse de personnalité
                            @else
                                Personality Analysis
                            @endif
                        </a>
                    </div>
                </div>
                <a href="{{ route('pricing') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('pricing') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_pricing') }}
                </a>

                <a href="{{ route('about') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('about') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_about') }}
                </a>
                <a href="{{ route('contact') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 {{ request()->routeIs('contact') ? 'text-blue-600 dark:text-blue-400 font-semibold' : '' }}">
                    {{ __('messages.nav_contact') }}
                </a>
                <div class="flex flex-col space-y-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                    @auth
                        <!-- User Profile Mobile -->
                        <div class="flex items-center space-x-3 rtl:space-x-reverse p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div class="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-medium">{{ substr(Auth::user()->name, 0, 1) }}</span>
                            </div>
                            <div>
                                <p class="font-medium text-gray-900 dark:text-white">{{ Auth::user()->name }}</p>
                                <p class="text-sm text-gray-500 dark:text-gray-400">{{ Auth::user()->email }}</p>
                            </div>
                        </div>

                        <a href="{{ route('profile') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                            <span>{{ __('messages.profile') }}</span>
                        </a>
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="w-full text-left text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 transition-colors duration-200 flex items-center space-x-2 rtl:space-x-reverse">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                </svg>
                                <span>{{ __('messages.logout') }}</span>
                            </button>
                        </form>
                    @else
                        <!-- Login/Register Mobile -->
                        <a href="{{ route('login') }}" class="text-gray-700 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors duration-200">
                            {{ __('messages.login') }}
                        </a>
                        <a href="{{ route('register') }}" class="btn-primary text-white px-4 py-2 rounded-lg text-sm font-medium text-center">
                            {{ __('messages.get_started') }}
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </div>
</nav>
