<!-- Cute Floating Assistant Robot (3D Vector Style) -->
<div id="followingRobot" class="fixed top-4 right-4 w-20 h-24 cursor-pointer transition-all duration-300 hover:scale-105 z-50" style="z-index: 2147483647;">
    <!-- Main Ground Shadow (Large & Soft) with Animation -->
    <div id="mainShadow" class="absolute bottom-0 left-1/2 w-18 h-3 rounded-full" style="background: radial-gradient(ellipse, rgba(0,0,0,0.3) 0%, rgba(0,0,0,0.2) 30%, rgba(0,0,0,0.1) 60%, rgba(0,0,0,0.05) 80%, transparent 100%); filter: blur(4px); animation: shadowFloat 4s ease-in-out infinite;"></div>

    <!-- Secondary Shadow Layer (Medium & Focused) with Animation -->
    <div id="secondaryShadow" class="absolute bottom-0.5 left-1/2 w-14 h-1.5 rounded-full" style="background: radial-gradient(ellipse, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.25) 40%, rgba(0,0,0,0.1) 70%, transparent 100%); filter: blur(2px); animation: shadowFloat 4s ease-in-out infinite 0.1s;"></div>

    <!-- Core Shadow (Small & Sharp) with Animation -->
    <div id="coreShadow" class="absolute bottom-1 left-1/2 w-10 h-1 rounded-full" style="background: radial-gradient(ellipse, rgba(0,0,0,0.5) 0%, rgba(0,0,0,0.3) 50%, transparent 100%); filter: blur(0.5px); animation: shadowFloat 4s ease-in-out infinite 0.2s;"></div>

    <!-- Robot Container -->
    <div class="relative w-full h-full">
        <!-- Robot Head (White/Gray Smooth with Enhanced Shadow) -->
        <div class="relative w-14 h-14 mx-auto rounded-full" style="background: linear-gradient(145deg, #ffffff 0%, #f1f5f9 30%, #e2e8f0 70%, #cbd5e1 100%); box-shadow: 0 8px 25px rgba(0,0,0,0.2), 0 4px 12px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.8), inset 0 -1px 0 rgba(0,0,0,0.1);">

            <!-- Black Face Screen with Depth -->
            <div class="absolute inset-2 rounded-full overflow-hidden" style="background: linear-gradient(145deg, #0f172a 0%, #1e293b 100%); box-shadow: inset 0 2px 8px rgba(0,0,0,0.6), inset 0 4px 12px rgba(0,0,0,0.4);">
                <!-- Glowing Blue Eyes (Closer Together) -->
                <div id="leftEye" class="absolute left-3 top-2.5 w-2 h-2 rounded-full transition-all duration-300" style="background: radial-gradient(circle, #60a5fa 0%, #3b82f6 70%, #1d4ed8 100%); box-shadow: 0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5), 0 1px 2px rgba(0,0,0,0.3);"></div>

                <div id="rightEye" class="absolute right-3 top-2.5 w-2 h-2 rounded-full transition-all duration-300" style="background: radial-gradient(circle, #60a5fa 0%, #3b82f6 70%, #1d4ed8 100%); box-shadow: 0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5), 0 1px 2px rgba(0,0,0,0.3);"></div>

                <!-- Robot Mouth -->
                <div id="robotMouth" class="absolute bottom-2 left-1/2 transform -translate-x-1/2 w-2 h-1 rounded-full transition-all duration-300" style="background: linear-gradient(145deg, #60a5fa 0%, #3b82f6 100%); box-shadow: 0 0 4px rgba(96,165,250,0.6), 0 1px 2px rgba(0,0,0,0.2);"></div>
            </div>

            <!-- Head Highlight -->
            <div class="absolute top-1 left-2 w-4 h-4 rounded-full pointer-events-none" style="background: radial-gradient(circle, rgba(255,255,255,0.6) 0%, transparent 70%);"></div>
        </div>

        <!-- Left Arm (Pointing Down with Enhanced Shadow) -->
        <div id="leftArm" class="absolute left-1 top-16 w-2.5 h-5 transform-gpu transition-transform duration-500 origin-top" style="border-radius: 50%; background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%); transform: rotate(15deg); box-shadow: 0 3px 10px rgba(0,0,0,0.2), 0 1px 4px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.6);"></div>

        <!-- Right Arm (Pointing Down with Enhanced Shadow) -->
        <div id="rightArm" class="absolute right-1 top-16 w-2.5 h-5 transform-gpu transition-transform duration-500 origin-top" style="border-radius: 50%; background: linear-gradient(145deg, #f8fafc 0%, #e2e8f0 50%, #cbd5e1 100%); transform: rotate(-15deg); box-shadow: 0 3px 10px rgba(0,0,0,0.2), 0 1px 4px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.6);"></div>

        <!-- Robot Body (White/Gray Smooth with Enhanced Shadow) -->
        <div class="relative w-10 h-12 mx-auto mt-0.5" style="border-radius: 50% 50% 50% 50% / 35% 35% 65% 65%; background: linear-gradient(145deg, #ffffff 0%, #f8fafc 30%, #e2e8f0 70%, #cbd5e1 100%); box-shadow: 0 8px 25px rgba(0,0,0,0.18), 0 4px 12px rgba(0,0,0,0.12), inset 0 1px 0 rgba(255,255,255,0.7), inset 0 -1px 0 rgba(0,0,0,0.08);">

            <!-- Glowing Blue Circle in Center of Body (Belly) -->
            <div id="centerCircle" class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-2.5 h-2.5 rounded-full transition-all duration-300" style="background: radial-gradient(circle, #60a5fa 0%, #3b82f6 70%, #1d4ed8 100%); box-shadow: 0 0 10px #60a5fa, 0 0 20px rgba(96,165,250,0.4), 0 2px 4px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.3);">
                <!-- Inner Glow -->
                <div class="absolute inset-0.5 rounded-full" style="background: radial-gradient(circle, rgba(255,255,255,0.4) 0%, transparent 60%);"></div>
            </div>

            <!-- Body Highlight -->
            <div class="absolute top-0.5 left-1.5 w-3 h-3 rounded-full pointer-events-none" style="background: radial-gradient(circle, rgba(255,255,255,0.5) 0%, transparent 70%);"></div>
        </div>
    </div>
</div>

<style>
/* Cute Robot Animations */
@keyframes robotFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-3px) rotate(1deg); }
    50% { transform: translateY(-5px) rotate(0deg); }
    75% { transform: translateY(-3px) rotate(-1deg); }
}

@keyframes shadowFloat {
    0%, 100% {
        transform: translate(-50%, 0) scale(1);
        opacity: 0.3;
    }
    25% {
        transform: translate(-50%, 0) scale(0.95);
        opacity: 0.25;
    }
    50% {
        transform: translate(-50%, 0) scale(0.9);
        opacity: 0.2;
    }
    75% {
        transform: translate(-50%, 0) scale(0.95);
        opacity: 0.25;
    }
}

@keyframes eyeBlink {
    0%, 90%, 100% { transform: scaleY(1); opacity: 1; }
    95% { transform: scaleY(0.1); opacity: 0.7; }
}

@keyframes eyeGlow {
    0%, 100% {
        box-shadow: 0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5);
    }
    50% {
        box-shadow: 0 0 12px #60a5fa, 0 0 25px rgba(96,165,250,0.7);
    }
}

@keyframes centerPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 0 10px #60a5fa, 0 0 20px rgba(96,165,250,0.4);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.1);
        box-shadow: 0 0 15px #60a5fa, 0 0 30px rgba(96,165,250,0.6);
    }
}

@keyframes armWave {
    0%, 100% { transform: rotate(-15deg); }
    50% { transform: rotate(-5deg); }
}

@keyframes armWaveLeft {
    0%, 100% { transform: rotate(15deg); }
    50% { transform: rotate(5deg); }
}

@keyframes robotBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Apply animations */
#followingRobot {
    animation: robotFloat 4s ease-in-out infinite;
    /* Add overall robot shadow */
    filter: drop-shadow(0 6px 20px rgba(0,0,0,0.15)) drop-shadow(0 2px 8px rgba(0,0,0,0.1));
}

#leftEye, #rightEye {
    animation: eyeBlink 5s ease-in-out infinite, eyeGlow 3s ease-in-out infinite;
}

#centerCircle {
    animation: centerPulse 2.5s ease-in-out infinite;
}

#leftArm {
    animation: armWaveLeft 3s ease-in-out infinite;
    transform-origin: top center;
}

#rightArm {
    animation: armWave 3s ease-in-out infinite 0.5s;
    transform-origin: top center;
}

/* Speaking mouth animation */
@keyframes mouthSpeak {
    0%, 100% {
        transform: translate(-50%, -50%) scaleX(1) scaleY(1);
    }
    50% {
        transform: translate(-50%, -50%) scaleX(1.3) scaleY(1.1);
    }
}

/* Hover effects */
#followingRobot:hover {
    animation: robotFloat 4s ease-in-out infinite, robotBounce 1s ease-in-out infinite;
}

#followingRobot:hover #leftArm {
    animation: armWaveLeft 1s ease-in-out infinite;
}

#followingRobot:hover #rightArm {
    animation: armWave 1s ease-in-out infinite 0.2s;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const robot = document.getElementById('followingRobot');
    const leftEye = document.getElementById('leftEye');
    const rightEye = document.getElementById('rightEye');
    const leftArm = document.getElementById('leftArm');
    const rightArm = document.getElementById('rightArm');
    const centerCircle = document.getElementById('centerCircle');
    const robotMouth = document.getElementById('robotMouth');

    // Enhanced blinking animation with expressions
    function enhancedBlink() {
        leftEye.style.animation = 'none';
        rightEye.style.animation = 'none';

        // Close eyes
        setTimeout(() => {
            leftEye.style.transform = 'scaleY(0.1)';
            rightEye.style.transform = 'scaleY(0.1)';
        }, 50);

        // Open eyes
        setTimeout(() => {
            leftEye.style.transform = 'scaleY(1)';
            rightEye.style.transform = 'scaleY(1)';
            leftEye.style.animation = 'eyeBlink 5s ease-in-out infinite, eyeGlow 3s ease-in-out infinite';
            rightEye.style.animation = 'eyeBlink 5s ease-in-out infinite, eyeGlow 3s ease-in-out infinite';
        }, 200);
    }

    // Happy expression
    function happyExpression() {
        leftEye.style.transform = 'scaleY(0.7) scaleX(1.2)';
        rightEye.style.transform = 'scaleY(0.7) scaleX(1.2)';
        robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1.5) scaleY(1.2)';
        robotMouth.style.borderRadius = '50% 50% 50% 50% / 100% 100% 20% 20%';

        setTimeout(() => {
            leftEye.style.transform = 'scaleY(1) scaleX(1)';
            rightEye.style.transform = 'scaleY(1) scaleX(1)';
            robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1) scaleY(1)';
            robotMouth.style.borderRadius = '50%';
        }, 2000);
    }

    // Excited wave animation
    function excitedWave() {
        leftArm.style.animation = 'none';
        rightArm.style.animation = 'none';

        // Fast wave sequence
        const waveSequence = [
            { left: 'rotate(45deg)', right: 'rotate(-45deg)' },
            { left: 'rotate(-15deg)', right: 'rotate(15deg)' },
            { left: 'rotate(60deg)', right: 'rotate(-60deg)' },
            { left: 'rotate(15deg)', right: 'rotate(-15deg)' }
        ];

        waveSequence.forEach((wave, index) => {
            setTimeout(() => {
                leftArm.style.transform = wave.left;
                rightArm.style.transform = wave.right;
            }, index * 200);
        });

        setTimeout(() => {
            leftArm.style.transform = 'rotate(15deg)';
            rightArm.style.transform = 'rotate(-15deg)';
            leftArm.style.animation = 'armWaveLeft 3s ease-in-out infinite';
            rightArm.style.animation = 'armWave 3s ease-in-out infinite 0.5s';
        }, 1000);
    }

    // Text-to-Speech function
    function speakText(text, lang = 'ar') {
        if ('speechSynthesis' in window) {
            // Cancel any ongoing speech
            speechSynthesis.cancel();

            const utterance = new SpeechSynthesisUtterance(text);

            // Set language based on current page language
            const currentLang = document.documentElement.lang || 'en';
            switch(currentLang) {
                case 'ar':
                    utterance.lang = 'ar-SA';
                    utterance.rate = 0.9;
                    break;
                case 'fr':
                    utterance.lang = 'fr-FR';
                    utterance.rate = 0.9;
                    break;
                default:
                    utterance.lang = 'en-US';
                    utterance.rate = 0.9;
            }

            utterance.pitch = 1.1;
            utterance.volume = 0.8;

            // Speaking animation
            utterance.onstart = function() {
                startSpeakingAnimation();
            };

            utterance.onend = function() {
                stopSpeakingAnimation();
            };

            speechSynthesis.speak(utterance);
        }
    }

    // Speaking animation
    function startSpeakingAnimation() {
        robotMouth.style.animation = 'mouthSpeak 0.3s ease-in-out infinite';
        leftEye.style.transform = 'scaleY(0.8)';
        rightEye.style.transform = 'scaleY(0.8)';
    }

    function stopSpeakingAnimation() {
        robotMouth.style.animation = 'none';
        leftEye.style.transform = 'scaleY(1)';
        rightEye.style.transform = 'scaleY(1)';
    }

    // Welcome greeting with voice and gestures
    function welcomeGreeting(withVoice = true) {
        // Smiling expression
        leftEye.style.transform = 'scaleY(0.7) scaleX(1.2)';
        rightEye.style.transform = 'scaleY(0.7) scaleX(1.2)';
        robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1.8) scaleY(1.3)';
        robotMouth.style.borderRadius = '50% 50% 50% 50% / 100% 100% 30% 30%';

        // Welcome wave gesture
        welcomeWaveGesture();

        // Enhanced glow
        centerCircle.style.boxShadow = '0 0 20px #60a5fa, 0 0 40px rgba(96,165,250,0.8)';
        leftEye.style.boxShadow = '0 0 15px #60a5fa, 0 0 30px rgba(96,165,250,0.8)';
        rightEye.style.boxShadow = '0 0 15px #60a5fa, 0 0 30px rgba(96,165,250,0.8)';
        robotMouth.style.boxShadow = '0 0 10px #60a5fa, 0 0 20px rgba(96,165,250,0.8)';

        // Robot bounce
        robot.style.transform = 'scale(1.1)';
        robot.style.transition = 'transform 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55)';

        // Voice greeting
        if (withVoice) {
            const welcomeText = getWelcomeVoiceMessage();
            setTimeout(() => speakText(welcomeText), 500);
        }

        setTimeout(() => {
            robot.style.transform = 'scale(1)';
            leftEye.style.transform = 'scaleY(1) scaleX(1)';
            rightEye.style.transform = 'scaleY(1) scaleX(1)';
            robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1) scaleY(1)';
            robotMouth.style.borderRadius = '50%';
            centerCircle.style.boxShadow = '0 0 10px #60a5fa, 0 0 20px rgba(96,165,250,0.4)';
            leftEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
            rightEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
            robotMouth.style.boxShadow = '0 0 4px rgba(96,165,250,0.6)';
        }, 3000);
    }

    // Welcome wave gesture
    function welcomeWaveGesture() {
        leftArm.style.animation = 'none';
        rightArm.style.animation = 'none';

        // Friendly welcome wave sequence
        const waveSequence = [
            { left: 'rotate(-30deg)', right: 'rotate(30deg)' },
            { left: 'rotate(45deg)', right: 'rotate(-45deg)' },
            { left: 'rotate(-15deg)', right: 'rotate(15deg)' },
            { left: 'rotate(60deg)', right: 'rotate(-60deg)' },
            { left: 'rotate(0deg)', right: 'rotate(0deg)' },
            { left: 'rotate(45deg)', right: 'rotate(-45deg)' },
            { left: 'rotate(15deg)', right: 'rotate(-15deg)' }
        ];

        waveSequence.forEach((wave, index) => {
            setTimeout(() => {
                leftArm.style.transform = wave.left;
                rightArm.style.transform = wave.right;
            }, index * 300);
        });

        setTimeout(() => {
            leftArm.style.transform = 'rotate(15deg)';
            rightArm.style.transform = 'rotate(-15deg)';
            leftArm.style.animation = 'armWaveLeft 3s ease-in-out infinite';
            rightArm.style.animation = 'armWave 3s ease-in-out infinite 0.5s';
        }, 2500);
    }

    // Laughing expression
    function laughExpression() {
        // Eyes become crescents (laughing eyes)
        leftEye.style.transform = 'scaleY(0.3) scaleX(1.3)';
        rightEye.style.transform = 'scaleY(0.3) scaleX(1.3)';
        leftEye.style.borderRadius = '50% 50% 50% 50% / 100% 100% 20% 20%';
        rightEye.style.borderRadius = '50% 50% 50% 50% / 100% 100% 20% 20%';

        // Mouth becomes big smile
        robotMouth.style.transform = 'translate(-50%, -50%) scaleX(2) scaleY(1.5)';
        robotMouth.style.borderRadius = '50% 50% 50% 50% / 100% 100% 30% 30%';
        robotMouth.style.boxShadow = '0 0 12px #60a5fa, 0 0 25px rgba(96,165,250,0.8)';

        // Body shake with laughter
        robot.style.animation = 'robotFloat 4s ease-in-out infinite, robotBounce 0.3s ease-in-out infinite';

        setTimeout(() => {
            leftEye.style.transform = 'scaleY(1) scaleX(1)';
            rightEye.style.transform = 'scaleY(1) scaleX(1)';
            leftEye.style.borderRadius = '50%';
            rightEye.style.borderRadius = '50%';
            robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1) scaleY(1)';
            robotMouth.style.borderRadius = '50%';
            robotMouth.style.boxShadow = '0 0 4px rgba(96,165,250,0.6)';
            robot.style.animation = 'robotFloat 4s ease-in-out infinite';
        }, 3000);
    }

    // Winking expression
    function winkExpression() {
        // Left eye winks
        leftEye.style.transform = 'scaleY(0.1)';
        rightEye.style.transform = 'scale(1.2)';

        // Mouth smiles
        robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1.4) rotate(5deg)';

        setTimeout(() => {
            leftEye.style.transform = 'scaleY(1)';
            rightEye.style.transform = 'scale(1)';
            robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1) rotate(0deg)';
        }, 1500);
    }

    // Surprised expression
    function surprisedExpression() {
        // Eyes become big
        leftEye.style.transform = 'scale(1.5)';
        rightEye.style.transform = 'scale(1.5)';

        // Mouth becomes small circle
        robotMouth.style.transform = 'translate(-50%, -50%) scale(0.7)';
        robotMouth.style.borderRadius = '50%';

        // Quick bounce
        robot.style.transform = 'scale(1.05)';

        setTimeout(() => {
            leftEye.style.transform = 'scale(1)';
            rightEye.style.transform = 'scale(1)';
            robotMouth.style.transform = 'translate(-50%, -50%) scale(1)';
            robot.style.transform = 'scale(1)';
        }, 2000);
    }

    // Simple random expressions (no voice)
    setInterval(() => {
        const randomAction = Math.random();
        if (randomAction < 0.3) {
            enhancedBlink();
        } else if (randomAction < 0.5) {
            happyExpression();
        } else if (randomAction < 0.7) {
            winkExpression();
        } else {
            excitedWave();
        }
    }, 6000); // Every 6 seconds

    // Check if this is first visit to website
    function checkFirstVisit() {
        const hasVisited = sessionStorage.getItem('monori_robot_welcomed');
        if (!hasVisited) {
            // First visit - do welcome
            setTimeout(() => {
                welcomeToWebsite();
                sessionStorage.setItem('monori_robot_welcomed', 'true');
            }, 3000);
        }
    }

    // Check for first visit
    checkFirstVisit();

    // Robot movement function
    function moveRobotToRandomPosition() {
        // Get viewport dimensions
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Robot dimensions
        const robotWidth = 80; // 20 * 4 (w-20)
        const robotHeight = 96; // 24 * 4 (h-24)

        // Safe boundaries (avoid footer and edges)
        const minX = 20; // 20px from left
        const maxX = viewportWidth - robotWidth - 20; // 20px from right
        const minY = 20; // 20px from top
        const maxY = viewportHeight - robotHeight - 150; // 150px from bottom (avoid footer)

        // Generate random position
        const randomX = Math.random() * (maxX - minX) + minX;
        const randomY = Math.random() * (maxY - minY) + minY;

        // Smooth movement animation
        robot.style.transition = 'all 2s cubic-bezier(0.25, 0.46, 0.45, 0.94)';
        robot.style.left = randomX + 'px';
        robot.style.top = randomY + 'px';
        robot.style.right = 'auto'; // Remove right positioning

        // Happy expression during movement (no text messages)
        setTimeout(() => {
            happyExpression();
            excitedWave();
        }, 1000);

        // Reset transition after movement
        setTimeout(() => {
            robot.style.transition = 'transform 0.3s ease';
        }, 2500);
    }

    // Auto movement every 15 seconds (between 10-20 seconds)
    setInterval(() => {
        const randomDelay = Math.random() * 10000 + 10000; // 10-20 seconds
        setTimeout(() => {
            moveRobotToRandomPosition();
        }, randomDelay);
    }, 15000);

    // Initial movement after welcome
    setTimeout(() => {
        moveRobotToRandomPosition();
    }, 8000);

    // Robot click handler with cute interactions
    function handleRobotClick() {
        // Cute bounce effect
        robot.style.transform = 'scale(1.15)';
        robot.style.transition = 'transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
        setTimeout(() => {
            robot.style.transform = 'scale(1)';
        }, 300);

        // Arms wave enthusiastically (pointing down)
        leftArm.style.transform = 'rotate(45deg)';
        rightArm.style.transform = 'rotate(-45deg)';
        setTimeout(() => {
            leftArm.style.transform = 'rotate(15deg)';
            rightArm.style.transform = 'rotate(-15deg)';
        }, 600);

        // Center circle enhanced glow
        centerCircle.style.boxShadow = '0 0 20px #60a5fa, 0 0 40px rgba(96,165,250,0.8)';
        centerCircle.style.transform = 'translate(-50%, -50%) scale(1.3)';
        setTimeout(() => {
            centerCircle.style.boxShadow = '0 0 10px #60a5fa, 0 0 20px rgba(96,165,250,0.4)';
            centerCircle.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 1000);

        // Eyes glow brighter
        leftEye.style.boxShadow = '0 0 15px #60a5fa, 0 0 30px rgba(96,165,250,0.8)';
        rightEye.style.boxShadow = '0 0 15px #60a5fa, 0 0 30px rgba(96,165,250,0.8)';

        // Mouth glows when speaking
        robotMouth.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.8)';
        robotMouth.style.transform = 'translate(-50%, -50%) scale(1.2)';

        setTimeout(() => {
            leftEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
            rightEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
            robotMouth.style.boxShadow = '0 0 4px rgba(96,165,250,0.6)';
            robotMouth.style.transform = 'translate(-50%, -50%) scale(1)';
        }, 1000);

        // Enhanced blink
        enhancedBlink();

        // Get current language
        const currentLang = document.documentElement.lang || 'en';
        let greeting;

        switch(currentLang) {
            case 'ar':
                greeting = 'مرحباً! كيف يمكنني مساعدتك؟ 🤖✨';
                break;
            case 'fr':
                greeting = 'Bonjour! Comment puis-je vous aider? 🤖✨';
                break;
            default:
                greeting = 'Hello! How can I help you? 🤖✨';
        }

        // Open chat/help system on click
        openChatSystem();
    }

    // Removed unused message functions

    // Open chat/help system
    function openChatSystem() {
        // Check if AI Chat exists and open it
        if (window.aiChat) {
            window.aiChat.openChatWindow();

            // Add special greeting message when robot is clicked
            setTimeout(() => {
                const currentLang = document.documentElement.lang || 'ar';
                let greetingMessage;

                switch(currentLang) {
                    case 'ar':
                        greetingMessage = 'مرحباً! فاش بغيتيني نساعدك؟ 😊';
                        break;
                    case 'fr':
                        greetingMessage = 'Bonjour! Comment puis-je vous aider? 😊';
                        break;
                    default:
                        greetingMessage = 'Hello! How can I help you? 😊';
                }

                window.aiChat.addMessage(greetingMessage, 'ai');
                window.aiChat.speakMessage(greetingMessage);
            }, 500);
            return;
        }

        // Check if floating robot chat exists
        const floatingRobotChat = document.getElementById('chatWindow');
        if (floatingRobotChat) {
            // If floating robot chat exists, open it
            const floatingRobotButton = document.getElementById('robotButton');
            if (floatingRobotButton) {
                floatingRobotButton.click();
                return;
            }
        }

        // Otherwise create a simple help modal
        createHelpModal();
    }

    // Create help modal
    function createHelpModal() {
        // Remove existing modal
        const existingModal = document.getElementById('robotHelpModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Get current language
        const currentLang = document.documentElement.lang || 'en';
        const helpContent = getHelpContent(currentLang);

        // Create modal
        const modal = document.createElement('div');
        modal.id = 'robotHelpModal';
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.style.zIndex = '2147483648';

        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-xl p-6 max-w-md mx-4 shadow-2xl transform transition-all duration-300 scale-95 opacity-0">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-xl font-bold text-gray-800 dark:text-white flex items-center">
                        <span class="mr-2">🤖</span>
                        ${helpContent.title}
                    </h3>
                    <button id="closeHelpModal" class="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 text-2xl">
                        ×
                    </button>
                </div>
                <div class="text-gray-600 dark:text-gray-300 space-y-3">
                    ${helpContent.content}
                </div>
                <div class="mt-6 flex justify-end">
                    <button id="closeHelpModalBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors">
                        ${helpContent.closeBtn}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Animate in
        setTimeout(() => {
            const modalContent = modal.querySelector('div > div');
            modalContent.style.transform = 'scale(1)';
            modalContent.style.opacity = '1';
        }, 50);

        // Close modal events
        const closeModal = () => {
            const modalContent = modal.querySelector('div > div');
            modalContent.style.transform = 'scale(0.95)';
            modalContent.style.opacity = '0';
            setTimeout(() => modal.remove(), 300);
        };

        modal.addEventListener('click', (e) => {
            if (e.target === modal) closeModal();
        });

        document.getElementById('closeHelpModal').addEventListener('click', closeModal);
        document.getElementById('closeHelpModalBtn').addEventListener('click', closeModal);
    }

    // Get help content based on language
    function getHelpContent(lang) {
        const content = {
            'ar': {
                title: 'مساعد MonOri AI',
                content: `
                    <p><strong>مرحباً! أنا مساعدك الذكي 🤖</strong></p>
                    <p>يمكنني مساعدتك في:</p>
                    <ul class="list-disc list-inside space-y-1 mt-2">
                        <li>إنشاء السيرة الذاتية</li>
                        <li>تحليل الشخصية</li>
                        <li>البحث عن الوظائف</li>
                        <li>التدريب على المقابلات</li>
                        <li>الإجابة على أسئلتك</li>
                    </ul>
                    <p class="mt-3 text-sm">💬 تواصل معي في أي وقت!</p>
                `,
                closeBtn: 'حسناً'
            },
            'fr': {
                title: 'Assistant MonOri AI',
                content: `
                    <p><strong>Salut! Je suis votre assistant intelligent 🤖</strong></p>
                    <p>Je peux vous aider avec:</p>
                    <ul class="list-disc list-inside space-y-1 mt-2">
                        <li>Création de CV</li>
                        <li>Analyse de personnalité</li>
                        <li>Recherche d'emploi</li>
                        <li>Formation aux entretiens</li>
                        <li>Répondre à vos questions</li>
                    </ul>
                    <p class="mt-3 text-sm">💬 Contactez-moi à tout moment!</p>
                `,
                closeBtn: 'D\'accord'
            },
            'en': {
                title: 'MonOri AI Assistant',
                content: `
                    <p><strong>Hi! I'm your smart assistant 🤖</strong></p>
                    <p>I can help you with:</p>
                    <ul class="list-disc list-inside space-y-1 mt-2">
                        <li>CV/Resume creation</li>
                        <li>Personality analysis</li>
                        <li>Job searching</li>
                        <li>Interview training</li>
                        <li>Answering your questions</li>
                    </ul>
                    <p class="mt-3 text-sm">💬 Contact me anytime!</p>
                `,
                closeBtn: 'Got it'
            }
        };

        return content[lang] || content['en'];
    }

    // Mouse hover effects with dynamic interactions
    robot.addEventListener('mouseenter', function() {
        // Happy expression on hover
        leftEye.style.transform = 'scale(1.1) scaleY(0.8)';
        rightEye.style.transform = 'scale(1.1) scaleY(0.8)';
        robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1.3)';

        // Enhanced glow effects
        centerCircle.style.boxShadow = '0 0 15px #60a5fa, 0 0 30px rgba(96,165,250,0.6)';
        leftEye.style.boxShadow = '0 0 12px #60a5fa, 0 0 25px rgba(96,165,250,0.7)';
        rightEye.style.boxShadow = '0 0 12px #60a5fa, 0 0 25px rgba(96,165,250,0.7)';
        robotMouth.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.7)';

        // Friendly wave (pointing down)
        leftArm.style.transform = 'rotate(25deg)';
        rightArm.style.transform = 'rotate(-25deg)';

        // Add enhanced body glow and shadow
        robot.style.filter = 'drop-shadow(0 0 15px rgba(96,165,250,0.4)) drop-shadow(0 8px 25px rgba(0,0,0,0.2))';

        // Enhance ground shadows
        const mainShadow = document.getElementById('mainShadow');
        const secondaryShadow = document.getElementById('secondaryShadow');
        const coreShadow = document.getElementById('coreShadow');

        if (mainShadow) mainShadow.style.opacity = '0.4';
        if (secondaryShadow) secondaryShadow.style.opacity = '0.5';
        if (coreShadow) coreShadow.style.opacity = '0.6';

        // No hover messages - just visual effects
    });

    robot.addEventListener('mouseleave', function() {
        // Reset facial expression
        leftEye.style.transform = 'scale(1) scaleY(1)';
        rightEye.style.transform = 'scale(1) scaleY(1)';
        robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1)';

        // Reset glow effects
        centerCircle.style.boxShadow = '0 0 10px #60a5fa, 0 0 20px rgba(96,165,250,0.4)';
        leftEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
        rightEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
        robotMouth.style.boxShadow = '0 0 4px rgba(96,165,250,0.6)';

        // Arms return to normal (pointing down)
        leftArm.style.transform = 'rotate(15deg)';
        rightArm.style.transform = 'rotate(-15deg)';

        // Reset shadows and glow
        robot.style.filter = 'drop-shadow(0 6px 20px rgba(0,0,0,0.15)) drop-shadow(0 2px 8px rgba(0,0,0,0.1))';

        // Reset ground shadows
        const mainShadow = document.getElementById('mainShadow');
        const secondaryShadow = document.getElementById('secondaryShadow');
        const coreShadow = document.getElementById('coreShadow');

        if (mainShadow) mainShadow.style.opacity = '1';
        if (secondaryShadow) secondaryShadow.style.opacity = '1';
        if (coreShadow) coreShadow.style.opacity = '1';
    });

    // Get hover message based on language
    // Removed unused hover message function

    // Get laugh message based on language
    function getLaughMessage() {
        const currentLang = document.documentElement.lang || 'en';
        const laughMessages = {
            'ar': [
                'هههههه! أحب أن أضحك معكم! 😂',
                'هاهاها! الحياة جميلة! 😄',
                'ههههه! أنتم رائعون! 🤣',
                'هاهاها! دعونا نستمتع! 😆'
            ],
            'fr': [
                'Hahaha! J\'adore rire avec vous! 😂',
                'Héhéhé! La vie est belle! 😄',
                'Hihihi! Vous êtes géniaux! 🤣',
                'Hahaha! Amusons-nous! 😆'
            ],
            'en': [
                'Hahaha! I love laughing with you! 😂',
                'Hehehe! Life is beautiful! 😄',
                'Hihihi! You guys are awesome! 🤣',
                'Hahaha! Let\'s have fun! 😆'
            ]
        };

        const messages = laughMessages[currentLang] || laughMessages['en'];
        return messages[Math.floor(Math.random() * messages.length)];
    }

    // Get random engagement message
    function getRandomEngagementMessage() {
        const currentLang = document.documentElement.lang || 'en';
        const engagementMessages = {
            'ar': [
                'هل تحتاج مساعدة في شيء؟ 🤔',
                'أنا هنا إذا كنت تريد التحدث! 💬',
                'كيف يمكنني جعل يومك أفضل؟ ✨',
                'هل تريد معرفة المزيد عن خدماتنا؟ 🚀',
                'أحب التحدث مع الزوار الجدد! 😊',
                'هل لديك أسئلة؟ أنا هنا للمساعدة! 🤖'
            ],
            'fr': [
                'Avez-vous besoin d\'aide? 🤔',
                'Je suis là si vous voulez parler! 💬',
                'Comment puis-je améliorer votre journée? ✨',
                'Voulez-vous en savoir plus sur nos services? 🚀',
                'J\'adore parler aux nouveaux visiteurs! 😊',
                'Des questions? Je suis là pour aider! 🤖'
            ],
            'en': [
                'Need help with anything? 🤔',
                'I\'m here if you want to chat! 💬',
                'How can I make your day better? ✨',
                'Want to know more about our services? 🚀',
                'I love talking to new visitors! 😊',
                'Any questions? I\'m here to help! 🤖'
            ]
        };

        const messages = engagementMessages[currentLang] || engagementMessages['en'];
        return messages[Math.floor(Math.random() * messages.length)];
    }

    // Mouse movement interaction
    let lastMouseMove = 0;
    document.addEventListener('mousemove', function(e) {
        const now = Date.now();
        if (now - lastMouseMove > 2000) { // Every 2 seconds max
            const robotRect = robot.getBoundingClientRect();
            const robotCenterX = robotRect.left + robotRect.width / 2;
            const robotCenterY = robotRect.top + robotRect.height / 2;

            const mouseX = e.clientX;
            const mouseY = e.clientY;

            // Calculate distance
            const distance = Math.sqrt(Math.pow(mouseX - robotCenterX, 2) + Math.pow(mouseY - robotCenterY, 2));

            // If mouse is close to robot, react
            if (distance < 200) {
                const reactions = [winkExpression, happyExpression, surprisedExpression];
                const randomReaction = reactions[Math.floor(Math.random() * reactions.length)];
                randomReaction();

                if (Math.random() < 0.3) {
                    setTimeout(() => showRobotMessage(getMouseInteractionMessage()), 500);
                }
            }
            lastMouseMove = now;
        }
    });

    // Get mouse interaction message
    function getMouseInteractionMessage() {
        const currentLang = document.documentElement.lang || 'en';
        const mouseMessages = {
            'ar': [
                'رأيتك هناك! 👀',
                'مرحباً أيها الصديق! 👋',
                'أحب عندما تقترب! 😊'
            ],
            'fr': [
                'Je vous ai vu là! 👀',
                'Salut mon ami! 👋',
                'J\'aime quand vous vous approchez! 😊'
            ],
            'en': [
                'I saw you there! 👀',
                'Hey there friend! 👋',
                'I love when you come close! 😊'
            ]
        };

        const messages = mouseMessages[currentLang] || mouseMessages['en'];
        return messages[Math.floor(Math.random() * messages.length)];
    }

    // Page visibility change interaction (no messages)
    document.addEventListener('visibilitychange', function() {
        if (!document.hidden) {
            // User came back to page - just visual reaction
            setTimeout(() => {
                happyExpression();
                excitedWave();
            }, 1000);
        }
    });

    // Get welcome back message
    function getWelcomeBackMessage() {
        const currentLang = document.documentElement.lang || 'en';
        const welcomeBackMessages = {
            'ar': [
                'أهلاً بعودتك! افتقدتك! 🤗',
                'مرحباً مرة أخرى! كيف حالك؟ 😊',
                'عدت! أنا سعيد لرؤيتك! 🎉'
            ],
            'fr': [
                'Bon retour! Vous m\'avez manqué! 🤗',
                'Salut encore! Comment allez-vous? 😊',
                'Vous êtes de retour! Je suis content de vous voir! 🎉'
            ],
            'en': [
                'Welcome back! I missed you! 🤗',
                'Hello again! How are you? 😊',
                'You\'re back! I\'m happy to see you! 🎉'
            ]
        };

        const messages = welcomeBackMessages[currentLang] || welcomeBackMessages['en'];
        return messages[Math.floor(Math.random() * messages.length)];
    }

    // Welcome to website function
    function welcomeToWebsite() {
        // Big smile expression
        leftEye.style.transform = 'scaleY(0.6) scaleX(1.3)';
        rightEye.style.transform = 'scaleY(0.6) scaleX(1.3)';
        robotMouth.style.transform = 'translate(-50%, -50%) scaleX(2.2) scaleY(1.5)';
        robotMouth.style.borderRadius = '50% 50% 50% 50% / 100% 100% 20% 20%';

        // Welcome wave gesture
        welcomeWaveGesture();

        // Enhanced glow
        centerCircle.style.boxShadow = '0 0 25px #60a5fa, 0 0 50px rgba(96,165,250,0.9)';
        leftEye.style.boxShadow = '0 0 20px #60a5fa, 0 0 40px rgba(96,165,250,0.9)';
        rightEye.style.boxShadow = '0 0 20px #60a5fa, 0 0 40px rgba(96,165,250,0.9)';
        robotMouth.style.boxShadow = '0 0 15px #60a5fa, 0 0 30px rgba(96,165,250,0.9)';

        // Robot bounce
        robot.style.transform = 'scale(1.15)';
        robot.style.transition = 'transform 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)';

        // Get welcome message and speak it (no text message)
        const welcomeMessage = getWebsiteWelcomeMessage();

        // Only speak the welcome message
        setTimeout(() => speakText(welcomeMessage.voice), 800);

        // Reset after welcome
        setTimeout(() => {
            robot.style.transform = 'scale(1)';
            leftEye.style.transform = 'scaleY(1) scaleX(1)';
            rightEye.style.transform = 'scaleY(1) scaleX(1)';
            robotMouth.style.transform = 'translate(-50%, -50%) scaleX(1) scaleY(1)';
            robotMouth.style.borderRadius = '50%';
            centerCircle.style.boxShadow = '0 0 10px #60a5fa, 0 0 20px rgba(96,165,250,0.4)';
            leftEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
            rightEye.style.boxShadow = '0 0 8px #60a5fa, 0 0 15px rgba(96,165,250,0.5)';
            robotMouth.style.boxShadow = '0 0 4px rgba(96,165,250,0.6)';
        }, 4000);
    }

    // Get website welcome message (same voice message for all languages)
    function getWebsiteWelcomeMessage() {
        const currentLang = document.documentElement.lang || 'en';

        const messages = {
            'ar': {
                text: 'مرحباً بكم في موقع MonOri AI 🎉✨',
                voice: 'مرحباً بكم في موقع مونوري إيه آي'
            },
            'fr': {
                text: 'Bienvenue sur le site MonOri AI 🎉✨',
                voice: 'Bienvenue sur le site MonOri AI'
            },
            'en': {
                text: 'Welcome to MonOri AI website 🎉✨',
                voice: 'Welcome to MonOri AI website'
            }
        };

        return messages[currentLang] || messages['en'];
    }

    // Get movement message
    function getMovementMessage() {
        const currentLang = document.documentElement.lang || 'en';
        const movementMessages = {
            'ar': [
                'أحب استكشاف الصفحة! 🚀',
                'دعني أتجول قليلاً! 😊',
                'أنا أتحرك لأكون أقرب إليك! 👋',
                'مكان جديد، منظر جديد! ✨'
            ],
            'fr': [
                'J\'aime explorer la page! 🚀',
                'Laissez-moi me promener! 😊',
                'Je bouge pour être plus près de vous! 👋',
                'Nouvel endroit, nouvelle vue! ✨'
            ],
            'en': [
                'I love exploring the page! 🚀',
                'Let me move around a bit! 😊',
                'I\'m moving to be closer to you! 👋',
                'New place, new view! ✨'
            ]
        };

        const messages = movementMessages[currentLang] || movementMessages['en'];
        return messages[Math.floor(Math.random() * messages.length)];
    }

    // Get random voice interaction message
    function getVoiceInteractionMessage() {
        const currentLang = document.documentElement.lang || 'en';
        const voiceInteractions = {
            'ar': [
                'هل تحتاج مساعدة؟',
                'أنا هنا لخدمتك',
                'كيف حالك اليوم؟',
                'هل تريد معرفة المزيد عن خدماتنا؟'
            ],
            'fr': [
                'Avez-vous besoin d\'aide?',
                'Je suis là pour vous servir',
                'Comment allez-vous aujourd\'hui?',
                'Voulez-vous en savoir plus sur nos services?'
            ],
            'en': [
                'Do you need any help?',
                'I am here to serve you',
                'How are you doing today?',
                'Would you like to know more about our services?'
            ]
        };

        const messages = voiceInteractions[currentLang] || voiceInteractions['en'];
        return messages[Math.floor(Math.random() * messages.length)];
    }

    // Handle window resize to keep robot in safe area
    window.addEventListener('resize', function() {
        const currentLeft = parseInt(robot.style.left) || 0;
        const currentTop = parseInt(robot.style.top) || 0;

        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const robotWidth = 80;
        const robotHeight = 96;

        // Check if robot is out of bounds and move it back
        const maxX = viewportWidth - robotWidth - 20;
        const maxY = viewportHeight - robotHeight - 150;

        if (currentLeft > maxX || currentTop > maxY) {
            const safeX = Math.min(currentLeft, maxX);
            const safeY = Math.min(currentTop, maxY);

            robot.style.transition = 'all 1s ease';
            robot.style.left = safeX + 'px';
            robot.style.top = safeY + 'px';
        }
    });

    // Event listeners
    robot.addEventListener('click', handleRobotClick);
});
</script>
