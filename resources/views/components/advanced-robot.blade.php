<!-- Advanced Moving Robot -->
<div id="advancedRobot" class="fixed z-[99999]" style="bottom: 100px; right: 100px; transition: all 0.8s ease-in-out;">
    <!-- Robot Container -->
    <div id="robotContainer" class="relative cursor-pointer group">
        <!-- Robot Body -->
        <div id="robotBody" class="w-24 h-32 relative transform transition-all duration-500 hover:scale-110" style="animation: robotFloat 4s ease-in-out infinite;">
            
            <!-- Robot Head -->
            <div class="w-20 h-20 mx-auto rounded-full relative" style="background: linear-gradient(135deg, #1E3A8A, #3B82F6, #60A5FA); box-shadow: 0 8px 32px rgba(59, 130, 246, 0.4);">
                <!-- Head Shine -->
                <div class="absolute inset-2 rounded-full" style="background: linear-gradient(135deg, rgba(255,255,255,0.3), transparent);"></div>
                
                <!-- Robot Eyes -->
                <div class="absolute top-4 left-3 w-4 h-4 rounded-full" style="background: linear-gradient(135deg, #00D4FF, #0099CC);">
                    <div id="leftEye" class="w-3 h-3 bg-white rounded-full mt-0.5 ml-0.5 transition-all duration-300" style="animation: robotBlink 5s ease-in-out infinite;">
                        <div class="w-2 h-2 bg-blue-600 rounded-full mt-0.5 ml-0.5"></div>
                    </div>
                </div>
                <div class="absolute top-4 right-3 w-4 h-4 rounded-full" style="background: linear-gradient(135deg, #00D4FF, #0099CC);">
                    <div id="rightEye" class="w-3 h-3 bg-white rounded-full mt-0.5 ml-0.5 transition-all duration-300" style="animation: robotBlink 5s ease-in-out infinite;">
                        <div class="w-2 h-2 bg-blue-600 rounded-full mt-0.5 ml-0.5"></div>
                    </div>
                </div>
                
                <!-- Robot Mouth -->
                <div id="robotMouth" class="absolute bottom-3 left-1/2 transform -translate-x-1/2 w-6 h-3 rounded-full transition-all duration-300" style="background: linear-gradient(135deg, #00D4FF, #0066CC);">
                    <div class="w-full h-full bg-gray-800 rounded-full transform scale-75 mt-0.5"></div>
                </div>
                
                <!-- Robot Antenna -->
                <div class="absolute -top-3 left-1/2 transform -translate-x-1/2 w-1 h-8 bg-gray-300 rounded-full">
                    <div id="robotAntenna" class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-4 h-4 rounded-full animate-pulse" style="background: radial-gradient(circle, #FF4444, #CC0000);"></div>
                </div>
            </div>
            
            <!-- Robot Body -->
            <div class="w-16 h-12 mx-auto mt-1 rounded-lg relative" style="background: linear-gradient(135deg, #E5E7EB, #F3F4F6, #FFFFFF); box-shadow: 0 4px 16px rgba(0,0,0,0.2);">
                <!-- Body Details -->
                <div class="absolute top-2 left-1/2 transform -translate-x-1/2 w-8 h-2 bg-blue-500 rounded-full"></div>
                <div class="absolute bottom-2 left-2 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <div class="absolute bottom-2 right-2 w-2 h-2 bg-red-400 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
            </div>
            
            <!-- Robot Arms -->
            <div id="leftArm" class="absolute top-16 -left-4 w-6 h-3 rounded-full transform rotate-45 transition-all duration-500" style="background: linear-gradient(135deg, #E5E7EB, #F3F4F6);"></div>
            <div id="rightArm" class="absolute top-16 -right-4 w-6 h-3 rounded-full transform -rotate-45 transition-all duration-500" style="background: linear-gradient(135deg, #E5E7EB, #F3F4F6);"></div>
            
            <!-- Robot Legs -->
            <div class="absolute bottom-0 left-3 w-3 h-6 rounded-full" style="background: linear-gradient(135deg, #E5E7EB, #F3F4F6);"></div>
            <div class="absolute bottom-0 right-3 w-3 h-6 rounded-full" style="background: linear-gradient(135deg, #E5E7EB, #F3F4F6);"></div>
            
            <!-- Speaking Animation -->
            <div id="speakingRings" class="absolute inset-0 opacity-0 transition-opacity duration-300">
                <div class="absolute inset-4 border-4 border-blue-400/40 rounded-full animate-ping"></div>
                <div class="absolute inset-8 border-4 border-blue-300/30 rounded-full animate-ping" style="animation-delay: 0.3s"></div>
                <div class="absolute inset-12 border-4 border-blue-200/20 rounded-full animate-ping" style="animation-delay: 0.6s"></div>
            </div>
        </div>
        
        <!-- Speech Bubble -->
        <div id="speechBubble" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-4 px-4 py-2 bg-white rounded-2xl shadow-2xl opacity-0 transition-all duration-500 whitespace-nowrap border-2 border-blue-200">
            <div id="speechText" class="text-gray-800 font-medium text-sm">مرحباً! 👋</div>
            <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-8 border-r-8 border-t-8 border-transparent border-t-white"></div>
        </div>
        
        <!-- Notification Badge -->
        <div id="notificationBadge" class="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold animate-bounce hidden">
            💬
        </div>
    </div>
    
    <!-- Chat Window -->
    <div id="chatWindow" class="absolute bottom-full right-0 mb-4 w-80 h-96 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform scale-0 origin-bottom-right transition-all duration-500 opacity-0 border border-gray-200 dark:border-gray-700">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-white/20 rounded-full mr-3 flex items-center justify-center">
                        🤖
                    </div>
                    <div>
                        <h3 id="chatTitle" class="font-semibold text-sm">مساعد MonOri الذكي</h3>
                        <p id="chatStatus" class="text-xs text-white/80">متصل الآن</p>
                    </div>
                </div>
                <button id="closeChatBtn" class="text-white/80 hover:text-white transition-colors">
                    ✕
                </button>
            </div>
        </div>
        
        <!-- Chat Messages -->
        <div id="chatMessages" class="h-64 p-4 overflow-y-auto space-y-3">
            <div class="flex items-start">
                <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p id="welcomeMessage" class="text-gray-900 dark:text-white text-sm">مرحباً! أنا مساعدك الذكي في MonOri AI. كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex space-x-2 rtl:space-x-reverse">
                <input type="text" id="chatInput" placeholder="اكتب سؤالك هنا..." 
                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm">
                <button id="sendChatBtn" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    ➤
                </button>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="px-4 pb-4">
            <div class="flex flex-wrap gap-2">
                <button class="quick-action-btn text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors" data-question="services">
                    <span class="ar">خدماتنا</span><span class="en hidden">Our Services</span><span class="fr hidden">Nos Services</span>
                </button>
                <button class="quick-action-btn text-xs bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full hover:bg-purple-200 dark:hover:bg-purple-900/40 transition-colors" data-question="cv">
                    <span class="ar">إنشاء CV</span><span class="en hidden">Create CV</span><span class="fr hidden">Créer CV</span>
                </button>
                <button class="quick-action-btn text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors" data-question="pricing">
                    <span class="ar">الأسعار</span><span class="en hidden">Pricing</span><span class="fr hidden">Prix</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Advanced Robot Animations */
@keyframes robotFloat {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg) scale(1); 
    }
    25% { 
        transform: translateY(-8px) rotate(1deg) scale(1.02); 
    }
    50% { 
        transform: translateY(-15px) rotate(2deg) scale(1.05); 
    }
    75% { 
        transform: translateY(-8px) rotate(1deg) scale(1.02); 
    }
}

@keyframes robotBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes robotWave {
    0%, 100% { transform: rotate(45deg) scale(1); }
    25% { transform: rotate(60deg) scale(1.1); }
    50% { transform: rotate(90deg) scale(1.2); }
    75% { transform: rotate(60deg) scale(1.1); }
}

@keyframes robotMove {
    0% { transform: translateX(0px) translateY(0px); }
    25% { transform: translateX(20px) translateY(-10px); }
    50% { transform: translateX(0px) translateY(-20px); }
    75% { transform: translateX(-20px) translateY(-10px); }
    100% { transform: translateX(0px) translateY(0px); }
}

@keyframes robotBounce {
    0%, 100% { transform: scale(1) translateY(0px); }
    50% { transform: scale(1.1) translateY(-10px); }
}

@keyframes robotShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-3px); }
    75% { transform: translateX(3px); }
}

/* Robot States */
.robot-speaking #robotBody {
    animation: robotFloat 4s ease-in-out infinite, robotBounce 0.8s ease-in-out infinite;
}

.robot-thinking #robotBody {
    animation: robotFloat 4s ease-in-out infinite, robotShake 0.4s ease-in-out infinite;
}

.robot-excited #robotBody {
    animation: robotFloat 4s ease-in-out infinite, robotMove 6s ease-in-out infinite;
}

.robot-excited #leftArm {
    animation: robotWave 1.2s ease-in-out infinite;
}

.robot-excited #rightArm {
    animation: robotWave 1.2s ease-in-out infinite reverse;
    animation-delay: 0.4s;
}

/* Hover Effects */
#advancedRobot:hover #robotBody {
    animation: robotFloat 4s ease-in-out infinite, robotBounce 2s ease-in-out infinite;
}

#advancedRobot:hover #leftArm {
    animation: robotWave 2s ease-in-out infinite;
}

#advancedRobot:hover #rightArm {
    animation: robotWave 2s ease-in-out infinite reverse;
    animation-delay: 0.6s;
}

/* Ensure robot is always visible */
#advancedRobot {
    z-index: 2147483647 !important;
    position: fixed !important;
    pointer-events: auto !important;
}

/* Responsive */
@media (max-width: 640px) {
    #chatWindow {
        width: calc(100vw - 2rem);
        right: -6rem;
    }
    
    #advancedRobot {
        bottom: 80px !important;
        right: 20px !important;
    }
}

/* Language specific styles */
.lang-ar .en, .lang-ar .fr { display: none !important; }
.lang-en .ar, .lang-en .fr { display: none !important; }
.lang-fr .ar, .lang-fr .en { display: none !important; }
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const robot = document.getElementById('advancedRobot');
    const robotContainer = document.getElementById('robotContainer');
    const robotBody = document.getElementById('robotBody');
    const speechBubble = document.getElementById('speechBubble');
    const speechText = document.getElementById('speechText');
    const chatWindow = document.getElementById('chatWindow');
    const chatMessages = document.getElementById('chatMessages');
    const chatInput = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendChatBtn');
    const closeBtn = document.getElementById('closeChatBtn');
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');

    let isOpen = false;
    let isTyping = false;
    let currentLanguage = document.documentElement.lang || 'ar';
    let robotPosition = { x: 100, y: 100 };

    // Language-specific messages
    const messages = {
        ar: {
            greeting: 'مرحباً! 👋',
            welcome: 'مرحباً! أنا مساعدك الذكي في MonOri AI. كيف يمكنني مساعدتك اليوم؟',
            title: 'مساعد MonOri الذكي',
            status: 'متصل الآن',
            placeholder: 'اكتب سؤالك هنا...',
            thinking: 'أفكر... 🤔',
            speaking: 'أتحدث... 🗣️',
            excited: 'متحمس! 🎉'
        },
        en: {
            greeting: 'Hello! 👋',
            welcome: 'Hello! I\'m your smart assistant at MonOri AI. How can I help you today?',
            title: 'MonOri Smart Assistant',
            status: 'Online now',
            placeholder: 'Type your question here...',
            thinking: 'Thinking... 🤔',
            speaking: 'Speaking... 🗣️',
            excited: 'Excited! 🎉'
        },
        fr: {
            greeting: 'Bonjour! 👋',
            welcome: 'Bonjour! Je suis votre assistant intelligent chez MonOri AI. Comment puis-je vous aider aujourd\'hui?',
            title: 'Assistant Intelligent MonOri',
            status: 'En ligne maintenant',
            placeholder: 'Tapez votre question ici...',
            thinking: 'Je réfléchis... 🤔',
            speaking: 'Je parle... 🗣️',
            excited: 'Excité! 🎉'
        }
    };

    // Initialize robot
    function initializeRobot() {
        updateLanguage();
        startIdleAnimations();
        setupEventListeners();
        positionRobot();
    }

    // Update language
    function updateLanguage() {
        currentLanguage = document.documentElement.lang || 'ar';
        document.body.className = document.body.className.replace(/lang-\w+/g, '') + ` lang-${currentLanguage}`;

        const msg = messages[currentLanguage];
        speechText.textContent = msg.greeting;
        document.getElementById('welcomeMessage').textContent = msg.welcome;
        document.getElementById('chatTitle').textContent = msg.title;
        document.getElementById('chatStatus').textContent = msg.status;
        chatInput.placeholder = msg.placeholder;
    }

    // Position robot randomly but visible
    function positionRobot() {
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        // Random position but keep visible
        robotPosition.x = Math.random() * (viewportWidth - 200) + 50;
        robotPosition.y = Math.random() * (viewportHeight - 300) + 100;

        robot.style.right = robotPosition.x + 'px';
        robot.style.bottom = robotPosition.y + 'px';
    }

    // Move robot to new position
    function moveRobot() {
        if (!isOpen) {
            const viewportWidth = window.innerWidth;
            const viewportHeight = window.innerHeight;

            const newX = Math.random() * (viewportWidth - 200) + 50;
            const newY = Math.random() * (viewportHeight - 300) + 100;

            robot.style.transition = 'all 2s ease-in-out';
            robot.style.right = newX + 'px';
            robot.style.bottom = newY + 'px';

            robotPosition.x = newX;
            robotPosition.y = newY;

            setRobotExpression('excited');
            showSpeechBubble(messages[currentLanguage].excited, 2000);
        }
    }

    // Robot expressions
    function setRobotExpression(expression) {
        robot.classList.remove('robot-speaking', 'robot-thinking', 'robot-excited');

        const leftEye = document.getElementById('leftEye');
        const rightEye = document.getElementById('rightEye');
        const mouth = document.getElementById('robotMouth');
        const speakingRings = document.getElementById('speakingRings');

        switch(expression) {
            case 'speaking':
                robot.classList.add('robot-speaking');
                speakingRings.style.opacity = '1';
                mouth.style.transform = 'scaleY(1.5) scaleX(1.2)';
                mouth.style.background = 'linear-gradient(135deg, #10B981, #059669)';
                leftEye.style.backgroundColor = '#10B981';
                rightEye.style.backgroundColor = '#10B981';
                break;
            case 'thinking':
                robot.classList.add('robot-thinking');
                speakingRings.style.opacity = '0';
                mouth.style.transform = 'scaleY(0.8) scaleX(0.8)';
                mouth.style.background = 'linear-gradient(135deg, #F59E0B, #D97706)';
                leftEye.style.backgroundColor = '#F59E0B';
                rightEye.style.backgroundColor = '#F59E0B';
                break;
            case 'happy':
                speakingRings.style.opacity = '0';
                mouth.style.transform = 'scaleY(1.3) scaleX(1.1)';
                mouth.style.background = 'linear-gradient(135deg, #10B981, #059669)';
                leftEye.style.backgroundColor = '#10B981';
                rightEye.style.backgroundColor = '#10B981';
                break;
            case 'excited':
                robot.classList.add('robot-excited');
                speakingRings.style.opacity = '0.5';
                mouth.style.transform = 'scaleY(1.4) scaleX(1.3)';
                mouth.style.background = 'linear-gradient(135deg, #8B5CF6, #7C3AED)';
                leftEye.style.backgroundColor = '#8B5CF6';
                rightEye.style.backgroundColor = '#8B5CF6';
                break;
            default:
                speakingRings.style.opacity = '0';
                mouth.style.transform = 'scaleY(1) scaleX(1)';
                mouth.style.background = 'linear-gradient(135deg, #00D4FF, #0066CC)';
                leftEye.style.backgroundColor = '#3B82F6';
                rightEye.style.backgroundColor = '#3B82F6';
        }
    }

    // Show speech bubble
    function showSpeechBubble(text, duration = 3000) {
        speechText.textContent = text;
        speechBubble.style.opacity = '1';
        speechBubble.style.transform = 'translateX(-50%) scale(1)';

        setTimeout(() => {
            speechBubble.style.opacity = '0';
            speechBubble.style.transform = 'translateX(-50%) scale(0.8)';
        }, duration);
    }

    // Robot sounds with language
    function playRobotSound(type) {
        if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            switch(type) {
                case 'greeting':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.3);
                    break;
                case 'click':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                    break;
                case 'open':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);
                    break;
                case 'close':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.2);
                    break;
            }

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        }
    }

    // Speak text in current language
    function speakText(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);

            switch(currentLanguage) {
                case 'ar':
                    utterance.lang = 'ar-SA';
                    break;
                case 'en':
                    utterance.lang = 'en-US';
                    break;
                case 'fr':
                    utterance.lang = 'fr-FR';
                    break;
            }

            utterance.rate = 0.9;

            utterance.onstart = () => {
                setRobotExpression('speaking');
                showSpeechBubble(messages[currentLanguage].speaking, 1000);
            };

            utterance.onend = () => {
                setRobotExpression('happy');
            };

            speechSynthesis.speak(utterance);
        }
    }

    // Setup event listeners
    function setupEventListeners() {
        // Robot click
        robotContainer.addEventListener('click', function() {
            playRobotSound('greeting');

            if (isOpen) {
                closeChat();
            } else {
                openChat();
            }

            const greeting = messages[currentLanguage].greeting;
            showSpeechBubble(greeting, 2000);
            speakText(greeting);
            setRobotExpression('happy');
        });

        // Close chat
        closeBtn.addEventListener('click', closeChat);

        // Send message
        sendBtn.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage();
        });

        // Quick actions
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', async function() {
                const questionType = this.dataset.question;
                await handleQuickAction(questionType);
            });
        });

        // Language change detection
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                    updateLanguage();
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['lang']
        });
    }

    function openChat() {
        isOpen = true;
        playRobotSound('open');
        chatWindow.classList.remove('scale-0', 'opacity-0');
        chatWindow.classList.add('scale-100', 'opacity-100');
        chatInput.focus();
        setRobotExpression('happy');
    }

    function closeChat() {
        isOpen = false;
        playRobotSound('close');
        chatWindow.classList.add('scale-0', 'opacity-0');
        chatWindow.classList.remove('scale-100', 'opacity-100');
        setRobotExpression('neutral');
    }

    async function sendMessage() {
        const message = chatInput.value.trim();
        if (!message) return;

        addMessage('user', message);
        chatInput.value = '';
        setRobotExpression('thinking');
        showSpeechBubble(messages[currentLanguage].thinking, 1000);
        isTyping = true;

        showTyping();

        try {
            const response = await getAIResponse(message);
            hideTyping();
            isTyping = false;
            addMessage('robot', response);
            setRobotExpression('speaking');
            speakText(response);

            setTimeout(() => {
                setRobotExpression('happy');
            }, 2000);
        } catch (error) {
            hideTyping();
            isTyping = false;
            const errorMsg = currentLanguage === 'ar' ? 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.' :
                           currentLanguage === 'en' ? 'Sorry, an error occurred. Please try again.' :
                           'Désolé, une erreur s\'est produite. Veuillez réessayer.';
            addMessage('robot', errorMsg);
            setRobotExpression('neutral');
        }
    }

    async function handleQuickAction(questionType) {
        setRobotExpression('thinking');
        isTyping = true;
        showTyping();

        try {
            const response = await fetch('/customer-service/quick-action', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    action: questionType,
                    language: currentLanguage
                })
            });

            const data = await response.json();
            hideTyping();
            isTyping = false;

            if (data.success) {
                addMessage('robot', data.response);
                setRobotExpression('speaking');
                speakText(data.response);

                setTimeout(() => {
                    setRobotExpression('happy');
                }, 2000);
            }
        } catch (error) {
            hideTyping();
            isTyping = false;
            setRobotExpression('neutral');
        }
    }

    function addMessage(sender, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start mb-3';

        if (sender === 'robot') {
            messageDiv.innerHTML = `
                <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-900 dark:text-white text-sm">${text}</p>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="flex items-start justify-end w-full">
                    <div class="bg-blue-600 text-white rounded-lg p-3 max-w-xs mr-2">
                        <p class="text-sm">${text}</p>
                    </div>
                    <div class="w-6 h-6 bg-gray-400 rounded-full flex-shrink-0 flex items-center justify-center text-white text-xs">👤</div>
                </div>
            `;
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function showTyping() {
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'flex items-start mb-3';
        typingDiv.innerHTML = `
            <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
            </div>
        `;

        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function hideTyping() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    async function getAIResponse(message) {
        try {
            const response = await fetch('/customer-service/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    message: message,
                    language: currentLanguage
                })
            });

            const data = await response.json();

            if (data.success) {
                return data.response;
            } else {
                throw new Error(data.message || 'Service error');
            }
        } catch (error) {
            console.error('AI Response Error:', error);
            const errorMsg = currentLanguage === 'ar' ? 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.' :
                           currentLanguage === 'en' ? 'Sorry, connection error. Please try again.' :
                           'Désolé, erreur de connexion. Veuillez réessayer.';
            return errorMsg;
        }
    }

    // Idle animations
    function startIdleAnimations() {
        // Random expressions
        setInterval(() => {
            if (!isOpen && !isTyping) {
                const expressions = ['neutral', 'happy', 'excited'];
                const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
                setRobotExpression(randomExpression);

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 2000);
            }
        }, 10000);

        // Random movement
        setInterval(() => {
            if (!isOpen && !isTyping) {
                moveRobot();
            }
        }, 20000);

        // Random greetings
        setInterval(() => {
            if (!isOpen && Math.random() < 0.3) {
                const greeting = messages[currentLanguage].greeting;
                showSpeechBubble(greeting, 2000);
                setRobotExpression('happy');

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 3000);
            }
        }, 25000);

        // Random notifications
        setInterval(() => {
            if (!isOpen && Math.random() < 0.2) {
                const badge = document.getElementById('notificationBadge');
                badge.classList.remove('hidden');

                setTimeout(() => {
                    badge.classList.add('hidden');
                }, 4000);
            }
        }, 35000);
    }

    // Initialize
    initializeRobot();

    // Ensure robot stays visible
    window.addEventListener('scroll', function() {
        robot.style.position = 'fixed';
        robot.style.zIndex = '2147483647';
    });

    window.addEventListener('resize', function() {
        robot.style.position = 'fixed';
        robot.style.zIndex = '2147483647';
    });
});
</script>
