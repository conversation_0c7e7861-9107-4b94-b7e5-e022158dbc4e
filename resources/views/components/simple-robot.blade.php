<!-- Simple Floating Robot -->
<div id="simpleRobot" class="fixed bottom-6 right-6 z-[9999]" style="display: block !important; z-index: 9999 !important;">
    <!-- Robot Button -->
    <div id="robotBtn" class="relative cursor-pointer group">
        <!-- Robot Body -->
        <div id="robotBody" class="w-20 h-20 rounded-full shadow-2xl transform transition-all duration-300 hover:scale-110 relative"
             style="background: linear-gradient(135deg, #3B82F6, #8B5CF6, #4F46E5); animation: robotFloat 3s ease-in-out infinite;">

            <!-- Robot Face -->
            <div class="relative w-full h-full rounded-full overflow-hidden flex items-center justify-center">
                <!-- Eyes -->
                <div class="absolute top-4 left-4 w-3 h-3 bg-white rounded-full">
                    <div id="leftEye" class="w-2 h-2 bg-blue-800 rounded-full mt-0.5 ml-0.5 transition-all duration-300" style="animation: robotBlink 4s ease-in-out infinite;"></div>
                </div>
                <div class="absolute top-4 right-4 w-3 h-3 bg-white rounded-full">
                    <div id="rightEye" class="w-2 h-2 bg-blue-800 rounded-full mt-0.5 ml-0.5 transition-all duration-300" style="animation: robotBlink 4s ease-in-out infinite;"></div>
                </div>

                <!-- Mouth -->
                <div id="robotMouth" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 w-4 h-2 bg-white rounded-full transition-all duration-300">
                    <div class="w-full h-full bg-gray-800 rounded-full transform scale-75"></div>
                </div>

                <!-- Antenna -->
                <div class="absolute -top-2 left-1/2 transform -translate-x-1/2 w-1 h-6 bg-gray-300 rounded-full">
                    <div class="absolute -top-1 left-1/2 transform -translate-x-1/2 w-3 h-3 bg-red-400 rounded-full animate-pulse"></div>
                </div>

                <!-- Arms -->
                <div id="leftArm" class="absolute top-6 -left-3 w-4 h-1.5 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full transform rotate-45 transition-all duration-500"></div>
                <div id="rightArm" class="absolute top-6 -right-3 w-4 h-1.5 bg-gradient-to-r from-blue-400 to-purple-500 rounded-full transform -rotate-45 transition-all duration-500"></div>

                <!-- Speaking Rings -->
                <div id="speakingRings" class="absolute inset-0 opacity-0 transition-opacity duration-300">
                    <div class="absolute inset-2 border-2 border-white/30 rounded-full animate-ping"></div>
                    <div class="absolute inset-4 border-2 border-white/20 rounded-full animate-ping" style="animation-delay: 0.2s"></div>
                </div>
            </div>

            <!-- Notification Badge -->
            <div id="notificationBadge" class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center font-bold animate-bounce hidden">
                !
            </div>
        </div>
        
        <!-- Hover Text -->
        <div class="absolute bottom-full right-0 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            مساعد ذكي - اسأل أي سؤال! 🤖
            <div class="absolute top-full right-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
    </div>
    
    <!-- Chat Window -->
    <div id="chatWindow" class="absolute bottom-24 right-0 w-80 h-96 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform scale-0 origin-bottom-right transition-all duration-300 opacity-0">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-white/20 rounded-full mr-3 flex items-center justify-center">
                        🤖
                    </div>
                    <div>
                        <h3 class="font-semibold text-sm">مساعد MonOri الذكي</h3>
                        <p class="text-xs text-white/80">متصل الآن</p>
                    </div>
                </div>
                <button id="closeChatBtn" class="text-white/80 hover:text-white transition-colors">
                    ✕
                </button>
            </div>
        </div>
        
        <!-- Chat Messages -->
        <div id="chatMessages" class="h-64 p-4 overflow-y-auto space-y-3">
            <div class="flex items-start">
                <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">
                    🤖
                </div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-900 dark:text-white text-sm">مرحباً! أنا مساعدك الذكي في MonOri AI. كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex space-x-2 rtl:space-x-reverse">
                <input type="text" id="chatInput" placeholder="اكتب سؤالك هنا..." 
                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm">
                <button id="sendChatBtn" class="bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    ➤
                </button>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="px-4 pb-4">
            <div class="flex flex-wrap gap-2">
                <button class="quick-action-btn text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors" data-question="ما هي خدمات MonOri AI؟">
                    خدماتنا
                </button>
                <button class="quick-action-btn text-xs bg-purple-100 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-2 py-1 rounded-full hover:bg-purple-200 dark:hover:bg-purple-900/40 transition-colors" data-question="كيف أنشئ سيرة ذاتية؟">
                    إنشاء CV
                </button>
                <button class="quick-action-btn text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors" data-question="ما هي أسعار الخدمات؟">
                    الأسعار
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Ensure robot is always on top */
#simpleRobot {
    z-index: 999999 !important;
    position: fixed !important;
    bottom: 24px !important;
    right: 24px !important;
    pointer-events: auto !important;
}

#chatWindow {
    z-index: 999998 !important;
    position: absolute !important;
}

@keyframes robotFloat {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    25% { transform: translateY(-5px) rotate(1deg); }
    50% { transform: translateY(-10px) rotate(2deg); }
    75% { transform: translateY(-5px) rotate(1deg); }
}

@keyframes robotBlink {
    0%, 90%, 100% { transform: scaleY(1); }
    95% { transform: scaleY(0.1); }
}

@keyframes robotWave {
    0%, 100% { transform: rotate(45deg); }
    25% { transform: rotate(60deg); }
    50% { transform: rotate(90deg); }
    75% { transform: rotate(60deg); }
}

@keyframes robotBounce {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes robotShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-2px); }
    75% { transform: translateX(2px); }
}

.robot-speaking #robotBody {
    animation: robotFloat 3s ease-in-out infinite, robotBounce 0.5s ease-in-out infinite;
}

.robot-thinking #robotBody {
    animation: robotFloat 3s ease-in-out infinite, robotShake 0.3s ease-in-out infinite;
}

.robot-excited #leftArm {
    animation: robotWave 1s ease-in-out infinite;
}

.robot-excited #rightArm {
    animation: robotWave 1s ease-in-out infinite reverse;
    animation-delay: 0.3s;
}

#simpleRobot:hover #robotBody {
    animation: robotFloat 3s ease-in-out infinite, robotBounce 2s ease-in-out infinite;
}

#simpleRobot:hover #leftArm {
    animation: robotWave 2s ease-in-out infinite;
}

#simpleRobot:hover #rightArm {
    animation: robotWave 2s ease-in-out infinite reverse;
    animation-delay: 0.5s;
}

/* Responsive design */
@media (max-width: 640px) {
    #chatWindow {
        width: calc(100vw - 2rem);
        right: -1rem;
    }

    #simpleRobot {
        bottom: 20px !important;
        right: 20px !important;
    }
}

/* Ensure robot stays above everything */
body * {
    position: relative;
}

#simpleRobot {
    position: fixed !important;
    z-index: 2147483647 !important; /* Maximum z-index value */
}

/* Prevent any element from covering the robot */
footer, header, nav, main, section, div {
    z-index: auto !important;
}

footer {
    z-index: 1 !important;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const robotBtn = document.getElementById('robotBtn');
    const chatWindow = document.getElementById('chatWindow');
    const chatMessages = document.getElementById('chatMessages');
    const chatInput = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendChatBtn');
    const closeBtn = document.getElementById('closeChatBtn');
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');
    
    let isOpen = false;
    let isTyping = false;

    // Robot expressions
    function setRobotExpression(expression) {
        const robot = document.getElementById('simpleRobot');
        const leftEye = document.getElementById('leftEye');
        const rightEye = document.getElementById('rightEye');
        const mouth = document.getElementById('robotMouth');
        const speakingRings = document.getElementById('speakingRings');

        // Remove all expression classes
        robot.classList.remove('robot-speaking', 'robot-thinking', 'robot-excited');

        switch(expression) {
            case 'speaking':
                robot.classList.add('robot-speaking');
                speakingRings.style.opacity = '1';
                mouth.style.transform = 'scaleY(1.5) scaleX(1.2)';
                mouth.style.backgroundColor = '#EF4444';
                leftEye.style.backgroundColor = '#10B981';
                rightEye.style.backgroundColor = '#10B981';
                break;
            case 'thinking':
                robot.classList.add('robot-thinking');
                speakingRings.style.opacity = '0';
                mouth.style.transform = 'scaleY(0.8) scaleX(0.8)';
                mouth.style.backgroundColor = '#F59E0B';
                leftEye.style.backgroundColor = '#F59E0B';
                rightEye.style.backgroundColor = '#F59E0B';
                break;
            case 'happy':
                speakingRings.style.opacity = '0';
                mouth.style.transform = 'scaleY(1.3) scaleX(1.1)';
                mouth.style.backgroundColor = '#10B981';
                leftEye.style.backgroundColor = '#10B981';
                rightEye.style.backgroundColor = '#10B981';
                break;
            case 'excited':
                robot.classList.add('robot-excited');
                speakingRings.style.opacity = '0.5';
                mouth.style.transform = 'scaleY(1.4) scaleX(1.3)';
                mouth.style.backgroundColor = '#8B5CF6';
                leftEye.style.backgroundColor = '#8B5CF6';
                rightEye.style.backgroundColor = '#8B5CF6';
                break;
            default:
                speakingRings.style.opacity = '0';
                mouth.style.transform = 'scaleY(1) scaleX(1)';
                mouth.style.backgroundColor = '#6B7280';
                leftEye.style.backgroundColor = '#1E40AF';
                rightEye.style.backgroundColor = '#1E40AF';
        }
    }

    // Robot sounds
    function playRobotSound(type) {
        // Create audio context for robot sounds
        if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            switch(type) {
                case 'click':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                    break;
                case 'open':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(800, audioContext.currentTime + 0.2);
                    break;
                case 'close':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(200, audioContext.currentTime + 0.2);
                    break;
            }

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }
    }

    // Toggle chat
    robotBtn.addEventListener('click', function() {
        playRobotSound('click');
        if (isOpen) {
            closeChat();
        } else {
            openChat();
        }
    });
    
    closeBtn.addEventListener('click', closeChat);
    
    function openChat() {
        isOpen = true;
        playRobotSound('open');
        chatWindow.classList.remove('scale-0', 'opacity-0');
        chatWindow.classList.add('scale-100', 'opacity-100');
        chatInput.focus();
        setRobotExpression('happy');
    }

    function closeChat() {
        isOpen = false;
        playRobotSound('close');
        chatWindow.classList.add('scale-0', 'opacity-0');
        chatWindow.classList.remove('scale-100', 'opacity-100');
        setRobotExpression('neutral');
    }
    
    // Send message
    sendBtn.addEventListener('click', sendMessage);
    chatInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') sendMessage();
    });
    
    async function sendMessage() {
        const message = chatInput.value.trim();
        if (!message) return;

        addMessage('user', message);
        chatInput.value = '';
        setRobotExpression('thinking');
        isTyping = true;

        // Show typing
        showTyping();

        try {
            const response = await getAIResponse(message);
            hideTyping();
            isTyping = false;
            addMessage('robot', response);
            setRobotExpression('speaking');

            // Speak the response
            speakText(response);

            setTimeout(() => {
                setRobotExpression('happy');
            }, 2000);
        } catch (error) {
            hideTyping();
            isTyping = false;
            addMessage('robot', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
            setRobotExpression('neutral');
        }
    }
    
    function addMessage(sender, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start mb-3';
        
        if (sender === 'robot') {
            messageDiv.innerHTML = `
                <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-900 dark:text-white text-sm">${text}</p>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="flex items-start justify-end w-full">
                    <div class="bg-blue-600 text-white rounded-lg p-3 max-w-xs mr-2">
                        <p class="text-sm">${text}</p>
                    </div>
                    <div class="w-6 h-6 bg-gray-400 rounded-full flex-shrink-0 flex items-center justify-center text-white text-xs">👤</div>
                </div>
            `;
        }
        
        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function showTyping() {
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'flex items-start mb-3';
        typingDiv.innerHTML = `
            <div class="w-6 h-6 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
            </div>
        `;
        
        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }
    
    function hideTyping() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }
    
    async function getAIResponse(message) {
        try {
            const response = await fetch('/customer-service/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    message: message,
                    language: document.documentElement.lang || 'ar'
                })
            });

            const data = await response.json();
            
            if (data.success) {
                return data.response;
            } else {
                throw new Error(data.message || 'خطأ في الخدمة');
            }
        } catch (error) {
            console.error('AI Response Error:', error);
            return 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.';
        }
    }
    
    // Speak text function
    function speakText(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);
            utterance.lang = 'ar-SA';
            utterance.rate = 0.9;

            utterance.onstart = () => {
                setRobotExpression('speaking');
            };

            utterance.onend = () => {
                setRobotExpression('happy');
            };

            speechSynthesis.speak(utterance);
        }
    }

    // Random animations
    function startIdleAnimations() {
        // Random expressions
        setInterval(() => {
            if (!isOpen && !isTyping) {
                const expressions = ['neutral', 'happy', 'excited'];
                const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
                setRobotExpression(randomExpression);

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 2000);
            }
        }, 8000);

        // Random arm movements
        setInterval(() => {
            if (!isOpen && !isTyping) {
                const leftArm = document.getElementById('leftArm');
                const rightArm = document.getElementById('rightArm');

                const leftRotation = Math.random() * 90 + 45;
                const rightRotation = -(Math.random() * 90 + 45);

                leftArm.style.transform = `rotate(${leftRotation}deg)`;
                rightArm.style.transform = `rotate(${rightRotation}deg)`;

                setTimeout(() => {
                    leftArm.style.transform = 'rotate(45deg)';
                    rightArm.style.transform = 'rotate(-45deg)';
                }, 1500);
            }
        }, 12000);

        // Random notifications
        setInterval(() => {
            if (!isOpen && Math.random() < 0.3) {
                const badge = document.getElementById('notificationBadge');
                badge.classList.remove('hidden');

                setTimeout(() => {
                    badge.classList.add('hidden');
                }, 3000);
            }
        }, 30000);
    }

    // Quick actions
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', async function() {
            const question = this.dataset.question;
            addMessage('user', question);
            setRobotExpression('thinking');
            isTyping = true;
            showTyping();

            try {
                const response = await getAIResponse(question);
                hideTyping();
                isTyping = false;
                addMessage('robot', response);
                setRobotExpression('speaking');
                speakText(response);

                setTimeout(() => {
                    setRobotExpression('happy');
                }, 2000);
            } catch (error) {
                hideTyping();
                isTyping = false;
                addMessage('robot', 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.');
                setRobotExpression('neutral');
            }
        });
    });

    // Start idle animations
    startIdleAnimations();

    // Ensure robot stays visible on scroll
    window.addEventListener('scroll', function() {
        const robot = document.getElementById('simpleRobot');
        if (robot) {
            robot.style.position = 'fixed';
            robot.style.zIndex = '2147483647';
            robot.style.bottom = '24px';
            robot.style.right = '24px';
        }
    });

    // Ensure robot stays visible on resize
    window.addEventListener('resize', function() {
        const robot = document.getElementById('simpleRobot');
        if (robot) {
            robot.style.position = 'fixed';
            robot.style.zIndex = '2147483647';
        }
    });
});
</script>
