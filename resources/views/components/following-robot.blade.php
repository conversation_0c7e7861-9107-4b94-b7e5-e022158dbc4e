<!-- Following Robot - Exact Match to Image -->
<div id="followingRobot" class="fixed z-[99999] pointer-events-auto" style="transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);">
    <!-- Robot Container -->
    <div id="robotContainer" class="relative cursor-pointer group">
        <!-- Robot Shadow - Ultra Realistic -->
        <div id="robotShadow" class="absolute top-full left-1/2 transform -translate-x-1/2 w-24 h-8 rounded-full" style="background: radial-gradient(ellipse, rgba(0,0,0,0.4) 0%, rgba(0,0,0,0.2) 50%, transparent 100%); animation: shadowFloat 3s ease-in-out infinite; filter: blur(12px);"></div>
        
        <!-- Robot Exactly Like Image - Front Facing -->
        <div id="robotBody" class="relative w-36 h-44 transform transition-all duration-300" style="animation: robotFloat 4s ease-in-out infinite;">

            <!-- Robot Head - Oval Shape Like Image -->
            <div class="relative w-36 h-28 mx-auto" style="background: linear-gradient(145deg, #e5e7eb, #d1d5db, #9ca3af); border-radius: 50% 50% 45% 45%; box-shadow: 0 15px 45px rgba(0,0,0,0.3), inset 0 4px 15px rgba(255,255,255,0.4), inset 0 -4px 15px rgba(0,0,0,0.15);">

                <!-- Top Blue Panel Like Image -->
                <div class="absolute top-2 left-1/2 transform -translate-x-1/2 w-24 h-8" style="background: linear-gradient(145deg, #22d3ee, #06b6d4, #0891b2); border-radius: 12px 12px 4px 4px; box-shadow: inset 0 2px 6px rgba(255,255,255,0.5), 0 4px 15px rgba(34,211,238,0.4);"></div>

                <!-- Side Ear Panels Like Image -->
                <div class="absolute top-6 -left-2 w-6 h-16 rounded-full" style="background: linear-gradient(145deg, #e5e7eb, #d1d5db); box-shadow: 0 4px 12px rgba(0,0,0,0.2), inset 0 2px 6px rgba(255,255,255,0.3);"></div>
                <div class="absolute top-6 -right-2 w-6 h-16 rounded-full" style="background: linear-gradient(145deg, #e5e7eb, #d1d5db); box-shadow: 0 4px 12px rgba(0,0,0,0.2), inset 0 2px 6px rgba(255,255,255,0.3);"></div>

                <!-- Dark Face Visor Like Image -->
                <div class="absolute top-8 left-3 right-3 h-16" style="background: linear-gradient(145deg, #1e293b, #0f172a, #020617); border-radius: 50px; box-shadow: inset 0 5px 15px rgba(0,0,0,0.8), inset 0 -3px 8px rgba(255,255,255,0.05), 0 4px 20px rgba(0,0,0,0.5);">

                    <!-- Left Eye - Half Circle Like Image -->
                    <div class="absolute top-1/2 left-8 transform -translate-y-1/2 w-10 h-5" style="background: linear-gradient(180deg, #22d3ee, #06b6d4); border-radius: 20px 20px 0 0; box-shadow: 0 0 20px #22d3ee, 0 0 40px rgba(34,211,238,0.5);">
                        <div id="leftEye" class="w-full h-full" style="background: linear-gradient(180deg, #ffffff, #22d3ee); border-radius: 20px 20px 0 0; animation: eyeGlow 2s ease-in-out infinite alternate;"></div>
                    </div>

                    <!-- Right Eye - Half Circle Like Image -->
                    <div class="absolute top-1/2 right-8 transform -translate-y-1/2 w-10 h-5" style="background: linear-gradient(180deg, #22d3ee, #06b6d4); border-radius: 20px 20px 0 0; box-shadow: 0 0 20px #22d3ee, 0 0 40px rgba(34,211,238,0.5);">
                        <div id="rightEye" class="w-full h-full" style="background: linear-gradient(180deg, #ffffff, #22d3ee); border-radius: 20px 20px 0 0; animation: eyeGlow 2s ease-in-out infinite alternate;"></div>
                    </div>
                </div>
            </div>

            <!-- Robot Neck/Connection -->
            <div class="relative w-6 h-2 mx-auto -mt-1" style="background: linear-gradient(145deg, #e2e8f0, #cbd5e1); border-radius: 50%;"></div>

            <!-- Robot Body Like Image -->
            <div class="relative w-32 h-20 mx-auto -mt-2" style="background: linear-gradient(145deg, #e5e7eb, #d1d5db, #9ca3af); border-radius: 50% 50% 60% 60%; box-shadow: 0 12px 35px rgba(0,0,0,0.25), inset 0 4px 15px rgba(255,255,255,0.4), inset 0 -4px 15px rgba(0,0,0,0.15);">

                <!-- Central Blue Circle Like Image -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-8 h-8 rounded-full" style="background: radial-gradient(circle at 30% 30%, #22d3ee, #06b6d4, #0891b2); box-shadow: 0 0 25px #22d3ee, 0 0 50px rgba(34,211,238,0.4), inset 0 2px 6px rgba(255,255,255,0.5);">
                    <div class="w-full h-full rounded-full" style="background: radial-gradient(circle at 30% 30%, #ffffff, #22d3ee); animation: centerGlow 1.5s ease-in-out infinite alternate;"></div>
                    <!-- Center Ring -->
                    <div class="absolute inset-1 rounded-full border border-cyan-300/40"></div>
                </div>

                <!-- Body Chest Details Like Image -->
                <div class="absolute top-2 left-1/2 transform -translate-x-1/2 w-16 h-3" style="background: linear-gradient(145deg, #9ca3af, #6b7280); border-radius: 8px; box-shadow: inset 0 1px 3px rgba(0,0,0,0.3);"></div>
            </div>

            <!-- Left Arm Like Image -->
            <div id="leftArm" class="absolute top-20 -left-8 w-16 h-12 transform rotate-15 transition-all duration-300" style="background: linear-gradient(145deg, #e5e7eb, #d1d5db, #9ca3af); border-radius: 50% 50% 30% 30%; box-shadow: 0 6px 15px rgba(0,0,0,0.2), inset 0 2px 6px rgba(255,255,255,0.3);">
                <!-- Arm Detail -->
                <div class="absolute bottom-2 right-2 w-3 h-6" style="background: linear-gradient(145deg, #9ca3af, #6b7280); border-radius: 6px;"></div>
            </div>

            <!-- Right Arm Like Image -->
            <div id="rightArm" class="absolute top-20 -right-8 w-16 h-12 transform -rotate-15 transition-all duration-300" style="background: linear-gradient(145deg, #e5e7eb, #d1d5db, #9ca3af); border-radius: 50% 50% 30% 30%; box-shadow: 0 6px 15px rgba(0,0,0,0.2), inset 0 2px 6px rgba(255,255,255,0.3);">
                <!-- Arm Detail -->
                <div class="absolute bottom-2 left-2 w-3 h-6" style="background: linear-gradient(145deg, #9ca3af, #6b7280); border-radius: 6px;"></div>
            </div>



            <!-- Speaking Rings -->
            <div id="speakingRings" class="absolute inset-0 opacity-0 transition-opacity duration-300">
                <div class="absolute inset-2 border-2 border-cyan-400/40 rounded-full animate-ping"></div>
                <div class="absolute inset-4 border-2 border-cyan-300/30 rounded-full animate-ping" style="animation-delay: 0.2s"></div>
                <div class="absolute inset-6 border-2 border-cyan-200/20 rounded-full animate-ping" style="animation-delay: 0.4s"></div>
            </div>
        </div>
        
        <!-- Speech Bubble -->
        <div id="speechBubble" class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-white rounded-2xl shadow-xl opacity-0 transition-all duration-300 whitespace-nowrap border border-cyan-200">
            <div id="speechText" class="text-gray-800 font-medium text-sm">مرحباً! 👋</div>
            <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-6 border-transparent border-t-white"></div>
        </div>
        
        <!-- Hover Tooltip -->
        <div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 whitespace-nowrap">
            <span id="hoverText">مساعد ذكي - اضغط للتحدث! 🤖</span>
            <div class="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"></div>
        </div>
    </div>
    
    <!-- Chat Window -->
    <div id="chatWindow" class="absolute bottom-full right-0 mb-4 w-80 h-96 bg-white dark:bg-gray-800 rounded-2xl shadow-2xl transform scale-0 origin-bottom-right transition-all duration-400 opacity-0 border border-gray-200 dark:border-gray-700">
        <!-- Chat Header -->
        <div class="bg-gradient-to-r from-cyan-500 to-blue-600 text-white p-4 rounded-t-2xl">
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <div class="w-8 h-8 bg-white/20 rounded-full mr-3 flex items-center justify-center">
                        🤖
                    </div>
                    <div>
                        <h3 id="chatTitle" class="font-semibold text-sm">مساعد MonOri الذكي</h3>
                        <p id="chatStatus" class="text-xs text-white/80">متصل الآن</p>
                    </div>
                </div>
                <button id="closeChatBtn" class="text-white/80 hover:text-white transition-colors">
                    ✕
                </button>
            </div>
        </div>
        
        <!-- Chat Messages -->
        <div id="chatMessages" class="h-64 p-4 overflow-y-auto space-y-3">
            <div class="flex items-start">
                <div class="w-6 h-6 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p id="welcomeMessage" class="text-gray-900 dark:text-white text-sm">مرحباً! أنا مساعدك الذكي في MonOri AI. كيف يمكنني مساعدتك اليوم؟</p>
                </div>
            </div>
        </div>
        
        <!-- Chat Input -->
        <div class="p-4 border-t border-gray-200 dark:border-gray-700">
            <div class="flex space-x-2 rtl:space-x-reverse">
                <input type="text" id="chatInput" placeholder="اكتب سؤالك هنا..." 
                       class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-cyan-500 focus:border-transparent dark:bg-gray-700 dark:text-white text-sm">
                <button id="sendChatBtn" class="bg-cyan-600 text-white px-3 py-2 rounded-lg hover:bg-cyan-700 transition-colors">
                    ➤
                </button>
            </div>
        </div>
        
        <!-- Quick Actions -->
        <div class="px-4 pb-4">
            <div class="flex flex-wrap gap-2">
                <button class="quick-action-btn text-xs bg-cyan-100 dark:bg-cyan-900/20 text-cyan-700 dark:text-cyan-300 px-2 py-1 rounded-full hover:bg-cyan-200 dark:hover:bg-cyan-900/40 transition-colors" data-question="services">
                    <span class="ar">خدماتنا</span><span class="en hidden">Our Services</span><span class="fr hidden">Nos Services</span>
                </button>
                <button class="quick-action-btn text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded-full hover:bg-blue-200 dark:hover:bg-blue-900/40 transition-colors" data-question="cv">
                    <span class="ar">إنشاء CV</span><span class="en hidden">Create CV</span><span class="fr hidden">Créer CV</span>
                </button>
                <button class="quick-action-btn text-xs bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-2 py-1 rounded-full hover:bg-green-200 dark:hover:bg-green-900/40 transition-colors" data-question="pricing">
                    <span class="ar">الأسعار</span><span class="en hidden">Pricing</span><span class="fr hidden">Prix</span>
                </button>
            </div>
        </div>
    </div>
</div>

<style>
/* Advanced Robot Animations */
@keyframes robotFloat {
    0%, 100% {
        transform: translateY(0px) scale(1) rotate(0deg);
    }
    25% {
        transform: translateY(-4px) scale(1.01) rotate(0.5deg);
    }
    50% {
        transform: translateY(-8px) scale(1.02) rotate(0deg);
    }
    75% {
        transform: translateY(-4px) scale(1.01) rotate(-0.5deg);
    }
}

@keyframes shadowFloat {
    0%, 100% {
        transform: translateX(-50%) scale(1);
        opacity: 0.4;
    }
    25% {
        transform: translateX(-50%) scale(1.05);
        opacity: 0.35;
    }
    50% {
        transform: translateX(-50%) scale(1.1);
        opacity: 0.3;
    }
    75% {
        transform: translateX(-50%) scale(1.05);
        opacity: 0.35;
    }
}

@keyframes eyeGlow {
    0% { 
        box-shadow: 0 0 8px #00d4ff, inset 0 0 8px rgba(255,255,255,0.3); 
    }
    100% { 
        box-shadow: 0 0 16px #00d4ff, inset 0 0 12px rgba(255,255,255,0.5); 
    }
}

@keyframes centerGlow {
    0% { 
        box-shadow: 0 0 12px #00d4ff, inset 0 1px 2px rgba(255,255,255,0.3); 
    }
    100% { 
        box-shadow: 0 0 20px #00d4ff, inset 0 1px 4px rgba(255,255,255,0.5); 
    }
}

@keyframes armWave {
    0%, 100% { transform: rotate(12deg); }
    50% { transform: rotate(45deg); }
}

@keyframes armWaveRight {
    0%, 100% { transform: rotate(-12deg); }
    50% { transform: rotate(-45deg); }
}

/* Robot States */
.robot-speaking #robotBody {
    animation: robotFloat 3s ease-in-out infinite, pulse 0.8s ease-in-out infinite;
}

.robot-thinking #leftEye,
.robot-thinking #rightEye {
    background: radial-gradient(circle at 30% 30%, #ffffff, #fbbf24) !important;
    box-shadow: 0 0 12px #fbbf24 !important;
}

.robot-excited #leftArm {
    animation: armWave 1s ease-in-out infinite;
}

.robot-excited #rightArm {
    animation: armWaveRight 1s ease-in-out infinite;
}

.robot-following {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Hover Effects */
#followingRobot:hover #robotBody {
    transform: scale(1.05);
}

#followingRobot:hover #leftArm {
    animation: armWave 2s ease-in-out infinite;
}

#followingRobot:hover #rightArm {
    animation: armWaveRight 2s ease-in-out infinite;
}

/* Ensure robot is always visible */
#followingRobot {
    z-index: 2147483647 !important;
    position: fixed !important;
    pointer-events: auto !important;
}

/* Responsive */
@media (max-width: 640px) {
    #chatWindow {
        width: calc(100vw - 2rem);
        right: -6rem;
    }
}

/* Language specific styles */
.lang-ar .en, .lang-ar .fr { display: none !important; }
.lang-en .ar, .lang-en .fr { display: none !important; }
.lang-fr .ar, .lang-fr .en { display: none !important; }

/* Pulse animation for speaking */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const robot = document.getElementById('followingRobot');
    const robotContainer = document.getElementById('robotContainer');
    const robotBody = document.getElementById('robotBody');
    const speechBubble = document.getElementById('speechBubble');
    const speechText = document.getElementById('speechText');
    const chatWindow = document.getElementById('chatWindow');
    const chatMessages = document.getElementById('chatMessages');
    const chatInput = document.getElementById('chatInput');
    const sendBtn = document.getElementById('sendChatBtn');
    const closeBtn = document.getElementById('closeChatBtn');
    const quickActionBtns = document.querySelectorAll('.quick-action-btn');

    let isOpen = false;
    let isTyping = false;
    let currentLanguage = document.documentElement.lang || 'ar';
    let mouseX = 0;
    let mouseY = 0;
    let robotX = window.innerWidth - 150;
    let robotY = window.innerHeight - 150;
    let targetX = robotX;
    let targetY = robotY;
    let isFollowing = true; // Enabled scroll following
    let lastInteraction = Date.now();

    // Language-specific messages
    const messages = {
        ar: {
            greeting: 'مرحباً! 👋',
            welcome: 'مرحباً! أنا مساعدك الذكي في MonOri AI. أتبعك أينما ذهبت! كيف يمكنني مساعدتك؟',
            title: 'مساعد MonOri الذكي',
            status: 'يتبعك الآن',
            placeholder: 'اكتب سؤالك هنا...',
            thinking: 'أفكر... 🤔',
            speaking: 'أتحدث... 🗣️',
            following: 'أتبعك أينما ذهبت! 👀',
            hover: 'مساعد ذكي - اضغط للتحدث! 🤖'
        },
        en: {
            greeting: 'Hello! 👋',
            welcome: 'Hello! I\'m your smart assistant at MonOri AI. I follow you wherever you go! How can I help you?',
            title: 'MonOri Smart Assistant',
            status: 'Following you now',
            placeholder: 'Type your question here...',
            thinking: 'Thinking... 🤔',
            speaking: 'Speaking... 🗣️',
            following: 'Following you everywhere! 👀',
            hover: 'Smart Assistant - Click to chat! 🤖'
        },
        fr: {
            greeting: 'Bonjour! 👋',
            welcome: 'Bonjour! Je suis votre assistant intelligent chez MonOri AI. Je vous suis partout où vous allez! Comment puis-je vous aider?',
            title: 'Assistant Intelligent MonOri',
            status: 'Vous suit maintenant',
            placeholder: 'Tapez votre question ici...',
            thinking: 'Je réfléchis... 🤔',
            speaking: 'Je parle... 🗣️',
            following: 'Je vous suis partout! 👀',
            hover: 'Assistant Intelligent - Cliquez pour discuter! 🤖'
        }
    };

    // Initialize robot
    function initializeRobot() {
        updateLanguage();
        setupEventListeners();
        startFollowing();
        startIdleAnimations();
        positionRobot();
    }

    // Update language
    function updateLanguage() {
        currentLanguage = document.documentElement.lang || 'ar';
        document.body.className = document.body.className.replace(/lang-\w+/g, '') + ` lang-${currentLanguage}`;

        const msg = messages[currentLanguage];
        speechText.textContent = msg.greeting;
        document.getElementById('welcomeMessage').textContent = msg.welcome;
        document.getElementById('chatTitle').textContent = msg.title;
        document.getElementById('chatStatus').textContent = msg.status;
        document.getElementById('hoverText').textContent = msg.hover;
        chatInput.placeholder = msg.placeholder;
    }

    // Position robot initially - Right side, follows scroll
    function positionRobot() {
        // Initial position based on current scroll
        const scrollY = window.scrollY;
        const viewportHeight = window.innerHeight;

        // Start at 70% down the current viewport
        robotX = window.innerWidth - 80;
        robotY = scrollY + (viewportHeight * 0.7);
        targetX = robotX;
        targetY = robotY;

        // Set initial position
        robot.style.right = '30px';
        robot.style.top = (robotY - 50) + 'px';
        robot.style.left = 'auto';
        robot.style.bottom = 'auto';
    }

    // Smart scroll following - Follows page scroll only
    function startFollowing() {
        function followScroll() {
            if (isFollowing && !isOpen) {
                // Get current scroll position
                const scrollY = window.scrollY;
                const viewportHeight = window.innerHeight;

                // Calculate ideal robot position based on scroll
                const idealY = scrollY + (viewportHeight * 0.7); // 70% down the viewport

                // Smooth movement towards ideal position
                const moveSpeed = 0.1; // Smooth following
                robotY += (idealY - robotY) * moveSpeed;

                // Keep robot within reasonable bounds
                robotY = Math.max(scrollY + 100, Math.min(scrollY + viewportHeight - 100, robotY));

                // Update robot position - fixed horizontal, following vertical
                robot.style.right = '30px';
                robot.style.top = (robotY - 50) + 'px';
                robot.style.left = 'auto';
                robot.style.bottom = 'auto';
            }

            requestAnimationFrame(followScroll);
        }

        followScroll();
    }

    // Robot expressions
    function setRobotExpression(expression) {
        robot.classList.remove('robot-speaking', 'robot-thinking', 'robot-excited');

        const leftEye = document.getElementById('leftEye');
        const rightEye = document.getElementById('rightEye');
        const speakingRings = document.getElementById('speakingRings');

        switch(expression) {
            case 'speaking':
                robot.classList.add('robot-speaking');
                speakingRings.style.opacity = '1';
                leftEye.style.background = 'radial-gradient(circle at 30% 30%, #ffffff, #10b981)';
                rightEye.style.background = 'radial-gradient(circle at 30% 30%, #ffffff, #10b981)';
                leftEye.style.boxShadow = '0 0 12px #10b981';
                rightEye.style.boxShadow = '0 0 12px #10b981';
                break;
            case 'thinking':
                robot.classList.add('robot-thinking');
                speakingRings.style.opacity = '0';
                break;
            case 'excited':
                robot.classList.add('robot-excited');
                speakingRings.style.opacity = '0.5';
                leftEye.style.background = 'radial-gradient(circle at 30% 30%, #ffffff, #8b5cf6)';
                rightEye.style.background = 'radial-gradient(circle at 30% 30%, #ffffff, #8b5cf6)';
                leftEye.style.boxShadow = '0 0 12px #8b5cf6';
                rightEye.style.boxShadow = '0 0 12px #8b5cf6';
                break;
            default:
                speakingRings.style.opacity = '0';
                leftEye.style.background = 'radial-gradient(circle at 30% 30%, #ffffff, #00d4ff)';
                rightEye.style.background = 'radial-gradient(circle at 30% 30%, #ffffff, #00d4ff)';
                leftEye.style.boxShadow = '0 0 8px #00d4ff';
                rightEye.style.boxShadow = '0 0 8px #00d4ff';
        }
    }

    // Show speech bubble
    function showSpeechBubble(text, duration = 2000) {
        speechText.textContent = text;
        speechBubble.style.opacity = '1';
        speechBubble.style.transform = 'translateX(-50%) scale(1)';

        setTimeout(() => {
            speechBubble.style.opacity = '0';
            speechBubble.style.transform = 'translateX(-50%) scale(0.8)';
        }, duration);
    }

    // Robot sounds
    function playRobotSound(type) {
        if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            switch(type) {
                case 'greeting':
                    oscillator.frequency.setValueAtTime(600, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(900, audioContext.currentTime + 0.2);
                    break;
                case 'follow':
                    oscillator.frequency.setValueAtTime(400, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(600, audioContext.currentTime + 0.15);
                    break;
                case 'click':
                    oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                    oscillator.frequency.exponentialRampToValueAtTime(400, audioContext.currentTime + 0.1);
                    break;
            }

            gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.2);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.2);
        }
    }

    // Speak text
    function speakText(text) {
        if ('speechSynthesis' in window) {
            const utterance = new SpeechSynthesisUtterance(text);

            switch(currentLanguage) {
                case 'ar':
                    utterance.lang = 'ar-SA';
                    utterance.rate = 0.9;
                    break;
                case 'en':
                    utterance.lang = 'en-US';
                    utterance.rate = 1.0;
                    break;
                case 'fr':
                    utterance.lang = 'fr-FR';
                    utterance.rate = 0.9;
                    break;
            }

            utterance.onstart = () => {
                setRobotExpression('speaking');
                showSpeechBubble(messages[currentLanguage].speaking, 1000);
            };

            utterance.onend = () => {
                setRobotExpression('neutral');
            };

            speechSynthesis.speak(utterance);
        }
    }

    // Setup event listeners
    function setupEventListeners() {
        // Track mouse movement - No following, just for interaction tracking
        document.addEventListener('mousemove', function(e) {
            // Only track for interaction timing, no position following
            lastInteraction = Date.now();
        });

        // Track scroll - Just for interaction timing
        window.addEventListener('scroll', function() {
            lastInteraction = Date.now();
            // No position adjustments - robot stays in fixed position
        });

        // Robot click
        robotContainer.addEventListener('click', function() {
            playRobotSound('greeting');

            if (isOpen) {
                closeChat();
            } else {
                openChat();
            }

            const greeting = messages[currentLanguage].greeting;
            showSpeechBubble(greeting, 2000);
            speakText(greeting);
            setRobotExpression('excited');

            setTimeout(() => {
                setRobotExpression('neutral');
            }, 3000);
        });

        // Close chat
        closeBtn.addEventListener('click', closeChat);

        // Send message
        sendBtn.addEventListener('click', sendMessage);
        chatInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') sendMessage();
        });

        // Quick actions
        quickActionBtns.forEach(btn => {
            btn.addEventListener('click', async function() {
                const questionType = this.dataset.question;
                await handleQuickAction(questionType);
            });
        });

        // Language change detection
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'attributes' && mutation.attributeName === 'lang') {
                    updateLanguage();
                }
            });
        });

        observer.observe(document.documentElement, {
            attributes: true,
            attributeFilter: ['lang']
        });

        // Window resize
        window.addEventListener('resize', function() {
            positionRobot();
        });

        // No scroll tracking - Robot stays in fixed position
    }

    function openChat() {
        isOpen = true;
        // isFollowing already disabled - no need to change
        playRobotSound('click');
        chatWindow.classList.remove('scale-0', 'opacity-0');
        chatWindow.classList.add('scale-100', 'opacity-100');
        chatInput.focus();
        setRobotExpression('excited');
    }

    function closeChat() {
        isOpen = false;
        // isFollowing stays disabled - no following
        playRobotSound('click');
        chatWindow.classList.add('scale-0', 'opacity-0');
        chatWindow.classList.remove('scale-100', 'opacity-100');
        setRobotExpression('neutral');
    }

    async function sendMessage() {
        const message = chatInput.value.trim();
        if (!message) return;

        addMessage('user', message);
        chatInput.value = '';
        setRobotExpression('thinking');
        showSpeechBubble(messages[currentLanguage].thinking, 1000);
        isTyping = true;

        showTyping();

        try {
            const response = await getAIResponse(message);
            hideTyping();
            isTyping = false;
            addMessage('robot', response);
            setRobotExpression('speaking');
            speakText(response);

            setTimeout(() => {
                setRobotExpression('neutral');
            }, 2000);
        } catch (error) {
            hideTyping();
            isTyping = false;
            const errorMsg = currentLanguage === 'ar' ? 'عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.' :
                           currentLanguage === 'en' ? 'Sorry, an error occurred. Please try again.' :
                           'Désolé, une erreur s\'est produite. Veuillez réessayer.';
            addMessage('robot', errorMsg);
            setRobotExpression('neutral');
        }
    }

    async function handleQuickAction(questionType) {
        setRobotExpression('thinking');
        isTyping = true;
        showTyping();

        try {
            const response = await fetch('/customer-service/quick-action', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    action: questionType,
                    language: currentLanguage
                })
            });

            const data = await response.json();
            hideTyping();
            isTyping = false;

            if (data.success) {
                addMessage('robot', data.response);
                setRobotExpression('speaking');
                speakText(data.response);

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 2000);
            }
        } catch (error) {
            hideTyping();
            isTyping = false;
            setRobotExpression('neutral');
        }
    }

    function addMessage(sender, text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start mb-3';

        if (sender === 'robot') {
            messageDiv.innerHTML = `
                <div class="w-6 h-6 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
                <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3 max-w-xs">
                    <p class="text-gray-900 dark:text-white text-sm">${text}</p>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="flex items-start justify-end w-full">
                    <div class="bg-cyan-600 text-white rounded-lg p-3 max-w-xs mr-2">
                        <p class="text-sm">${text}</p>
                    </div>
                    <div class="w-6 h-6 bg-gray-400 rounded-full flex-shrink-0 flex items-center justify-center text-white text-xs">👤</div>
                </div>
            `;
        }

        chatMessages.appendChild(messageDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function showTyping() {
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'flex items-start mb-3';
        typingDiv.innerHTML = `
            <div class="w-6 h-6 bg-gradient-to-br from-cyan-400 to-blue-500 rounded-full mr-2 flex-shrink-0 flex items-center justify-center text-white text-xs">🤖</div>
            <div class="bg-gray-100 dark:bg-gray-700 rounded-lg p-3">
                <div class="flex space-x-1">
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
            </div>
        `;

        chatMessages.appendChild(typingDiv);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    function hideTyping() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    async function getAIResponse(message) {
        try {
            const response = await fetch('/customer-service/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || ''
                },
                body: JSON.stringify({
                    message: message,
                    language: currentLanguage
                })
            });

            const data = await response.json();

            if (data.success) {
                return data.response;
            } else {
                throw new Error(data.message || 'Service error');
            }
        } catch (error) {
            console.error('AI Response Error:', error);
            const errorMsg = currentLanguage === 'ar' ? 'عذراً، حدث خطأ في الاتصال. يرجى المحاولة مرة أخرى.' :
                           currentLanguage === 'en' ? 'Sorry, connection error. Please try again.' :
                           'Désolé, erreur de connexion. Veuillez réessayer.';
            return errorMsg;
        }
    }

    // Idle animations
    function startIdleAnimations() {
        // Random expressions when not busy
        setInterval(() => {
            if (!isOpen && !isTyping && Math.random() < 0.3) {
                const expressions = ['neutral', 'excited'];
                const randomExpression = expressions[Math.floor(Math.random() * expressions.length)];
                setRobotExpression(randomExpression);

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 2000);
            }
        }, 8000);

        // Random greetings when following
        setInterval(() => {
            if (!isOpen && isFollowing && Math.random() < 0.2) {
                const greeting = messages[currentLanguage].greeting;
                showSpeechBubble(greeting, 1500);
                playRobotSound('follow');
                setRobotExpression('excited');

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 2000);
            }
        }, 15000);

        // Encourage interaction if user hasn't interacted recently
        setInterval(() => {
            if (!isOpen && Date.now() - lastInteraction > 60000) {
                const encouragement = currentLanguage === 'ar' ? 'هل تحتاج مساعدة؟ 🤔' :
                                    currentLanguage === 'en' ? 'Need any help? 🤔' :
                                    'Besoin d\'aide? 🤔';
                showSpeechBubble(encouragement, 3000);
                setRobotExpression('thinking');

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 3000);
            }
        }, 30000);
    }

    // Initialize
    initializeRobot();

    // Ensure robot stays visible and responsive
    window.addEventListener('scroll', function() {
        robot.style.position = 'fixed';
        robot.style.zIndex = '2147483647';
    });

    window.addEventListener('resize', function() {
        robot.style.position = 'fixed';
        robot.style.zIndex = '2147483647';
        positionRobot();
    });

    // Handle page visibility changes
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'visible') {
            lastInteraction = Date.now();
            if (!isOpen) {
                const welcomeBack = currentLanguage === 'ar' ? 'أهلاً بعودتك! 👋' :
                                 currentLanguage === 'en' ? 'Welcome back! 👋' :
                                 'Bon retour! 👋';
                showSpeechBubble(welcomeBack, 2000);
                setRobotExpression('excited');

                setTimeout(() => {
                    setRobotExpression('neutral');
                }, 2000);
            }
        }
    });
});
</script>
