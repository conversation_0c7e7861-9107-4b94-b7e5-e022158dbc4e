<!-- Language Switcher Modal -->
<div id="language-modal" class="hidden fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75 dark:bg-gray-900 dark:bg-opacity-75" onclick="toggleLanguageModal()"></div>

        <!-- Modal panel -->
        <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white dark:bg-gray-800 shadow-xl rounded-2xl">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">
                    {{ __('messages.language') }}
                </h3>
                <button onclick="toggleLanguageModal()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>

            <div class="space-y-2">
                <!-- Arabic -->
                <a href="{{ url('/set-language/ar') }}" onclick="setTimeout(() => window.location.reload(), 200)"
                   class="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 {{ app()->getLocale() === 'ar' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : '' }}">
                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                        <div class="w-6 h-6 rounded-full bg-gradient-to-r from-green-500 to-red-500 flex items-center justify-center">
                            <span class="text-xs font-bold text-white">ع</span>
                        </div>
                        <div>
                            <div class="font-medium">{{ __('messages.arabic') }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">العربية</div>
                        </div>
                    </div>
                    @if(app()->getLocale() === 'ar')
                        <svg class="w-5 h-5 ml-auto rtl:mr-auto rtl:ml-0 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </a>

                <!-- English -->
                <a href="{{ url('/set-language/en') }}" onclick="setTimeout(() => window.location.reload(), 200)"
                   class="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 {{ app()->getLocale() === 'en' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : '' }}">
                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                        <div class="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-red-500 flex items-center justify-center">
                            <span class="text-xs font-bold text-white">EN</span>
                        </div>
                        <div>
                            <div class="font-medium">{{ __('messages.english') }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">English</div>
                        </div>
                    </div>
                    @if(app()->getLocale() === 'en')
                        <svg class="w-5 h-5 ml-auto rtl:mr-auto rtl:ml-0 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </a>

                <!-- French -->
                <a href="{{ url('/set-language/fr') }}" onclick="setTimeout(() => window.location.reload(), 200)"
                   class="flex items-center w-full px-4 py-3 text-sm text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors duration-200 {{ app()->getLocale() === 'fr' ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : '' }}">
                    <div class="flex items-center space-x-3 rtl:space-x-reverse">
                        <div class="w-6 h-6 rounded-full bg-gradient-to-r from-blue-500 to-white to-red-500 flex items-center justify-center">
                            <span class="text-xs font-bold text-blue-600">FR</span>
                        </div>
                        <div>
                            <div class="font-medium">{{ __('messages.french') }}</div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">Français</div>
                        </div>
                    </div>
                    @if(app()->getLocale() === 'fr')
                        <svg class="w-5 h-5 ml-auto rtl:mr-auto rtl:ml-0 text-blue-600 dark:text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                    @endif
                </a>
            </div>
        </div>
    </div>
</div>
