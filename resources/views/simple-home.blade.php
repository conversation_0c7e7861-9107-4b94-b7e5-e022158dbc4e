<!DOCTYPE html>
<html lang="{{ app()->getLocale() }}" dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    
    <title>MonOri AI - AI-Powered Career Guidance</title>
    <meta name="description" content="Discover your career path with AI-powered personality analysis, CV generation, and job recommendations">
    
    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('favicon.ico') }}">
    
    <!-- Styles -->
    <link rel="stylesheet" href="{{ asset('css/style.css') }}?v={{ time() }}">
</head>

<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="header-content">
                <div class="logo">MonOri AI</div>
                
                <nav>
                    <ul>
                        <li><a href="{{ route('home') }}">Home</a></li>
                        <li><a href="{{ route('services') }}">Services</a></li>
                        <li><a href="{{ route('about') }}">About</a></li>
                        <li><a href="{{ route('contact') }}">Contact</a></li>
                    </ul>
                </nav>
                
                <div class="language-selector">
                    <select onchange="changeLanguage(this.value)">
                        <option value="en" {{ app()->getLocale() === 'en' ? 'selected' : '' }}>English</option>
                        <option value="ar" {{ app()->getLocale() === 'ar' ? 'selected' : '' }}>العربية</option>
                        <option value="fr" {{ app()->getLocale() === 'fr' ? 'selected' : '' }}>Français</option>
                    </select>
                </div>
                
                <div>
                    <a href="{{ route('login') }}" class="btn btn-secondary btn-compact">Login</a>
                    <a href="{{ route('register') }}" class="btn btn-primary btn-compact">Get Started</a>
                </div>
            </div>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <h1 class="fade-in-up">MonOri AI - Your Career Guide</h1>
            <p class="fade-in-up">Discover your perfect career path with AI-powered personality analysis, professional CV generation, and personalized job recommendations.</p>
            <a href="{{ route('register') }}" class="btn btn-primary">Get Started</a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="p-8">
        <div class="container">
            <h2 class="text-center mb-8">Our Services</h2>

            <div class="grid grid-3">
                <!-- Personality Analysis -->
                <div class="card">
                    <h3 class="mb-4">Personality Analysis</h3>
                    <p class="mb-4">Discover your personality traits and career preferences with our AI-powered analysis.</p>
                    <a href="{{ route('personality-analysis') }}" class="btn btn-secondary">Learn More</a>
                </div>

                <!-- CV Generation -->
                <div class="card">
                    <h3 class="mb-4">CV Generation</h3>
                    <p class="mb-4">Create professional CVs tailored to your skills and target positions.</p>
                    <a href="{{ route('cv-generator') }}" class="btn btn-secondary">Learn More</a>
                </div>

                <!-- Job Recommendations -->
                <div class="card">
                    <h3 class="mb-4">Job Recommendations</h3>
                    <p class="mb-4">Get personalized job recommendations based on your profile and preferences.</p>
                    <a href="{{ route('job-recommendations') }}" class="btn btn-secondary">Learn More</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="p-8 text-center">
        <div class="container">
            <p>&copy; 2024 MonOri AI. All rights reserved.</p>
        </div>
    </footer>

    <script>
        function changeLanguage(locale) {
            window.location.href = '{{ url('/') }}/' + locale;
        }
    </script>
</body>
</html>
