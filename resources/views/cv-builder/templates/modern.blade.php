<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $cvData['personal_info']['full_name'] ?? __('messages.cv_title') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            font-size: 14px;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            min-height: 297mm;
        }
        
        .cv-header {
            text-align: center;
            border-bottom: 3px solid #3B82F6;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .cv-name {
            font-size: 32px;
            font-weight: bold;
            color: #1F2937;
            margin-bottom: 10px;
        }
        
        .cv-contact {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            color: #6B7280;
            font-size: 12px;
        }
        
        .cv-contact span {
            display: inline-block;
        }
        
        .cv-section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        
        .cv-section-title {
            font-size: 18px;
            font-weight: bold;
            color: #3B82F6;
            border-bottom: 2px solid #E5E7EB;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .cv-item {
            margin-bottom: 15px;
            page-break-inside: avoid;
        }
        
        .cv-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
        }
        
        .cv-item-title {
            font-weight: bold;
            color: #1F2937;
            font-size: 14px;
        }
        
        .cv-item-subtitle {
            color: #6B7280;
            font-style: italic;
            font-size: 13px;
        }
        
        .cv-item-date {
            color: #9CA3AF;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .cv-item-description {
            margin-top: 5px;
            color: #4B5563;
            font-size: 13px;
            line-height: 1.5;
        }
        
        .cv-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .cv-skill {
            background: #F3F4F6;
            padding: 4px 10px;
            border-radius: 12px;
            font-size: 12px;
            color: #374151;
            display: inline-block;
        }
        
        .cv-languages {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .cv-language {
            display: flex;
            justify-content: space-between;
            padding: 5px 0;
            border-bottom: 1px solid #E5E7EB;
            font-size: 13px;
        }
        
        .cv-language-name {
            font-weight: 500;
        }
        
        .cv-language-level {
            color: #6B7280;
        }
        
        .cv-summary {
            background: #F8FAFC;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3B82F6;
            margin-bottom: 20px;
            font-size: 13px;
            line-height: 1.6;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .main-content {
            /* Main content styles */
        }
        
        .sidebar {
            /* Sidebar styles */
        }
        
        @page {
            margin: 15mm;
            size: A4;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="cv-header">
            <h1 class="cv-name">{{ $cvData['personal_info']['full_name'] ?? '' }}</h1>
            <div class="cv-contact">
                @if(!empty($cvData['personal_info']['email']))
                    <span>✉ {{ $cvData['personal_info']['email'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['phone']))
                    <span>📱 {{ $cvData['personal_info']['phone'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['address']))
                    <span>📍 {{ $cvData['personal_info']['address'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['linkedin']))
                    <span>💼 {{ $cvData['personal_info']['linkedin'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['website']))
                    <span>🌐 {{ $cvData['personal_info']['website'] }}</span>
                @endif
            </div>
        </div>

        <div class="two-column">
            <div class="main-content">
                <!-- Summary -->
                @if(!empty($cvData['summary']))
                <div class="cv-section">
                    <h2 class="cv-section-title">نبذة مختصرة</h2>
                    <div class="cv-summary">
                        {{ $cvData['summary'] }}
                    </div>
                </div>
                @endif

                <!-- Experience -->
                @if(!empty($cvData['experience']) && count($cvData['experience']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">الخبرات العملية</h2>
                    @foreach($cvData['experience'] as $exp)
                        @if(!empty($exp['company']) && !empty($exp['position']))
                        <div class="cv-item">
                            <div class="cv-item-header">
                                <div>
                                    <div class="cv-item-title">{{ $exp['position'] }}</div>
                                    <div class="cv-item-subtitle">{{ $exp['company'] }}</div>
                                </div>
                                @if(!empty($exp['start_date']))
                                <div class="cv-item-date">
                                    {{ date('Y/m', strtotime($exp['start_date'])) }} - 
                                    {{ !empty($exp['end_date']) ? date('Y/m', strtotime($exp['end_date'])) : __('messages.now') }}
                                </div>
                                @endif
                            </div>
                            @if(!empty($exp['description']))
                            <div class="cv-item-description">{{ $exp['description'] }}</div>
                            @endif
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif

                <!-- Education -->
                @if(!empty($cvData['education']) && count($cvData['education']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">{{ __("messages.education") }}</h2>
                    @foreach($cvData['education'] as $edu)
                        @if(!empty($edu['institution']) && !empty($edu['degree']))
                        <div class="cv-item">
                            <div class="cv-item-header">
                                <div>
                                    <div class="cv-item-title">{{ $edu['degree'] }}</div>
                                    <div class="cv-item-subtitle">
                                        {{ $edu['institution'] }}
                                        @if(!empty($edu['field']))
                                            - {{ $edu['field'] }}
                                        @endif
                                    </div>
                                </div>
                                @if(!empty($edu['end_date']))
                                <div class="cv-item-date">{{ date('Y', strtotime($edu['end_date'])) }}</div>
                                @endif
                            </div>
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif
            </div>

            <div class="sidebar">
                <!-- Skills -->
                @if(!empty($cvData['skills']) && count($cvData['skills']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">{{ __("messages.skills") }}</h2>
                    <div class="cv-skills">
                        @foreach($cvData['skills'] as $skill)
                            @if(!empty($skill))
                            <span class="cv-skill">{{ $skill }}</span>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Languages -->
                @if(!empty($cvData['languages']) && count($cvData['languages']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">{{ __("messages.languages") }}</h2>
                    <div class="cv-languages">
                        @foreach($cvData['languages'] as $lang)
                            @if(!empty($lang['language']))
                            <div class="cv-language">
                                <span class="cv-language-name">{{ $lang['language'] }}</span>
                                <span class="cv-language-level">{{ $lang['level'] ?? '' }}</span>
                            </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Certifications -->
                @if(!empty($cvData['certifications']) && count($cvData['certifications']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">الشهادات والدورات</h2>
                    @foreach($cvData['certifications'] as $cert)
                        @if(!empty($cert['name']))
                        <div class="cv-item">
                            <div class="cv-item-header">
                                <div>
                                    <div class="cv-item-title">{{ $cert['name'] }}</div>
                                    @if(!empty($cert['issuer']))
                                    <div class="cv-item-subtitle">{{ $cert['issuer'] }}</div>
                                    @endif
                                </div>
                                @if(!empty($cert['date']))
                                <div class="cv-item-date">{{ date('Y/m', strtotime($cert['date'])) }}</div>
                                @endif
                            </div>
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
