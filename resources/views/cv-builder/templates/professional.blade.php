<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $cvData['personal_info']['full_name'] ?? __('messages.cv_title') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.6;
            color: #2C3E50;
            background: white;
            font-size: 14px;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 15mm;
            min-height: 297mm;
        }
        
        .cv-header {
            text-align: center;
            border-bottom: 4px solid #2C3E50;
            padding-bottom: 25px;
            margin-bottom: 35px;
            background: #F8F9FA;
            padding: 25px;
            margin: -15mm -15mm 35px -15mm;
        }
        
        .cv-name {
            font-size: 36px;
            font-weight: bold;
            color: #2C3E50;
            margin-bottom: 8px;
            letter-spacing: 1px;
        }
        
        .cv-title {
            font-size: 16px;
            color: #7F8C8D;
            margin-bottom: 15px;
            font-style: italic;
        }
        
        .cv-contact {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 25px;
            color: #34495E;
            font-size: 12px;
        }
        
        .cv-contact span {
            display: inline-flex;
            align-items: center;
            font-weight: 500;
        }
        
        .cv-section {
            margin-bottom: 30px;
            page-break-inside: avoid;
        }
        
        .cv-section-title {
            font-size: 20px;
            font-weight: bold;
            color: #2C3E50;
            border-bottom: 3px solid #3498DB;
            padding-bottom: 8px;
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .cv-item {
            margin-bottom: 20px;
            page-break-inside: avoid;
            border-left: 3px solid #ECF0F1;
            padding-left: 15px;
        }
        
        .cv-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 8px;
        }
        
        .cv-item-title {
            font-weight: bold;
            color: #2C3E50;
            font-size: 16px;
        }
        
        .cv-item-subtitle {
            color: #7F8C8D;
            font-size: 14px;
            margin-top: 2px;
        }
        
        .cv-item-date {
            color: #95A5A6;
            font-size: 12px;
            white-space: nowrap;
            background: #ECF0F1;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: 500;
        }
        
        .cv-item-description {
            margin-top: 8px;
            color: #34495E;
            font-size: 13px;
            line-height: 1.6;
        }
        
        .cv-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .cv-skill {
            background: #3498DB;
            color: white;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
        }
        
        .cv-languages {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .cv-language {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 2px solid #ECF0F1;
            font-size: 13px;
        }
        
        .cv-language-name {
            font-weight: 600;
            color: #2C3E50;
        }
        
        .cv-language-level {
            color: #7F8C8D;
            background: #ECF0F1;
            padding: 2px 8px;
            border-radius: 10px;
            font-size: 11px;
        }
        
        .cv-summary {
            background: #F8F9FA;
            padding: 20px;
            border-radius: 8px;
            border-left: 5px solid #3498DB;
            margin-bottom: 25px;
            font-size: 14px;
            line-height: 1.7;
            color: #2C3E50;
        }
        
        .sidebar-layout {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
        }
        
        .main-content {
            /* Main content styles */
        }
        
        .sidebar {
            background: #F8F9FA;
            padding: 20px;
            border-radius: 8px;
            height: fit-content;
        }
        
        .sidebar .cv-section-title {
            color: #3498DB;
            font-size: 16px;
            border-bottom: 2px solid #3498DB;
        }
        
        @page {
            margin: 10mm;
            size: A4;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="cv-header">
            <h1 class="cv-name">{{ $cvData['personal_info']['full_name'] ?? '' }}</h1>
            @if(!empty($cvData['summary']))
                <div class="cv-title">{{ Str::limit($cvData['summary'], 100) }}</div>
            @endif
            <div class="cv-contact">
                @if(!empty($cvData['personal_info']['email']))
                    <span>✉ {{ $cvData['personal_info']['email'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['phone']))
                    <span>📱 {{ $cvData['personal_info']['phone'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['address']))
                    <span>📍 {{ $cvData['personal_info']['address'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['linkedin']))
                    <span>💼 {{ $cvData['personal_info']['linkedin'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['website']))
                    <span>🌐 {{ $cvData['personal_info']['website'] }}</span>
                @endif
            </div>
        </div>

        <div class="sidebar-layout">
            <div class="main-content">
                <!-- Summary -->
                @if(!empty($cvData['summary']))
                <div class="cv-section">
                    <h2 class="cv-section-title">الملف الشخصي</h2>
                    <div class="cv-summary">
                        {{ $cvData['summary'] }}
                    </div>
                </div>
                @endif

                <!-- Experience -->
                @if(!empty($cvData['experience']) && count($cvData['experience']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">الخبرات المهنية</h2>
                    @foreach($cvData['experience'] as $exp)
                        @if(!empty($exp['company']) && !empty($exp['position']))
                        <div class="cv-item">
                            <div class="cv-item-header">
                                <div>
                                    <div class="cv-item-title">{{ $exp['position'] }}</div>
                                    <div class="cv-item-subtitle">{{ $exp['company'] }}</div>
                                </div>
                                @if(!empty($exp['start_date']))
                                <div class="cv-item-date">
                                    {{ date('Y/m', strtotime($exp['start_date'])) }} - 
                                    {{ !empty($exp['end_date']) ? date('Y/m', strtotime($exp['end_date'])) : __('messages.now') }}
                                </div>
                                @endif
                            </div>
                            @if(!empty($exp['description']))
                            <div class="cv-item-description">{{ $exp['description'] }}</div>
                            @endif
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif

                <!-- Education -->
                @if(!empty($cvData['education']) && count($cvData['education']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">{{ __("messages.education") }}</h2>
                    @foreach($cvData['education'] as $edu)
                        @if(!empty($edu['institution']) && !empty($edu['degree']))
                        <div class="cv-item">
                            <div class="cv-item-header">
                                <div>
                                    <div class="cv-item-title">{{ $edu['degree'] }}</div>
                                    <div class="cv-item-subtitle">
                                        {{ $edu['institution'] }}
                                        @if(!empty($edu['field']))
                                            - {{ $edu['field'] }}
                                        @endif
                                    </div>
                                </div>
                                @if(!empty($edu['end_date']))
                                <div class="cv-item-date">{{ date('Y', strtotime($edu['end_date'])) }}</div>
                                @endif
                            </div>
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif
            </div>

            <div class="sidebar">
                <!-- Skills -->
                @if(!empty($cvData['skills']) && count($cvData['skills']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">{{ __("messages.skills") }}</h2>
                    <div class="cv-skills">
                        @foreach($cvData['skills'] as $skill)
                            @if(!empty($skill))
                            <span class="cv-skill">{{ $skill }}</span>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Languages -->
                @if(!empty($cvData['languages']) && count($cvData['languages']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">{{ __("messages.languages") }}</h2>
                    <div class="cv-languages">
                        @foreach($cvData['languages'] as $lang)
                            @if(!empty($lang['language']))
                            <div class="cv-language">
                                <span class="cv-language-name">{{ $lang['language'] }}</span>
                                <span class="cv-language-level">{{ $lang['level'] ?? '' }}</span>
                            </div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Certifications -->
                @if(!empty($cvData['certifications']) && count($cvData['certifications']) > 0)
                <div class="cv-section">
                    <h2 class="cv-section-title">{{ __("messages.certifications") }}</h2>
                    @foreach($cvData['certifications'] as $cert)
                        @if(!empty($cert['name']))
                        <div class="cv-item">
                            <div class="cv-item-header">
                                <div>
                                    <div class="cv-item-title">{{ $cert['name'] }}</div>
                                    @if(!empty($cert['issuer']))
                                    <div class="cv-item-subtitle">{{ $cert['issuer'] }}</div>
                                    @endif
                                </div>
                                @if(!empty($cert['date']))
                                <div class="cv-item-date">{{ date('Y/m', strtotime($cert['date'])) }}</div>
                                @endif
                            </div>
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
