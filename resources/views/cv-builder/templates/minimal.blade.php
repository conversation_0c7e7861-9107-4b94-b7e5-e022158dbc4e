<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $cvData['personal_info']['full_name'] ?? __('messages.cv_title') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.7;
            color: #333;
            background: white;
            font-size: 14px;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 25mm;
            min-height: 297mm;
        }
        
        .cv-header {
            text-align: left;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 1px solid #ddd;
        }
        
        .cv-name {
            font-size: 28px;
            font-weight: 300;
            color: #222;
            margin-bottom: 5px;
            letter-spacing: 2px;
        }
        
        .cv-contact {
            color: #666;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .cv-contact span {
            display: block;
            margin-bottom: 2px;
        }
        
        .cv-section {
            margin-bottom: 35px;
            page-break-inside: avoid;
        }
        
        .cv-section-title {
            font-size: 16px;
            font-weight: 600;
            color: #222;
            margin-bottom: 15px;
            text-transform: uppercase;
            letter-spacing: 1px;
        }
        
        .cv-item {
            margin-bottom: 20px;
            page-break-inside: avoid;
        }
        
        .cv-item-header {
            display: flex;
            justify-content: space-between;
            align-items: baseline;
            margin-bottom: 5px;
        }
        
        .cv-item-title {
            font-weight: 600;
            color: #222;
            font-size: 14px;
        }
        
        .cv-item-subtitle {
            color: #666;
            font-size: 13px;
            margin-top: 2px;
        }
        
        .cv-item-date {
            color: #999;
            font-size: 12px;
            white-space: nowrap;
        }
        
        .cv-item-description {
            margin-top: 8px;
            color: #555;
            font-size: 13px;
            line-height: 1.6;
        }
        
        .cv-skills {
            line-height: 1.8;
        }
        
        .cv-skill {
            display: inline;
            font-size: 13px;
            color: #555;
        }
        
        .cv-skill:not(:last-child)::after {
            content: ' • ';
            color: #ccc;
        }
        
        .cv-languages {
            line-height: 1.8;
        }
        
        .cv-language {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
            font-size: 13px;
        }
        
        .cv-language-name {
            color: #333;
        }
        
        .cv-language-level {
            color: #666;
        }
        
        .cv-summary {
            margin-bottom: 25px;
            font-size: 14px;
            line-height: 1.7;
            color: #444;
            text-align: justify;
        }
        
        .single-column {
            /* Single column layout */
        }
        
        @page {
            margin: 20mm;
            size: A4;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="cv-header">
            <h1 class="cv-name">{{ $cvData['personal_info']['full_name'] ?? '' }}</h1>
            <div class="cv-contact">
                @if(!empty($cvData['personal_info']['email']))
                    <span>{{ $cvData['personal_info']['email'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['phone']))
                    <span>{{ $cvData['personal_info']['phone'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['address']))
                    <span>{{ $cvData['personal_info']['address'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['linkedin']))
                    <span>{{ $cvData['personal_info']['linkedin'] }}</span>
                @endif
                @if(!empty($cvData['personal_info']['website']))
                    <span>{{ $cvData['personal_info']['website'] }}</span>
                @endif
            </div>
        </div>

        <div class="single-column">
            <!-- Summary -->
            @if(!empty($cvData['summary']))
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.professional_summary') }}</h2>
                <div class="cv-summary">
                    {{ $cvData['summary'] }}
                </div>
            </div>
            @endif

            <!-- Experience -->
            @if(!empty($cvData['experience']) && count($cvData['experience']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.work_experience') }}</h2>
                @foreach($cvData['experience'] as $exp)
                    @if(!empty($exp['company']) && !empty($exp['position']))
                    <div class="cv-item">
                        <div class="cv-item-header">
                            <div>
                                <div class="cv-item-title">{{ $exp['position'] }}</div>
                                <div class="cv-item-subtitle">{{ $exp['company'] }}</div>
                            </div>
                            @if(!empty($exp['start_date']))
                            <div class="cv-item-date">
                                {{ date('Y', strtotime($exp['start_date'])) }}–{{ !empty($exp['end_date']) ? date('Y', strtotime($exp['end_date'])) : __('messages.now') }}
                            </div>
                            @endif
                        </div>
                        @if(!empty($exp['description']))
                        <div class="cv-item-description">{{ $exp['description'] }}</div>
                        @endif
                    </div>
                    @endif
                @endforeach
            </div>
            @endif

            <!-- Education -->
            @if(!empty($cvData['education']) && count($cvData['education']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.education') }}</h2>
                @foreach($cvData['education'] as $edu)
                    @if(!empty($edu['institution']) && !empty($edu['degree']))
                    <div class="cv-item">
                        <div class="cv-item-header">
                            <div>
                                <div class="cv-item-title">{{ $edu['degree'] }}</div>
                                <div class="cv-item-subtitle">
                                    {{ $edu['institution'] }}@if(!empty($edu['field'])), {{ $edu['field'] }}@endif
                                </div>
                            </div>
                            @if(!empty($edu['end_date']))
                            <div class="cv-item-date">{{ date('Y', strtotime($edu['end_date'])) }}</div>
                            @endif
                        </div>
                    </div>
                    @endif
                @endforeach
            </div>
            @endif

            <!-- Skills -->
            @if(!empty($cvData['skills']) && count($cvData['skills']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.skills') }}</h2>
                <div class="cv-skills">
                    @foreach($cvData['skills'] as $skill)
                        @if(!empty($skill))
                        <span class="cv-skill">{{ $skill }}</span>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Languages -->
            @if(!empty($cvData['languages']) && count($cvData['languages']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.languages') }}</h2>
                <div class="cv-languages">
                    @foreach($cvData['languages'] as $lang)
                        @if(!empty($lang['language']))
                        <div class="cv-language">
                            <span class="cv-language-name">{{ $lang['language'] }}</span>
                            <span class="cv-language-level">{{ $lang['level'] ?? '' }}</span>
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Certifications -->
            @if(!empty($cvData['certifications']) && count($cvData['certifications']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.certifications') }}</h2>
                @foreach($cvData['certifications'] as $cert)
                    @if(!empty($cert['name']))
                    <div class="cv-item">
                        <div class="cv-item-header">
                            <div>
                                <div class="cv-item-title">{{ $cert['name'] }}</div>
                                @if(!empty($cert['issuer']))
                                <div class="cv-item-subtitle">{{ $cert['issuer'] }}</div>
                                @endif
                            </div>
                            @if(!empty($cert['date']))
                            <div class="cv-item-date">{{ date('Y', strtotime($cert['date'])) }}</div>
                            @endif
                        </div>
                    </div>
                    @endif
                @endforeach
            </div>
            @endif
        </div>
    </div>
</body>
</html>
