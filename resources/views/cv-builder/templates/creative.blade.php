<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $cvData['personal_info']['full_name'] ?? __('messages.cv_title') }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'DejaVu Sans', Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            background: white;
            font-size: 14px;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            min-height: 297mm;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
        }
        
        .cv-content {
            background: white;
            margin: 20mm 15mm 15mm 15mm;
            padding: 0;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        
        .cv-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        
        .cv-header::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 0;
            height: 0;
            border-left: 20px solid transparent;
            border-right: 20px solid transparent;
            border-top: 20px solid #764ba2;
        }
        
        .cv-name {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .cv-title {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 20px;
            font-style: italic;
        }
        
        .cv-contact {
            display: flex;
            justify-content: center;
            flex-wrap: wrap;
            gap: 20px;
            font-size: 12px;
        }
        
        .cv-contact span {
            background: rgba(255,255,255,0.2);
            padding: 5px 10px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .cv-body {
            padding: 30px;
        }
        
        .cv-section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        
        .cv-section-title {
            font-size: 18px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
            position: relative;
            padding-left: 20px;
        }
        
        .cv-section-title::before {
            content: '';
            position: absolute;
            left: 0;
            top: 50%;
            transform: translateY(-50%);
            width: 12px;
            height: 12px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
        }
        
        .cv-item {
            margin-bottom: 18px;
            page-break-inside: avoid;
            position: relative;
            padding-left: 25px;
        }
        
        .cv-item::before {
            content: '';
            position: absolute;
            left: 0;
            top: 8px;
            width: 8px;
            height: 8px;
            background: #667eea;
            border-radius: 50%;
        }
        
        .cv-item-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 5px;
        }
        
        .cv-item-title {
            font-weight: bold;
            color: #333;
            font-size: 15px;
        }
        
        .cv-item-subtitle {
            color: #666;
            font-size: 13px;
            margin-top: 2px;
        }
        
        .cv-item-date {
            color: #999;
            font-size: 11px;
            background: #f0f0f0;
            padding: 3px 8px;
            border-radius: 10px;
            white-space: nowrap;
        }
        
        .cv-item-description {
            margin-top: 5px;
            color: #555;
            font-size: 13px;
            line-height: 1.5;
        }
        
        .cv-skills {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .cv-skill {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .cv-languages {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }
        
        .cv-language {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 8px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 13px;
            border-left: 3px solid #667eea;
        }
        
        .cv-language-name {
            font-weight: 600;
            color: #333;
        }
        
        .cv-language-level {
            color: #667eea;
            font-size: 11px;
            background: white;
            padding: 2px 6px;
            border-radius: 8px;
        }
        
        .cv-summary {
            background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 30px;
        }
        
        .sidebar {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            height: fit-content;
        }
        
        .sidebar .cv-section-title {
            color: #764ba2;
        }
        
        .sidebar .cv-section-title::before {
            background: #764ba2;
        }
        
        @page {
            margin: 0;
            size: A4;
        }
        
        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <div class="cv-content">
            <!-- Header -->
            <div class="cv-header">
                <h1 class="cv-name">{{ $cvData['personal_info']['full_name'] ?? '' }}</h1>
                @if(!empty($cvData['summary']))
                    <div class="cv-title">{{ Str::limit($cvData['summary'], 80) }}</div>
                @endif
                <div class="cv-contact">
                    @if(!empty($cvData['personal_info']['email']))
                        <span>✉ {{ $cvData['personal_info']['email'] }}</span>
                    @endif
                    @if(!empty($cvData['personal_info']['phone']))
                        <span>📱 {{ $cvData['personal_info']['phone'] }}</span>
                    @endif
                    @if(!empty($cvData['personal_info']['address']))
                        <span>📍 {{ $cvData['personal_info']['address'] }}</span>
                    @endif
                    @if(!empty($cvData['personal_info']['linkedin']))
                        <span>💼 LinkedIn</span>
                    @endif
                    @if(!empty($cvData['personal_info']['website']))
                        <span>🌐 Website</span>
                    @endif
                </div>
            </div>

            <div class="cv-body">
                <div class="two-column">
                    <div class="main-content">
                        <!-- Summary -->
                        @if(!empty($cvData['summary']))
                        <div class="cv-section">
                            <h2 class="cv-section-title">نبذة إبداعية</h2>
                            <div class="cv-summary">
                                {{ $cvData['summary'] }}
                            </div>
                        </div>
                        @endif

                        <!-- Experience -->
                        @if(!empty($cvData['experience']) && count($cvData['experience']) > 0)
                        <div class="cv-section">
                            <h2 class="cv-section-title">رحلة الإبداع المهني</h2>
                            @foreach($cvData['experience'] as $exp)
                                @if(!empty($exp['company']) && !empty($exp['position']))
                                <div class="cv-item">
                                    <div class="cv-item-header">
                                        <div>
                                            <div class="cv-item-title">{{ $exp['position'] }}</div>
                                            <div class="cv-item-subtitle">{{ $exp['company'] }}</div>
                                        </div>
                                        @if(!empty($exp['start_date']))
                                        <div class="cv-item-date">
                                            {{ date('Y/m', strtotime($exp['start_date'])) }} - 
                                            {{ !empty($exp['end_date']) ? date('Y/m', strtotime($exp['end_date'])) : __('messages.now') }}
                                        </div>
                                        @endif
                                    </div>
                                    @if(!empty($exp['description']))
                                    <div class="cv-item-description">{{ $exp['description'] }}</div>
                                    @endif
                                </div>
                                @endif
                            @endforeach
                        </div>
                        @endif

                        <!-- Education -->
                        @if(!empty($cvData['education']) && count($cvData['education']) > 0)
                        <div class="cv-section">
                            <h2 class="cv-section-title">التعلم والنمو</h2>
                            @foreach($cvData['education'] as $edu)
                                @if(!empty($edu['institution']) && !empty($edu['degree']))
                                <div class="cv-item">
                                    <div class="cv-item-header">
                                        <div>
                                            <div class="cv-item-title">{{ $edu['degree'] }}</div>
                                            <div class="cv-item-subtitle">
                                                {{ $edu['institution'] }}
                                                @if(!empty($edu['field']))
                                                    - {{ $edu['field'] }}
                                                @endif
                                            </div>
                                        </div>
                                        @if(!empty($edu['end_date']))
                                        <div class="cv-item-date">{{ date('Y', strtotime($edu['end_date'])) }}</div>
                                        @endif
                                    </div>
                                </div>
                                @endif
                            @endforeach
                        </div>
                        @endif
                    </div>

                    <div class="sidebar">
                        <!-- Skills -->
                        @if(!empty($cvData['skills']) && count($cvData['skills']) > 0)
                        <div class="cv-section">
                            <h2 class="cv-section-title">أدوات الإبداع</h2>
                            <div class="cv-skills">
                                @foreach($cvData['skills'] as $skill)
                                    @if(!empty($skill))
                                    <span class="cv-skill">{{ $skill }}</span>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Languages -->
                        @if(!empty($cvData['languages']) && count($cvData['languages']) > 0)
                        <div class="cv-section">
                            <h2 class="cv-section-title">لغات التواصل</h2>
                            <div class="cv-languages">
                                @foreach($cvData['languages'] as $lang)
                                    @if(!empty($lang['language']))
                                    <div class="cv-language">
                                        <span class="cv-language-name">{{ $lang['language'] }}</span>
                                        <span class="cv-language-level">{{ $lang['level'] ?? '' }}</span>
                                    </div>
                                    @endif
                                @endforeach
                            </div>
                        </div>
                        @endif

                        <!-- Certifications -->
                        @if(!empty($cvData['certifications']) && count($cvData['certifications']) > 0)
                        <div class="cv-section">
                            <h2 class="cv-section-title">إنجازات وشهادات</h2>
                            @foreach($cvData['certifications'] as $cert)
                                @if(!empty($cert['name']))
                                <div class="cv-item">
                                    <div class="cv-item-header">
                                        <div>
                                            <div class="cv-item-title">{{ $cert['name'] }}</div>
                                            @if(!empty($cert['issuer']))
                                            <div class="cv-item-subtitle">{{ $cert['issuer'] }}</div>
                                            @endif
                                        </div>
                                        @if(!empty($cert['date']))
                                        <div class="cv-item-date">{{ date('Y/m', strtotime($cert['date'])) }}</div>
                                        @endif
                                    </div>
                                </div>
                                @endif
                            @endforeach
                        </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
