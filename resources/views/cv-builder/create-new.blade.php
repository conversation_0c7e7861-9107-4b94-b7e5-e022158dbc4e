@extends('layouts.app')

@section('title', __('messages.create_cv_title'))
@section('description', __('messages.create_cv_description'))

@push('styles')
<style>
    /* Modern CV Builder Styles */
    .cv-builder-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        position: relative;
        overflow: hidden;
    }

    .cv-builder-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }
    
    .main-content {
        position: relative;
        z-index: 1;
        padding: 2rem 0;
    }
    
    .hero-section {
        text-align: center;
        margin-bottom: 3rem;
        color: white;
    }
    
    .hero-title {
        font-size: 3.5rem;
        font-weight: 800;
        margin-bottom: 1rem;
        background: linear-gradient(45deg, #fff, #e0e7ff);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-shadow: 0 4px 8px rgba(0,0,0,0.3);
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
        opacity: 0.9;
        margin-bottom: 2rem;
    }
    
    .ai-quick-create {
        background: linear-gradient(135deg, #1e293b, #334155);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(148, 163, 184, 0.3);
        border-radius: 25px;
        padding: 2.5rem;
        margin: 2rem auto;
        max-width: 700px;
        box-shadow: 0 15px 45px rgba(0,0,0,0.4);
    }

    .ai-quick-btn {
        background: linear-gradient(135deg, #f59e0b, #ef4444);
        border: none;
        padding: 1.2rem 2.5rem;
        border-radius: 50px;
        color: #1e293b;
        font-weight: 700;
        font-size: 1.1rem;
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
        cursor: pointer;
    }

    .ai-quick-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.6);
    }
    
    .ai-quick-btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
        transition: left 0.5s;
    }
    
    .ai-quick-btn:hover::before {
        left: 100%;
    }
    
    .builder-card {
        background: linear-gradient(135deg, #1e293b, #334155);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 20px 60px rgba(0,0,0,0.4);
        border: 1px solid rgba(148, 163, 184, 0.3);
        overflow: hidden;
        margin: 2rem auto;
        max-width: 1200px;
    }
    
    .step-navigation {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 3rem 2rem;
        position: relative;
        overflow: hidden;
        border-radius: 20px;
        margin: 2rem;
        box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
    }

    .step-navigation::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 20px;
    }

    .steps-container {
        max-width: 900px;
        margin: 0 auto;
        position: relative;
        z-index: 1;
    }

    .step-progress-wrapper {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 2rem;
        position: relative;
    }

    .step-progress-wrapper::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 10%;
        right: 10%;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        transform: translateY(-50%);
        z-index: 0;
    }

    .step-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        z-index: 1;
    }

    .step-indicator {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 700;
        font-size: 1.2rem;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: rgba(30, 41, 59, 0.9);
        color: #94a3b8;
        border: 3px solid rgba(30, 41, 59, 0.7);
        margin-bottom: 1rem;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    }

    .step-indicator.active {
        background: linear-gradient(135deg, #f59e0b, #ef4444);
        color: #1e293b;
        border-color: transparent;
        box-shadow: 0 12px 30px rgba(245, 158, 11, 0.5);
        transform: scale(1.15);
    }

    .step-indicator.completed {
        background: linear-gradient(135deg, #10b981, #3b82f6);
        color: #1e293b;
        border-color: transparent;
        box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
    }

    .step-indicator.completed::after {
        content: '✓';
        font-size: 1.1rem;
        font-weight: 900;
    }

    .step-label {
        font-size: 0.9rem;
        color: #cbd5e1;
        font-weight: 600;
        text-align: center;
        max-width: 90px;
        line-height: 1.3;
        text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .step-label.active {
        color: #f59e0b;
        font-weight: 700;
        transform: scale(1.05);
    }

    .step-label.completed {
        color: #10b981;
        font-weight: 600;
    }

    /* Navigation Buttons */
    .navigation-buttons {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 2rem;
        background: linear-gradient(135deg, #1e293b, #334155);
        border-top: 1px solid rgba(148, 163, 184, 0.2);
    }

    .nav-btn {
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        font-size: 1rem;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .nav-btn.primary {
        background: linear-gradient(135deg, #f59e0b, #ef4444);
        color: #1e293b;
        box-shadow: 0 4px 15px rgba(245, 158, 11, 0.4);
        font-weight: 700;
    }

    .nav-btn.primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(245, 158, 11, 0.6);
    }

    .nav-btn.secondary {
        background: linear-gradient(135deg, #6366f1, #8b5cf6);
        color: #f1f5f9;
        box-shadow: 0 4px 15px rgba(99, 102, 241, 0.4);
    }

    .nav-btn.secondary:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.6);
    }

    /* Responsive improvements */
    @media (max-width: 768px) {
        .step-indicator {
            width: 40px;
            height: 40px;
            font-size: 0.9rem;
        }

        .step-label {
            font-size: 0.75rem;
            max-width: 60px;
        }

        .step-progress-wrapper {
            gap: 0.5rem;
        }

        .step-navigation {
            padding: 1.5rem 1rem;
        }

        .navigation-buttons {
            padding: 1rem;
            flex-direction: column;
            gap: 1rem;
        }

        .nav-btn {
            width: 100%;
            padding: 0.8rem 1.5rem;
        }
    }

    /* Form Styles */
    .form-label {
        display: block;
        font-weight: 600;
        color: #f1f5f9;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #475569;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: #334155;
        color: #f1f5f9;
    }

    .form-input:focus {
        outline: none;
        border-color: #f59e0b;
        box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.2);
        transform: translateY(-1px);
    }

    .form-input:hover {
        border-color: #64748b;
    }

    .form-input::placeholder {
        color: #94a3b8;
    }

    textarea.form-input {
        resize: vertical;
        min-height: 100px;
    }

    select.form-input {
        cursor: pointer;
    }

    /* Form Sections */
    .form-section {
        background: linear-gradient(135deg, #334155, #475569);
        border-radius: 20px;
        padding: 2rem;
        margin-bottom: 2rem;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(148, 163, 184, 0.2);
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #f1f5f9;
        margin-bottom: 1.5rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .section-title svg {
        color: #f59e0b;
    }
    
    .form-content {
        padding: 3rem;
    }
    
    .form-section {
        display: none;
        animation: slideInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .form-section.active {
        display: block;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .section-header {
        text-align: center;
        margin-bottom: 3rem;
    }
    
    .section-title {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        margin-bottom: 1rem;
    }
    
    .section-subtitle {
        color: #6b7280;
        font-size: 1.1rem;
        margin-bottom: 2rem;
    }
    
    .ai-assistant-bar {
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 16px;
        padding: 1.5rem;
        margin-bottom: 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: white;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .ai-assistant-info {
        display: flex;
        align-items: center;
    }
    
    .ai-avatar {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: linear-gradient(135deg, #ff6b6b, #feca57);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        animation: float 3s ease-in-out infinite;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-10px); }
    }
    
    .ai-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 0.75rem 1.5rem;
        border-radius: 50px;
        font-weight: 600;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        cursor: pointer;
    }
    
    .ai-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
    }
    
    /* Template Styles */
    .template-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 2rem;
        margin-top: 2rem;
    }

    .template-card {
        background: white;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        cursor: pointer;
        position: relative;
        border: 3px solid transparent;
    }

    .template-card:hover {
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 50px rgba(0,0,0,0.15);
    }

    .template-card.selected {
        border-color: #667eea;
        transform: translateY(-10px) scale(1.02);
        box-shadow: 0 20px 50px rgba(102, 126, 234, 0.3);
    }

    .template-card.selected::after {
        content: '✓';
        position: absolute;
        top: 1rem;
        left: 1rem;
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        font-weight: bold;
        z-index: 10;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .template-preview {
        height: 450px;
        background: #f8fafc;
        position: relative;
        overflow: hidden;
    }

    .template-info {
        padding: 1.5rem;
        text-align: center;
        background: linear-gradient(135deg, #f8fafc, #ffffff);
    }

    .template-name {
        font-size: 1.25rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 0.5rem;
    }

    .template-description {
        color: #6b7280;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        line-height: 1.5;
    }

    .template-features {
        display: flex;
        justify-content: center;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }

    .template-feature {
        background: rgba(102, 126, 234, 0.1);
        color: #667eea;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.8rem;
        font-weight: 500;
    }

    .ai-recommendation-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: linear-gradient(135deg, #56ab2f, #a8e6cf);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.8rem;
        font-weight: 600;
        animation: pulse 2s infinite;
        box-shadow: 0 4px 15px rgba(86, 171, 47, 0.4);
        z-index: 5;
    }

    @keyframes pulse {
        0%, 100% {
            transform: scale(1);
            opacity: 1;
        }
        50% {
            transform: scale(1.05);
            opacity: 0.9;
        }
    }

    /* Responsive Design */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.5rem;
        }

        .form-content {
            padding: 2rem;
        }

        .template-grid {
            grid-template-columns: 1fr;
        }

        .step-indicator {
            width: 50px;
            height: 50px;
            font-size: 1rem;
        }

        .step-line {
            width: 60px;
        }
    }
</style>
@endpush

@section('content')
<div class="cv-builder-container">
    <div class="main-content">
        <div class="container mx-auto px-4">
            <!-- Hero Section -->
            <div class="hero-section">
                <h1 class="hero-title">{{ __('messages.create_professional_cv') }}</h1>
                <p class="hero-subtitle">{{ __('messages.ai_powered_cv_creation') }}</p>
                
                <!-- AI Quick Create -->
                <div class="ai-quick-create">
                    <div class="flex items-center justify-center mb-4">
                        <div class="ai-avatar">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-white mr-3">{{ __('messages.ai_quick_create') }}</h3>
                    </div>
                    <p class="text-white/90 mb-4">{{ __('messages.let_ai_create_cv_seconds') }}</p>
                    <button type="button" id="aiQuickGenerate" class="ai-quick-btn">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                        </svg>
                        {{ __('messages.create_with_ai') }}
                    </button>
                </div>
            </div>

            <!-- Main Builder Card -->
            <div class="builder-card">
                <!-- Step Navigation -->
                <div class="step-navigation">
                    <div class="steps-container">
                        <div class="step-progress-wrapper">
                            <!-- Step 1 -->
                            <div class="step-item">
                                <div class="step-indicator active" data-step="1">1</div>
                                <div class="step-label active" data-step="1">{{ __('messages.choose_template') }}</div>
                            </div>

                            <div class="step-line" data-step="1"></div>

                            <!-- Step 2 -->
                            <div class="step-item">
                                <div class="step-indicator" data-step="2">2</div>
                                <div class="step-label" data-step="2">{{ __('messages.personal_info') }}</div>
                            </div>

                            <div class="step-line" data-step="2"></div>

                            <!-- Step 3 -->
                            <div class="step-item">
                                <div class="step-indicator" data-step="3">3</div>
                                <div class="step-label" data-step="3">{{ __('messages.experience') }}</div>
                            </div>

                            <div class="step-line" data-step="3"></div>

                            <!-- Step 4 -->
                            <div class="step-item">
                                <div class="step-indicator" data-step="4">4</div>
                                <div class="step-label" data-step="4">{{ __('messages.skills') }}</div>
                            </div>

                            <div class="step-line" data-step="4"></div>

                            <!-- Step 5 -->
                            <div class="step-item">
                                <div class="step-indicator" data-step="5">5</div>
                                <div class="step-label" data-step="5">{{ __('messages.review') }}</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Content -->
                <div class="form-content">
                    <form id="cvBuilderForm" action="{{ route('cv.store') }}" method="POST">
                        @csrf
                        
                        <!-- Step 1: Template Selection -->
                        <div class="form-section active" data-step="1">
                            <div class="section-header">
                                <h2 class="section-title">{{ __('messages.choose_template') }}</h2>
                                <p class="section-subtitle">{{ __('messages.select_perfect_template') }}</p>
                            </div>

                            <!-- AI Assistant Bar -->
                            <div class="ai-assistant-bar">
                                <div class="ai-assistant-info">
                                    <div class="ai-avatar">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold">{{ __('messages.ai_template_assistant') }}</h4>
                                        <p class="text-sm opacity-90">{{ __('messages.ai_will_recommend_best_template') }}</p>
                                    </div>
                                </div>
                                <button type="button" onclick="getTemplateRecommendations()" class="ai-btn">
                                    {{ __('messages.get_ai_recommendations') }}
                                </button>
                            </div>

                            <!-- Template Grid -->
                            <div id="templateGrid" class="template-grid">
                                <!-- Templates will be loaded dynamically -->
                            </div>
                        </div>

                        <!-- Step 2: Personal Information -->
                        <div class="form-section" data-step="2">
                            <div class="section-header">
                                <h2 class="section-title">{{ __('messages.personal_information') }}</h2>
                                <p class="section-subtitle">{{ __('messages.enter_basic_info') }}</p>
                            </div>

                            <!-- AI Assistant Bar -->
                            <div class="ai-assistant-bar">
                                <div class="ai-assistant-info">
                                    <div class="ai-avatar">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold">{{ __('messages.ai_profile_optimizer') }}</h4>
                                        <p class="text-sm opacity-90">{{ __('messages.ai_will_optimize_profile') }}</p>
                                    </div>
                                </div>
                                <button type="button" onclick="optimizePersonalInfo()" class="ai-btn">
                                    {{ __('messages.optimize_with_ai') }}
                                </button>
                            </div>

                            <!-- Personal Info Form -->
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div>
                                    <label class="form-label">{{ __('messages.full_name') }} <span class="text-red-500">*</span></label>
                                    <input type="text" name="personal_info[full_name]" class="form-input" required>
                                </div>
                                <div>
                                    <label class="form-label">{{ __('messages.email') }} <span class="text-red-500">*</span></label>
                                    <input type="email" name="personal_info[email]" class="form-input" required>
                                </div>
                                <div>
                                    <label class="form-label">{{ __('messages.phone') }} <span class="text-red-500">*</span></label>
                                    <input type="tel" name="personal_info[phone]" class="form-input" required>
                                </div>
                                <div>
                                    <label class="form-label">{{ __('messages.address') }}</label>
                                    <input type="text" name="personal_info[address]" class="form-input">
                                </div>
                                <div>
                                    <label class="form-label">{{ __('messages.job_title') }}</label>
                                    <input type="text" name="personal_info[job_title]" class="form-input">
                                </div>
                                <div>
                                    <label class="form-label">{{ __('messages.linkedin') }}</label>
                                    <input type="url" name="personal_info[linkedin]" class="form-input">
                                </div>
                            </div>

                            <!-- Professional Summary -->
                            <div class="mt-8">
                                <label class="form-label">{{ __('messages.professional_summary') }}</label>
                                <textarea name="summary" rows="4" class="form-input" placeholder="{{ __('messages.brief_about_yourself') }}"></textarea>
                                <p class="text-sm text-gray-500 mt-2">{{ __('messages.ai_will_help_summary') }}</p>
                            </div>
                        </div>

                        <!-- Step 3: Experience & Education -->
                        <div class="form-section" data-step="3">
                            <div class="section-header">
                                <h2 class="section-title">{{ __('messages.experience_education') }}</h2>
                                <p class="section-subtitle">{{ __('messages.add_experience_education') }}</p>
                            </div>

                            <!-- AI Assistant Bar -->
                            <div class="ai-assistant-bar">
                                <div class="ai-assistant-info">
                                    <div class="ai-avatar">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold">{{ __('messages.ai_experience_enhancer') }}</h4>
                                        <p class="text-sm opacity-90">{{ __('messages.ai_will_enhance_experience') }}</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button type="button" onclick="enhanceExperience()" class="ai-btn">
                                        {{ __('messages.enhance_experience') }}
                                    </button>
                                    <button type="button" onclick="enhanceEducation()" class="ai-btn">
                                        {{ __('messages.enhance_education') }}
                                    </button>
                                </div>
                            </div>

                            <!-- Experience Section -->
                            <div class="mb-8">
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-xl font-semibold text-gray-800">{{ __('messages.work_experiences') }}</h3>
                                    <button type="button" id="addExperience" class="ai-btn" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                                        {{ __('messages.add_experience') }}
                                    </button>
                                </div>
                                <div id="experienceContainer"></div>
                            </div>

                            <!-- Education Section -->
                            <div>
                                <div class="flex items-center justify-between mb-4">
                                    <h3 class="text-xl font-semibold text-gray-800">{{ __('messages.education') }}</h3>
                                    <button type="button" id="addEducation" class="ai-btn" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                                        {{ __('messages.add_education') }}
                                    </button>
                                </div>
                                <div id="educationContainer"></div>
                            </div>
                        </div>

                        <!-- Step 4: Skills & Languages -->
                        <div class="form-section" data-step="4">
                            <div class="section-header">
                                <h2 class="section-title">{{ __('messages.skills_languages') }}</h2>
                                <p class="section-subtitle">{{ __('messages.add_skills_languages') }}</p>
                            </div>

                            <!-- AI Assistant Bar -->
                            <div class="ai-assistant-bar">
                                <div class="ai-assistant-info">
                                    <div class="ai-avatar">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold">{{ __('messages.ai_skills_analyzer') }}</h4>
                                        <p class="text-sm opacity-90">{{ __('messages.ai_will_suggest_skills') }}</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button type="button" onclick="suggestSkills()" class="ai-btn">
                                        {{ __('messages.suggest_skills') }}
                                    </button>
                                    <button type="button" onclick="suggestLanguages()" class="ai-btn">
                                        {{ __('messages.suggest_languages') }}
                                    </button>
                                </div>
                            </div>

                            <!-- Skills & Languages Grid -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Skills -->
                                <div>
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-xl font-semibold text-gray-800">{{ __('messages.skills') }}</h3>
                                        <button type="button" id="addSkill" class="ai-btn" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                                            {{ __('messages.add_skill') }}
                                        </button>
                                    </div>
                                    <div id="skillsContainer"></div>
                                </div>

                                <!-- Languages -->
                                <div>
                                    <div class="flex items-center justify-between mb-4">
                                        <h3 class="text-xl font-semibold text-gray-800">{{ __('messages.languages') }}</h3>
                                        <button type="button" id="addLanguage" class="ai-btn" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                                            {{ __('messages.add_language') }}
                                        </button>
                                    </div>
                                    <div id="languagesContainer"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 5: Review & Generate -->
                        <div class="form-section" data-step="5">
                            <div class="section-header">
                                <h2 class="section-title">{{ __('messages.review_generate') }}</h2>
                                <p class="section-subtitle">{{ __('messages.review_information_generate') }}</p>
                            </div>

                            <!-- AI Final Review -->
                            <div class="ai-assistant-bar">
                                <div class="ai-assistant-info">
                                    <div class="ai-avatar">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold">{{ __('messages.ai_final_reviewer') }}</h4>
                                        <p class="text-sm opacity-90">{{ __('messages.ai_will_review_optimize') }}</p>
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <button type="button" onclick="performFinalReview()" class="ai-btn">
                                        {{ __('messages.ai_review') }}
                                    </button>
                                    <button type="button" onclick="optimizeCV()" class="ai-btn">
                                        {{ __('messages.ai_optimize') }}
                                    </button>
                                </div>
                            </div>

                            <!-- CV Preview -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Preview Panel -->
                                <div class="bg-white rounded-2xl p-6 shadow-lg border">
                                    <h3 class="text-xl font-semibold text-gray-800 mb-4">{{ __('messages.cv_preview') }}</h3>
                                    <div id="cvPreview" class="bg-gray-50 rounded-lg p-4 min-h-[600px]">
                                        <div class="text-center text-gray-500 py-20">
                                            <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                            <p>{{ __('messages.cv_preview_will_appear_here') }}</p>
                                        </div>
                                    </div>
                                    <button type="button" id="generatePreviewBtn" class="w-full mt-4 nav-btn primary">
                                        {{ __('messages.generate_preview') }}
                                    </button>
                                </div>

                                <!-- Summary Panel -->
                                <div class="space-y-6">
                                    <!-- Personal Info Summary -->
                                    <div class="bg-white rounded-2xl p-6 shadow-lg border">
                                        <h4 class="text-lg font-semibold text-gray-800 mb-3">{{ __('messages.personal_information') }}</h4>
                                        <div id="personalSummary" class="text-gray-600 text-sm space-y-1">
                                            <p class="text-gray-400">{{ __('messages.no_information_entered') }}</p>
                                        </div>
                                    </div>

                                    <!-- Experience Summary -->
                                    <div class="bg-white rounded-2xl p-6 shadow-lg border">
                                        <h4 class="text-lg font-semibold text-gray-800 mb-3">{{ __('messages.experience_summary') }}</h4>
                                        <div id="experienceSummary" class="text-gray-600 text-sm space-y-1">
                                            <p class="text-gray-400">{{ __('messages.no_experience_added') }}</p>
                                        </div>
                                    </div>

                                    <!-- Skills Summary -->
                                    <div class="bg-white rounded-2xl p-6 shadow-lg border">
                                        <h4 class="text-lg font-semibold text-gray-800 mb-3">{{ __('messages.skills_summary') }}</h4>
                                        <div id="skillsSummary" class="text-gray-600 text-sm">
                                            <p class="text-gray-400">{{ __('messages.no_skills_added') }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>

                <!-- Navigation Buttons -->
                <div class="navigation-buttons">
                    <button type="button" id="prevBtn" class="nav-btn secondary" style="display: none;">
                        {{ __('messages.previous') }}
                    </button>
                    <div class="flex space-x-4">
                        <button type="button" id="nextBtn" class="nav-btn primary">
                            {{ __('messages.next') }}
                        </button>
                        <button type="button" id="submitBtn" class="nav-btn primary" style="display: none;" onclick="submitCV()">
                            {{ __('messages.create_cv') }}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 5;

    // Initialize
    loadTemplates();
    showStep(currentStep);

    // Add event listener for AI Quick Generate button
    document.getElementById('aiQuickGenerate')?.addEventListener('click', aiQuickGenerate);

    // Add event listener for preview generation
    document.getElementById('generatePreviewBtn')?.addEventListener('click', generatePreview);

    // Add real-time validation
    document.addEventListener('input', function(e) {
        if (e.target.matches('input, textarea, select')) {
            // Reset border color on input
            e.target.style.borderColor = '#475569';

            // Auto-save after input
            autoSave();

            // Update summaries
            updateSummaries();
        }
    });

    // Add form submission validation
    document.getElementById('nextBtn')?.addEventListener('click', function() {
        if (validateStep(currentStep)) {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
                updateSummaries();
            }
        }
    });

    // Navigation
    document.getElementById('nextBtn').addEventListener('click', function() {
        if (validateCurrentStep()) {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);
                updateSummaries();
            }
        }
    });

    document.getElementById('prevBtn').addEventListener('click', function() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });

    // Show/Hide steps
    function showStep(step) {
        // Hide all sections
        document.querySelectorAll('.form-section').forEach(section => {
            section.classList.remove('active');
        });

        // Show current section
        const currentSection = document.querySelector(`[data-step="${step}"]`);
        if (currentSection) {
            currentSection.classList.add('active');
        }

        // Update step indicators
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const stepNumber = index + 1;
            indicator.classList.remove('active', 'completed');

            if (stepNumber < step) {
                indicator.classList.add('completed');
            } else if (stepNumber === step) {
                indicator.classList.add('active');
            }
        });

        // Update step labels
        document.querySelectorAll('.step-label').forEach((label, index) => {
            const stepNumber = index + 1;
            label.classList.remove('active', 'completed');

            if (stepNumber < step) {
                label.classList.add('completed');
            } else if (stepNumber === step) {
                label.classList.add('active');
            }
        });

        // Update step lines
        document.querySelectorAll('.step-line').forEach((line, index) => {
            const stepNumber = index + 1;
            line.classList.remove('completed');

            if (stepNumber < step) {
                line.classList.add('completed');
            }
        });

        // Update navigation buttons
        document.getElementById('prevBtn').style.display = step > 1 ? 'block' : 'none';
        document.getElementById('nextBtn').style.display = step < totalSteps ? 'block' : 'none';
        document.getElementById('submitBtn').style.display = step === totalSteps ? 'block' : 'none';
    }

    // Validate current step
    function validateCurrentStep() {
        const currentSection = document.querySelector(`[data-step="${currentStep}"]`);
        const requiredFields = currentSection.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.style.borderColor = '#ef4444';
                isValid = false;
            } else {
                field.style.borderColor = '#e5e7eb';
            }
        });

        if (currentStep === 1) {
            const selectedTemplate = document.querySelector('input[name="template"]:checked');
            if (!selectedTemplate) {
                showNotification('{{ __("messages.please_choose_template") }}', 'error');
                isValid = false;
            }
        }

        return isValid;
    }

    // Load templates
    function loadTemplates() {
        const templateGrid = document.getElementById('templateGrid');
        templateGrid.innerHTML = `
            <div class="template-card" data-template="modern">
                <div class="template-preview">
                    <div style="background: linear-gradient(135deg, #667eea, #764ba2); height: 100%; padding: 2rem; color: white; position: relative;">
                        <div class="ai-recommendation-badge">{{ __('messages.ai_recommended') }}</div>
                        <div style="border-left: 4px solid white; padding-left: 1rem; margin-bottom: 2rem;">
                            <h3 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">أحمد محمد</h3>
                            <p style="opacity: 0.9;">مطور برمجيات محترف</p>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 8px; margin-bottom: 1rem;">
                            <h4 style="font-weight: bold; margin-bottom: 0.5rem;">الخبرة المهنية</h4>
                            <p style="font-size: 0.9rem; opacity: 0.9;">مطور أول - شركة التقنية المتقدمة</p>
                            <p style="font-size: 0.8rem; opacity: 0.7;">2020 - الآن</p>
                        </div>
                        <div style="display: flex; flex-wrap: wrap; gap: 0.5rem;">
                            <span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem;">React</span>
                            <span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem;">Node.js</span>
                            <span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem;">Python</span>
                        </div>
                    </div>
                </div>
                <div class="template-info">
                    <h3 class="template-name">{{ __('messages.modern_template') }}</h3>
                    <p class="template-description">{{ __('messages.modern_template_desc') }}</p>
                    <div class="template-features">
                        <span class="template-feature">{{ __('messages.gradient_design') }}</span>
                        <span class="template-feature">{{ __('messages.tech_friendly') }}</span>
                        <span class="template-feature">{{ __('messages.modern_layout') }}</span>
                    </div>
                    <input type="radio" name="template" value="modern" style="display: none;">
                </div>
            </div>

            <div class="template-card" data-template="professional">
                <div class="template-preview">
                    <div style="background: linear-gradient(135deg, #2d3748, #4a5568); height: 100%; padding: 2rem; color: white;">
                        <div style="text-align: center; margin-bottom: 2rem; padding-bottom: 1rem; border-bottom: 2px solid rgba(255,255,255,0.2);">
                            <h3 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">سارة أحمد</h3>
                            <p style="opacity: 0.9;">مديرة مشاريع</p>
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="font-weight: bold; margin-bottom: 0.5rem; color: #90cdf4;">المهارات</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 0.5rem; font-size: 0.9rem;">
                                <div>إدارة المشاريع</div>
                                <div>القيادة</div>
                                <div>التخطيط الاستراتيجي</div>
                                <div>التواصل</div>
                            </div>
                        </div>
                        <div>
                            <h4 style="font-weight: bold; margin-bottom: 0.5rem; color: #90cdf4;">التعليم</h4>
                            <p style="font-size: 0.9rem; opacity: 0.9;">ماجستير إدارة أعمال</p>
                            <p style="font-size: 0.8rem; opacity: 0.7;">جامعة الملك سعود - 2018</p>
                        </div>
                    </div>
                </div>
                <div class="template-info">
                    <h3 class="template-name">{{ __('messages.professional_template') }}</h3>
                    <p class="template-description">{{ __('messages.professional_template_desc') }}</p>
                    <div class="template-features">
                        <span class="template-feature">{{ __('messages.classic_design') }}</span>
                        <span class="template-feature">{{ __('messages.business_ready') }}</span>
                        <span class="template-feature">{{ __('messages.clean_layout') }}</span>
                    </div>
                    <input type="radio" name="template" value="professional" style="display: none;">
                </div>
            </div>

            <div class="template-card" data-template="creative">
                <div class="template-preview">
                    <div style="background: linear-gradient(135deg, #ed64a6, #f093fb); height: 100%; padding: 2rem; color: white;">
                        <div style="background: rgba(255,255,255,0.1); border-radius: 50%; width: 80px; height: 80px; margin: 0 auto 1rem; display: flex; align-items: center; justify-content: center;">
                            <span style="font-size: 2rem;">👩‍🎨</span>
                        </div>
                        <div style="text-align: center; margin-bottom: 2rem;">
                            <h3 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">نور الدين</h3>
                            <p style="opacity: 0.9;">مصمم جرافيك</p>
                        </div>
                        <div style="background: rgba(255,255,255,0.1); padding: 1rem; border-radius: 12px; margin-bottom: 1rem;">
                            <h4 style="font-weight: bold; margin-bottom: 0.5rem;">المشاريع المميزة</h4>
                            <ul style="font-size: 0.9rem; opacity: 0.9; list-style: none; padding: 0;">
                                <li style="margin-bottom: 0.25rem;">• تصميم هوية بصرية لشركة ناشئة</li>
                                <li style="margin-bottom: 0.25rem;">• حملة إعلانية رقمية</li>
                                <li>• تطبيق موبايل UI/UX</li>
                            </ul>
                        </div>
                        <div style="text-align: center;">
                            <div style="display: inline-flex; gap: 0.5rem;">
                                <span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem;">Photoshop</span>
                                <span style="background: rgba(255,255,255,0.2); padding: 0.25rem 0.75rem; border-radius: 20px; font-size: 0.8rem;">Illustrator</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="template-info">
                    <h3 class="template-name">{{ __('messages.creative_template') }}</h3>
                    <p class="template-description">{{ __('messages.creative_template_desc') }}</p>
                    <div class="template-features">
                        <span class="template-feature">{{ __('messages.artistic_design') }}</span>
                        <span class="template-feature">{{ __('messages.colorful_layout') }}</span>
                        <span class="template-feature">{{ __('messages.creative_friendly') }}</span>
                    </div>
                    <input type="radio" name="template" value="creative" style="display: none;">
                </div>
            </div>

            <div class="template-card" data-template="minimal">
                <div class="template-preview">
                    <div style="background: #ffffff; height: 100%; padding: 2rem; color: #1f2937; border: 1px solid #e5e7eb;">
                        <div style="border-bottom: 3px solid #3b82f6; padding-bottom: 1rem; margin-bottom: 2rem;">
                            <h3 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem; color: #1f2937;">محمد العلي</h3>
                            <p style="color: #6b7280;">مهندس برمجيات</p>
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <h4 style="font-weight: bold; margin-bottom: 0.5rem; color: #3b82f6;">معلومات الاتصال</h4>
                            <div style="font-size: 0.9rem; color: #6b7280; line-height: 1.5;">
                                <div>📧 <EMAIL></div>
                                <div>📱 +966 50 123 4567</div>
                                <div>📍 الرياض، السعودية</div>
                            </div>
                        </div>
                        <div>
                            <h4 style="font-weight: bold; margin-bottom: 0.5rem; color: #3b82f6;">الملخص المهني</h4>
                            <p style="font-size: 0.9rem; color: #6b7280; line-height: 1.4;">
                                مهندس برمجيات متخصص في تطوير التطبيقات الويب مع خبرة 5 سنوات في العمل مع فرق متعددة الثقافات.
                            </p>
                        </div>
                    </div>
                </div>
                <div class="template-info">
                    <h3 class="template-name">{{ __('messages.minimal_template') }}</h3>
                    <p class="template-description">{{ __('messages.minimal_template_desc') }}</p>
                    <div class="template-features">
                        <span class="template-feature">{{ __('messages.simple_design') }}</span>
                        <span class="template-feature">{{ __('messages.content_focused') }}</span>
                        <span class="template-feature">{{ __('messages.elegant_style') }}</span>
                    </div>
                    <input type="radio" name="template" value="minimal" style="display: none;">
                </div>
            </div>
        `;

        // Add click handlers for templates
        document.querySelectorAll('.template-card').forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                document.querySelectorAll('.template-card').forEach(c => c.classList.remove('selected'));

                // Add selected class to clicked card
                this.classList.add('selected');

                // Check the radio button
                const radio = this.querySelector('input[type="radio"]');
                if (radio) {
                    radio.checked = true;
                }

                // Show success message
                const templateName = this.querySelector('.template-name').textContent;
                showNotification(`{{ __('messages.template_selected') }}: ${templateName}`, 'success');
            });
        });

        // Add dynamic form builders
        addDynamicFormBuilders();
    }

    // Update summaries
    function updateSummaries() {
        if (currentStep >= 2) {
            updatePersonalSummary();
        }
        if (currentStep >= 3) {
            updateExperienceSummary();
        }
        if (currentStep >= 4) {
            updateSkillsSummary();
        }
    }

    function updatePersonalSummary() {
        const fullName = document.querySelector('input[name="personal_info[full_name]"]')?.value || '';
        const email = document.querySelector('input[name="personal_info[email]"]')?.value || '';
        const phone = document.querySelector('input[name="personal_info[phone]"]')?.value || '';
        const jobTitle = document.querySelector('input[name="personal_info[job_title]"]')?.value || '';

        const summaryDiv = document.getElementById('personalSummary');
        if (fullName || email || phone || jobTitle) {
            summaryDiv.innerHTML = `
                ${fullName ? `<p><strong>{{ __('messages.name') }}:</strong> ${fullName}</p>` : ''}
                ${jobTitle ? `<p><strong>{{ __('messages.job_title') }}:</strong> ${jobTitle}</p>` : ''}
                ${email ? `<p><strong>{{ __('messages.email') }}:</strong> ${email}</p>` : ''}
                ${phone ? `<p><strong>{{ __('messages.phone') }}:</strong> ${phone}</p>` : ''}
            `;
        }
    }

    function updateExperienceSummary() {
        // Implementation for experience summary
        const summaryDiv = document.getElementById('experienceSummary');
        summaryDiv.innerHTML = '<p class="text-gray-400">{{ __("messages.experience_will_appear_here") }}</p>';
    }

    function updateSkillsSummary() {
        // Implementation for skills summary
        const summaryDiv = document.getElementById('skillsSummary');
        summaryDiv.innerHTML = '<p class="text-gray-400">{{ __("messages.skills_will_appear_here") }}</p>';
    }

    // Show notification
    function showNotification(message, type = 'info') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'success' ? 'bg-green-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
});

// AI Functions
function getTemplateRecommendations() {
    showNotification('{{ __("messages.ai_analyzing_preferences") }}', 'info');

    // Get user's job title or field for better recommendations
    const jobTitle = document.querySelector('input[name="personal_info[job_title]"]')?.value || '';

    fetch('{{ route("cv.template.recommendations") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            job_title: jobTitle,
            preferences: gatherUserPreferences()
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.recommended_template) {
            const recommendedTemplate = document.querySelector(`[data-template="${data.recommended_template}"]`);
            if (recommendedTemplate) {
                recommendedTemplate.click();
                showNotification(data.message || '{{ __("messages.ai_template_recommended") }}', 'success');
            }
        } else {
            // Fallback to simple recommendation
            const modernTemplate = document.querySelector('[data-template="modern"]');
            if (modernTemplate) {
                modernTemplate.click();
                showNotification('{{ __("messages.ai_recommends_modern_template") }}', 'success');
            }
        }
    })
    .catch(error => {
        console.error('AI recommendation error:', error);
        showNotification('{{ __("messages.ai_recommendation_error") }}', 'error');
    });
}

// AI Quick Generate Function
function aiQuickGenerate() {
    // Show modal for user input
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-slate-800 rounded-2xl p-8 max-w-md w-full mx-4 border border-slate-600">
            <h3 class="text-2xl font-bold text-white mb-6">{{ __('messages.ai_quick_create') }}</h3>
            <div class="space-y-4">
                <div>
                    <label class="form-label">{{ __('messages.job_title') }}</label>
                    <input type="text" id="aiJobTitle" class="form-input" placeholder="{{ __('messages.enter_job_title') }}">
                </div>
                <div>
                    <label class="form-label">{{ __('messages.experience_level') }}</label>
                    <select id="aiExperienceLevel" class="form-input">
                        <option value="entry">{{ __('messages.entry_level') }}</option>
                        <option value="mid" selected>{{ __('messages.mid_level') }}</option>
                        <option value="senior">{{ __('messages.senior_level') }}</option>
                        <option value="expert">{{ __('messages.expert_level') }}</option>
                    </select>
                </div>
                <div>
                    <label class="form-label">{{ __('messages.industry') }}</label>
                    <input type="text" id="aiIndustry" class="form-input" placeholder="{{ __('messages.enter_industry') }}">
                </div>
            </div>
            <div class="flex space-x-4 mt-8">
                <button onclick="closeAIModal()" class="nav-btn secondary flex-1">{{ __('messages.cancel') }}</button>
                <button onclick="generateAICV()" class="nav-btn primary flex-1">{{ __('messages.generate') }}</button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
    document.getElementById('aiJobTitle').focus();
}

function closeAIModal() {
    const modal = document.querySelector('.fixed.inset-0');
    if (modal) {
        modal.remove();
    }
}

function generateAICV() {
    const jobTitle = document.getElementById('aiJobTitle').value.trim();
    const experienceLevel = document.getElementById('aiExperienceLevel').value;
    const industry = document.getElementById('aiIndustry').value.trim();

    if (!jobTitle) {
        showNotification('{{ __("messages.please_enter_job_title") }}', 'error');
        return;
    }

    closeAIModal();
    showNotification('{{ __("messages.ai_generating_cv") }}', 'info');

    // Show loading overlay
    const loadingOverlay = document.createElement('div');
    loadingOverlay.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50';
    loadingOverlay.innerHTML = `
        <div class="text-center text-white">
            <div class="animate-spin rounded-full h-16 w-16 border-b-2 border-amber-500 mx-auto mb-4"></div>
            <h3 class="text-xl font-bold mb-2">{{ __('messages.ai_creating_cv') }}</h3>
            <p class="text-gray-300">{{ __('messages.please_wait_ai_working') }}</p>
        </div>
    `;
    document.body.appendChild(loadingOverlay);

    fetch('{{ route("cv.ai.quick.generate") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            job_title: jobTitle,
            experience_level: experienceLevel,
            industry: industry
        })
    })
    .then(response => response.json())
    .then(data => {
        loadingOverlay.remove();

        if (data.success) {
            showNotification(data.message, 'success');

            // Populate form with AI-generated data
            if (data.cv_data) {
                populateFormWithAIData(data.cv_data);
            }

            // Redirect to edit the generated CV
            if (data.redirect_url) {
                setTimeout(() => {
                    window.location.href = data.redirect_url;
                }, 2000);
            }
        } else {
            showNotification(data.message || '{{ __("messages.ai_generation_failed") }}', 'error');
        }
    })
    .catch(error => {
        loadingOverlay.remove();
        console.error('AI generation error:', error);
        showNotification('{{ __("messages.ai_generation_failed") }}', 'error');
    });
}

function populateFormWithAIData(cvData) {
    // Populate template
    if (cvData.template) {
        const templateRadio = document.querySelector(`input[name="template"][value="${cvData.template}"]`);
        if (templateRadio) {
            templateRadio.checked = true;
            templateRadio.closest('.template-card').classList.add('selected');
        }
    }

    // Populate personal info
    if (cvData.personal_info) {
        Object.keys(cvData.personal_info).forEach(key => {
            const input = document.querySelector(`input[name="personal_info[${key}]"]`);
            if (input) {
                input.value = cvData.personal_info[key];
            }
        });
    }

    // Populate summary
    if (cvData.summary) {
        const summaryTextarea = document.querySelector('textarea[name="summary"]');
        if (summaryTextarea) {
            summaryTextarea.value = cvData.summary;
        }
    }

    // Populate experiences
    if (cvData.experience && cvData.experience.length > 0) {
        cvData.experience.forEach((exp, index) => {
            if (index > 0) addExperienceField();
            Object.keys(exp).forEach(key => {
                const input = document.querySelector(`input[name="experience[${index}][${key}]"], textarea[name="experience[${index}][${key}]"]`);
                if (input) {
                    input.value = exp[key];
                }
            });
        });
    }

    // Populate education
    if (cvData.education && cvData.education.length > 0) {
        cvData.education.forEach((edu, index) => {
            if (index > 0) addEducationField();
            Object.keys(edu).forEach(key => {
                const input = document.querySelector(`input[name="education[${index}][${key}]"]`);
                if (input) {
                    input.value = edu[key];
                }
            });
        });
    }

    // Populate skills
    if (cvData.skills && cvData.skills.length > 0) {
        cvData.skills.forEach((skill, index) => {
            if (index > 0) addSkillField();
            const nameInput = document.querySelector(`input[name="skills[${index}][name]"]`);
            const levelSelect = document.querySelector(`select[name="skills[${index}][level]"]`);
            if (nameInput) nameInput.value = skill.name;
            if (levelSelect) levelSelect.value = skill.level;
        });
    }

    // Populate languages
    if (cvData.languages && cvData.languages.length > 0) {
        cvData.languages.forEach((lang, index) => {
            if (index > 0) addLanguageField();
            const nameInput = document.querySelector(`input[name="languages[${index}][name]"]`);
            const levelSelect = document.querySelector(`select[name="languages[${index}][level]"]`);
            if (nameInput) nameInput.value = lang.name;
            if (levelSelect) levelSelect.value = lang.level;
        });
    }

    updateSummaries();
}

function gatherUserPreferences() {
    return {
        industry: document.querySelector('input[name="personal_info[job_title]"]')?.value || '',
        experience_level: document.getElementById('experienceContainer')?.children.length || 0,
        skills_count: document.getElementById('skillsContainer')?.children.length || 0
    };
}

function optimizePersonalInfo() {
    showNotification('{{ __("messages.ai_optimizing_profile") }}', 'info');

    const personalData = {
        full_name: document.querySelector('input[name="personal_info[full_name]"]')?.value || '',
        job_title: document.querySelector('input[name="personal_info[job_title]"]')?.value || '',
        summary: document.querySelector('textarea[name="summary"]')?.value || ''
    };

    fetch('{{ route("cv.suggestions") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            action: 'optimize_profile',
            data: personalData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.suggestions) {
            // Apply AI suggestions
            if (data.suggestions.job_title) {
                document.querySelector('input[name="personal_info[job_title]"]').value = data.suggestions.job_title;
            }
            if (data.suggestions.summary) {
                document.querySelector('textarea[name="summary"]').value = data.suggestions.summary;
            }
            showNotification('{{ __("messages.profile_optimized_successfully") }}', 'success');
            autoSave(); // Save changes
        } else {
            showNotification('{{ __("messages.ai_optimization_failed") }}', 'error');
        }
    })
    .catch(error => {
        console.error('AI optimization error:', error);
        showNotification('{{ __("messages.ai_optimization_failed") }}', 'error');
    });
}

function enhanceExperience() {
    showNotification('{{ __("messages.ai_enhancing_experience") }}', 'info');
    // AI enhancement logic here
}

function enhanceEducation() {
    showNotification('{{ __("messages.ai_enhancing_education") }}', 'info');
    // AI enhancement logic here
}

function suggestSkills() {
    showNotification('{{ __("messages.ai_suggesting_skills") }}', 'info');

    const jobTitle = document.querySelector('input[name="personal_info[job_title]"]')?.value || '';
    const currentSkills = Array.from(document.querySelectorAll('input[name^="skills"][name$="[name]"]'))
        .map(input => input.value)
        .filter(skill => skill.trim() !== '');

    fetch('{{ route("cv.suggestions") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            message: `اقترح 5 مهارات مهمة لوظيفة ${jobTitle}. المهارات الحالية: ${currentSkills.join(', ')}. أريد مهارات جديدة فقط.`,
            context: 'skill_suggestion'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.response) {
            // Parse AI response and add suggested skills
            const suggestedSkills = parseSkillSuggestions(data.response);
            suggestedSkills.forEach(skill => {
                addSkillField();
                const lastSkillInput = document.querySelector('input[name^="skills"]:last-of-type');
                if (lastSkillInput) {
                    lastSkillInput.value = skill;
                }
            });
            showNotification('{{ __("messages.skills_suggested_successfully") }}', 'success');
            autoSave();
        } else {
            showNotification('{{ __("messages.ai_suggestion_failed") }}', 'error');
        }
    })
    .catch(error => {
        console.error('AI skill suggestion error:', error);
        showNotification('{{ __("messages.ai_suggestion_failed") }}', 'error');
    });
}

function parseSkillSuggestions(aiResponse) {
    // Simple parsing - extract skills from AI response
    const skills = [];
    const lines = aiResponse.split('\n');
    lines.forEach(line => {
        // Look for numbered lists or bullet points
        const match = line.match(/^\d+\.?\s*(.+)$/) || line.match(/^[-•]\s*(.+)$/);
        if (match && match[1]) {
            const skill = match[1].trim().replace(/['"]/g, '');
            if (skill.length > 0 && skill.length < 50) {
                skills.push(skill);
            }
        }
    });
    return skills.slice(0, 5); // Limit to 5 skills
}

function suggestLanguages() {
    showNotification('{{ __("messages.ai_suggesting_languages") }}', 'info');
    // AI languages suggestion logic here
}

function performFinalReview() {
    showNotification('{{ __("messages.ai_reviewing_cv") }}', 'info');
    // AI final review logic here
}

function optimizeCV() {
    showNotification('{{ __("messages.ai_optimizing_cv") }}', 'info');
    // AI CV optimization logic here
}

// Dynamic form builders
function addDynamicFormBuilders() {
    // Add Experience button
    document.getElementById('addExperience')?.addEventListener('click', function() {
        addExperienceField();
    });

    // Add Education button
    document.getElementById('addEducation')?.addEventListener('click', function() {
        addEducationField();
    });

    // Add Skill button
    document.getElementById('addSkill')?.addEventListener('click', function() {
        addSkillField();
    });

    // Add Language button
    document.getElementById('addLanguage')?.addEventListener('click', function() {
        addLanguageField();
    });
}

function addExperienceField() {
    const container = document.getElementById('experienceContainer');
    const index = container.children.length;

    const experienceHTML = `
        <div class="experience-item bg-white rounded-2xl p-6 shadow-lg border mb-4">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-lg font-semibold text-gray-800">{{ __('messages.experience') }} ${index + 1}</h4>
                <button type="button" onclick="removeExperience(this)" class="text-red-500 hover:text-red-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="form-label">{{ __('messages.job_title') }}</label>
                    <input type="text" name="experience[${index}][job_title]" class="form-input">
                </div>
                <div>
                    <label class="form-label">{{ __('messages.company') }}</label>
                    <input type="text" name="experience[${index}][company]" class="form-input">
                </div>
                <div>
                    <label class="form-label">{{ __('messages.start_date') }}</label>
                    <input type="date" name="experience[${index}][start_date]" class="form-input">
                </div>
                <div>
                    <label class="form-label">{{ __('messages.end_date') }}</label>
                    <input type="date" name="experience[${index}][end_date]" class="form-input">
                </div>
            </div>
            <div class="mt-4">
                <label class="form-label">{{ __('messages.description') }}</label>
                <textarea name="experience[${index}][description]" rows="3" class="form-input" placeholder="{{ __('messages.describe_responsibilities') }}"></textarea>
            </div>
            <div class="mt-4 flex justify-end">
                <button type="button" onclick="enhanceExperienceDescription(${index})" class="ai-btn" style="background: linear-gradient(135deg, #667eea, #764ba2); color: white;">
                    {{ __('messages.enhance_with_ai') }}
                </button>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', experienceHTML);
}

function addEducationField() {
    const container = document.getElementById('educationContainer');
    const index = container.children.length;

    const educationHTML = `
        <div class="education-item bg-white rounded-2xl p-6 shadow-lg border mb-4">
            <div class="flex justify-between items-center mb-4">
                <h4 class="text-lg font-semibold text-gray-800">{{ __('messages.education') }} ${index + 1}</h4>
                <button type="button" onclick="removeEducation(this)" class="text-red-500 hover:text-red-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="form-label">{{ __('messages.degree') }}</label>
                    <input type="text" name="education[${index}][degree]" class="form-input">
                </div>
                <div>
                    <label class="form-label">{{ __('messages.institution') }}</label>
                    <input type="text" name="education[${index}][institution]" class="form-input">
                </div>
                <div>
                    <label class="form-label">{{ __('messages.graduation_year') }}</label>
                    <input type="number" name="education[${index}][graduation_year]" class="form-input" min="1950" max="2030">
                </div>
                <div>
                    <label class="form-label">{{ __('messages.gpa') }} ({{ __('messages.optional') }})</label>
                    <input type="text" name="education[${index}][gpa]" class="form-input">
                </div>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', educationHTML);
}

function addSkillField() {
    const container = document.getElementById('skillsContainer');
    const index = container.children.length;

    const skillHTML = `
        <div class="skill-item bg-white rounded-2xl p-4 shadow-lg border mb-3">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <input type="text" name="skills[${index}][name]" class="form-input" placeholder="{{ __('messages.skill_name') }}">
                </div>
                <div class="w-32">
                    <select name="skills[${index}][level]" class="form-input">
                        <option value="beginner">{{ __('messages.beginner') }}</option>
                        <option value="intermediate">{{ __('messages.intermediate') }}</option>
                        <option value="advanced">{{ __('messages.advanced') }}</option>
                        <option value="expert">{{ __('messages.expert') }}</option>
                    </select>
                </div>
                <button type="button" onclick="removeSkill(this)" class="text-red-500 hover:text-red-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', skillHTML);
}

function addLanguageField() {
    const container = document.getElementById('languagesContainer');
    const index = container.children.length;

    const languageHTML = `
        <div class="language-item bg-white rounded-2xl p-4 shadow-lg border mb-3">
            <div class="flex items-center space-x-4">
                <div class="flex-1">
                    <input type="text" name="languages[${index}][name]" class="form-input" placeholder="{{ __('messages.language_name') }}">
                </div>
                <div class="w-32">
                    <select name="languages[${index}][level]" class="form-input">
                        <option value="basic">{{ __('messages.basic') }}</option>
                        <option value="conversational">{{ __('messages.conversational') }}</option>
                        <option value="fluent">{{ __('messages.fluent') }}</option>
                        <option value="native">{{ __('messages.native') }}</option>
                    </select>
                </div>
                <button type="button" onclick="removeLanguage(this)" class="text-red-500 hover:text-red-700">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    `;

    container.insertAdjacentHTML('beforeend', languageHTML);
}

// Remove functions
function removeExperience(button) {
    button.closest('.experience-item').remove();
}

function removeEducation(button) {
    button.closest('.education-item').remove();
}

function removeSkill(button) {
    button.closest('.skill-item').remove();
}

function removeLanguage(button) {
    button.closest('.language-item').remove();
}

function enhanceExperienceDescription(index) {
    showNotification('{{ __("messages.ai_enhancing_description") }}', 'info');

    const jobTitle = document.querySelector(`input[name="experience[${index}][job_title]"]`)?.value || '';
    const company = document.querySelector(`input[name="experience[${index}][company]"]`)?.value || '';
    const currentDescription = document.querySelector(`textarea[name="experience[${index}][description]"]`)?.value || '';

    if (!jobTitle) {
        showNotification('{{ __("messages.please_enter_job_title_first") }}', 'error');
        return;
    }

    fetch('{{ route("cv.suggestions") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            message: `حسن وصف الخبرة العملية التالية:
            المسمى الوظيفي: ${jobTitle}
            الشركة: ${company}
            الوصف الحالي: ${currentDescription}

            اكتب وصفاً مهنياً ومفصلاً يبرز المسؤوليات والإنجازات بشكل جذاب.`,
            context: 'experience_enhancement'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success && data.response) {
            const descriptionTextarea = document.querySelector(`textarea[name="experience[${index}][description]"]`);
            if (descriptionTextarea) {
                descriptionTextarea.value = data.response.trim();
                showNotification('{{ __("messages.description_enhanced_successfully") }}', 'success');
                autoSave();
            }
        } else {
            showNotification('{{ __("messages.ai_enhancement_failed") }}', 'error');
        }
    })
    .catch(error => {
        console.error('AI enhancement error:', error);
        showNotification('{{ __("messages.ai_enhancement_failed") }}', 'error');
    });
}

// Generate CV Preview
function generatePreview() {
    showNotification('{{ __("messages.generating_preview") }}', 'info');

    const formData = new FormData(document.getElementById('cvBuilderForm'));

    fetch('{{ route("cv.preview") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const previewContainer = document.getElementById('cvPreview');
            if (previewContainer) {
                previewContainer.innerHTML = data.preview_html;
                showNotification('{{ __("messages.preview_generated_successfully") }}', 'success');
            }
        } else {
            showNotification(data.message || '{{ __("messages.preview_generation_failed") }}', 'error');
        }
    })
    .catch(error => {
        console.error('Preview generation error:', error);
        showNotification('{{ __("messages.preview_generation_failed") }}', 'error');
    });
}

// Real-time form validation
function validateStep(stepNumber) {
    let isValid = true;
    const errors = [];

    switch(stepNumber) {
        case 1:
            const selectedTemplate = document.querySelector('input[name="template"]:checked');
            if (!selectedTemplate) {
                errors.push('{{ __("messages.please_choose_template") }}');
                isValid = false;
            }
            break;

        case 2:
            const requiredFields = [
                { name: 'personal_info[full_name]', label: '{{ __("messages.full_name") }}' },
                { name: 'personal_info[email]', label: '{{ __("messages.email") }}' },
                { name: 'personal_info[phone]', label: '{{ __("messages.phone") }}' }
            ];

            requiredFields.forEach(field => {
                const input = document.querySelector(`input[name="${field.name}"]`);
                if (!input || !input.value.trim()) {
                    errors.push(`${field.label} مطلوب`);
                    isValid = false;
                    if (input) {
                        input.style.borderColor = '#ef4444';
                    }
                } else if (input) {
                    input.style.borderColor = '#10b981';
                }
            });

            // Email validation
            const emailInput = document.querySelector('input[name="personal_info[email]"]');
            if (emailInput && emailInput.value) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(emailInput.value)) {
                    errors.push('{{ __("messages.invalid_email_format") }}');
                    isValid = false;
                    emailInput.style.borderColor = '#ef4444';
                }
            }
            break;

        case 3:
            const hasExperience = document.getElementById('experienceContainer').children.length > 0;
            const hasEducation = document.getElementById('educationContainer').children.length > 0;

            if (!hasExperience && !hasEducation) {
                errors.push('{{ __("messages.add_at_least_experience_or_education") }}');
                isValid = false;
            }
            break;

        case 4:
            const hasSkills = document.getElementById('skillsContainer').children.length > 0;
            if (!hasSkills) {
                errors.push('{{ __("messages.add_at_least_one_skill") }}');
                isValid = false;
            }
            break;
    }

    // Display errors
    if (errors.length > 0) {
        errors.forEach(error => {
            showNotification(error, 'error');
        });
    }

    return isValid;
}

// Simple step validation
function isStepCompleted(step) {
    switch(step) {
        case 1:
            return document.querySelector('input[name="template"]:checked') !== null;
        case 2:
            const requiredPersonal = ['personal_info[full_name]', 'personal_info[email]', 'personal_info[phone]'];
            return requiredPersonal.every(field => {
                const input = document.querySelector(`input[name="${field}"]`);
                return input && input.value.trim() !== '';
            });
        case 3:
            return document.getElementById('experienceContainer').children.length > 0 ||
                   document.getElementById('educationContainer').children.length > 0;
        case 4:
            return document.getElementById('skillsContainer').children.length > 0;
        case 5:
            return true;
        default:
            return false;
    }
}

// Submit CV to database
function submitCV() {
    const formData = new FormData(document.getElementById('cvBuilderForm'));

    // Show loading
    showNotification('{{ __("messages.creating_cv") }}', 'info');

    fetch('{{ route("cv.store") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showNotification('{{ __("messages.cv_created_successfully") }}', 'success');
            // Redirect to CV preview or download
            setTimeout(() => {
                window.location.href = data.redirect_url || '{{ route("cv.index") }}';
            }, 2000);
        } else {
            showNotification(data.message || '{{ __("messages.error_creating_cv") }}', 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('{{ __("messages.error_creating_cv") }}', 'error');
    });
}

// Auto-save functionality
let autoSaveTimeout;
function autoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
        const formData = new FormData(document.getElementById('cvBuilderForm'));
        formData.append('auto_save', '1');

        fetch('{{ route("cv.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                console.log('Auto-saved successfully');
                // Show subtle indicator
                const indicator = document.createElement('div');
                indicator.className = 'fixed top-4 right-4 bg-green-500 text-white px-3 py-1 rounded text-sm z-50';
                indicator.textContent = '{{ __("messages.auto_saved") }}';
                document.body.appendChild(indicator);
                setTimeout(() => indicator.remove(), 2000);
            }
        })
        .catch(error => console.log('Auto-save failed:', error));
    }, 3000); // Auto-save after 3 seconds of inactivity
}

// Add auto-save listeners to form inputs
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('cvBuilderForm');
    if (form) {
        form.addEventListener('input', autoSave);
        form.addEventListener('change', autoSave);
    }
});

// Load existing CV data if editing
function loadExistingCV() {
    const cvId = new URLSearchParams(window.location.search).get('id');
    if (cvId) {
        fetch(`{{ url('/cv-builder') }}/${cvId}/data`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    populateForm(data.cv);
                }
            })
            .catch(error => console.error('Error loading CV:', error));
    }
}

// Populate form with existing data
function populateForm(cvData) {
    // Populate personal info
    if (cvData.personal_info) {
        Object.keys(cvData.personal_info).forEach(key => {
            const input = document.querySelector(`input[name="personal_info[${key}]"]`);
            if (input) {
                input.value = cvData.personal_info[key];
            }
        });
    }

    // Populate template selection
    if (cvData.template) {
        const templateRadio = document.querySelector(`input[name="template"][value="${cvData.template}"]`);
        if (templateRadio) {
            templateRadio.checked = true;
            templateRadio.closest('.template-card').classList.add('selected');
        }
    }

    // Populate experiences
    if (cvData.experiences && cvData.experiences.length > 0) {
        cvData.experiences.forEach((exp, index) => {
            if (index > 0) addExperienceField(); // Add additional fields if needed
            Object.keys(exp).forEach(key => {
                const input = document.querySelector(`input[name="experience[${index}][${key}]"], textarea[name="experience[${index}][${key}]"]`);
                if (input) {
                    input.value = exp[key];
                }
            });
        });
    }

    // Populate education
    if (cvData.education && cvData.education.length > 0) {
        cvData.education.forEach((edu, index) => {
            if (index > 0) addEducationField();
            Object.keys(edu).forEach(key => {
                const input = document.querySelector(`input[name="education[${index}][${key}]"]`);
                if (input) {
                    input.value = edu[key];
                }
            });
        });
    }

    // Populate skills
    if (cvData.skills && cvData.skills.length > 0) {
        cvData.skills.forEach((skill, index) => {
            if (index > 0) addSkillField();
            const nameInput = document.querySelector(`input[name="skills[${index}][name]"]`);
            const levelSelect = document.querySelector(`select[name="skills[${index}][level]"]`);
            if (nameInput) nameInput.value = skill.name;
            if (levelSelect) levelSelect.value = skill.level;
        });
    }

    // Populate languages
    if (cvData.languages && cvData.languages.length > 0) {
        cvData.languages.forEach((lang, index) => {
            if (index > 0) addLanguageField();
            const nameInput = document.querySelector(`input[name="languages[${index}][name]"]`);
            const levelSelect = document.querySelector(`select[name="languages[${index}][level]"]`);
            if (nameInput) nameInput.value = lang.name;
            if (levelSelect) levelSelect.value = lang.level;
        });
    }

    updateSummaries();
}

// Initialize existing CV loading
loadExistingCV();
</script>
@endpush
