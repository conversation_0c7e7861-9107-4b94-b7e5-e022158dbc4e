@extends('layouts.app')

@section('title', __('messages.create_cv_title'))
@section('description', __('messages.create_cv_description'))

@push('styles')
<style>
    .step-indicator {
        transition: all 0.3s ease;
    }
    .step-indicator.active {
        background: linear-gradient(135deg, #3B82F6, #8B5CF6);
        color: white;
        transform: scale(1.1);
    }
    .step-indicator.completed {
        background: linear-gradient(135deg, #10B981, #059669);
        color: white;
    }
    .form-section {
        display: none;
        animation: fadeIn 0.5s ease-in-out;
    }
    .form-section.active {
        display: block;
    }
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    /* Ensure all 5 steps are visible */
    .step-indicator[data-step="5"] {
        display: flex !important;
        visibility: visible !important;
    }

    /* Ensure step 5 form section can be shown */
    .form-section[data-step="5"] {
        display: none;
    }

    .form-section[data-step="5"].active {
        display: block !important;
    }
    .template-card {
        transition: all 0.3s ease;
        cursor: pointer;
    }
    .template-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
    }
    .template-card.selected {
        border-color: #3B82F6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    }
    .template-card {
        background: linear-gradient(145deg, #374151, #1f2937);
        border: 1px solid #4b5563;
        transition: all 0.3s ease;
    }

    .template-card:hover {
        border-color: #60a5fa;
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.5);
        transform: translateY(-2px);
    }

    .template-card.selected {
        border-color: #3b82f6;
        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
    }

    .ai-compatibility-score {
        animation: fadeInUp 0.5s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-800 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {{ __('messages.create_cv_title') }}
            </h1>
            <p class="text-lg text-gray-600 dark:text-gray-300 mb-6">
                {{ __('messages.follow_steps_create_cv') }}
            </p>

            <!-- AI Quick Generate Button -->
            <div class="bg-gradient-to-r from-purple-500 to-blue-600 rounded-2xl p-6 mb-8">
                <div class="flex items-center justify-center mb-4">
                    <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-white">{{ __('messages.ai_quick_create_full') }}</h3>
                </div>
                <p class="text-white/90 mb-4">{{ __('messages.ai_quick_description') }}</p>
                <button type="button" id="aiQuickGenerate" class="bg-white text-purple-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                    <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    {{ __('messages.ai_quick_create_full') }}
                </button>
            </div>
        </div>

        <!-- Progress Steps -->
        <div class="flex justify-center mb-12">
            <div class="flex items-center justify-center space-x-3 rtl:space-x-reverse">
                <div class="step-indicator active flex items-center justify-center w-12 h-12 rounded-full bg-blue-600 text-white font-bold transition-all duration-300" data-step="1">
                    1
                </div>
                <div class="w-16 h-1 bg-gray-200 dark:bg-gray-700 transition-all duration-300"></div>
                <div class="step-indicator flex items-center justify-center w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 font-bold transition-all duration-300" data-step="2">
                    2
                </div>
                <div class="w-16 h-1 bg-gray-200 dark:bg-gray-700 transition-all duration-300"></div>
                <div class="step-indicator flex items-center justify-center w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 font-bold transition-all duration-300" data-step="3">
                    3
                </div>
                <div class="w-16 h-1 bg-gray-200 dark:bg-gray-700 transition-all duration-300"></div>
                <div class="step-indicator flex items-center justify-center w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 font-bold transition-all duration-300" data-step="4">
                    4
                </div>
                <div class="w-16 h-1 bg-gray-200 dark:bg-gray-700 transition-all duration-300"></div>
                <div class="step-indicator flex items-center justify-center w-12 h-12 rounded-full bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-300 font-bold transition-all duration-300" data-step="5">
                    5
                </div>
            </div>
        </div>

        <!-- CV Builder Form -->
        <form id="cvBuilderForm" class="bg-gray-800 rounded-2xl shadow-2xl p-8 border border-gray-700">
            @csrf
            
            <!-- Step 1: Template Selection -->
            <div class="form-section active" data-step="1">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-4">
                        {{ __('messages.choose_template') }}
                    </h2>
                    <p class="text-gray-300">
                        {{ __('messages.choose_design_description') }}
                    </p>
                </div>

                <!-- AI Template Recommendation Section -->
                <div class="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-2xl p-6 mb-8">
                    <div class="flex items-center justify-center mb-4">
                        <div class="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center mr-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-bold text-white">{{ __('messages.ai_template_recommendation') }}</h3>
                    </div>
                    <p class="text-white/90 mb-4 text-center">{{ __('messages.ai_template_description') }}</p>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                        <input type="text" id="aiJobField" placeholder="{{ __('messages.job_field_placeholder') }}"
                               class="px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-white/70 focus:bg-white/20 focus:border-white/40 transition-colors">
                        <select id="aiExperienceLevel" class="px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white focus:bg-white/20 focus:border-white/40 transition-colors">
                            <option value="" class="text-gray-900">{{ __('messages.experience_level') }}</option>
                            <option value="entry" class="text-gray-900">{{ __('messages.entry_level') }}</option>
                            <option value="mid" class="text-gray-900">{{ __('messages.mid_level') }}</option>
                            <option value="senior" class="text-gray-900">{{ __('messages.senior_level') }}</option>
                            <option value="executive" class="text-gray-900">{{ __('messages.executive_level') }}</option>
                        </select>
                        <select id="aiIndustryType" class="px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white focus:bg-white/20 focus:border-white/40 transition-colors">
                            <option value="" class="text-gray-900">{{ __('messages.industry_type') }}</option>
                            <option value="technology" class="text-gray-900">{{ __('messages.technology') }}</option>
                            <option value="healthcare" class="text-gray-900">{{ __('messages.healthcare') }}</option>
                            <option value="finance" class="text-gray-900">{{ __('messages.finance') }}</option>
                            <option value="education" class="text-gray-900">{{ __('messages.education') }}</option>
                            <option value="marketing" class="text-gray-900">{{ __('messages.marketing') }}</option>
                            <option value="creative" class="text-gray-900">{{ __('messages.creative_arts') }}</option>
                            <option value="engineering" class="text-gray-900">{{ __('messages.engineering') }}</option>
                            <option value="sales" class="text-gray-900">{{ __('messages.sales') }}</option>
                            <option value="other" class="text-gray-900">{{ __('messages.other') }}</option>
                        </select>
                    </div>

                    <div class="text-center">
                        <button type="button" id="getAIRecommendations" class="bg-white text-indigo-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            {{ __('messages.get_ai_recommendations') }}
                        </button>
                    </div>
                </div>

                <!-- AI Recommendations Display -->
                <div id="aiRecommendations" class="hidden mb-8">
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-xl p-6">
                        <div class="flex items-center mb-4">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center mr-3">
                                <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                            </div>
                            <h4 class="text-lg font-semibold text-green-800 dark:text-green-200">{{ __('messages.ai_recommendations_title') }}</h4>
                        </div>
                        <div id="recommendationsList" class="space-y-3"></div>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    @foreach($templates as $key => $template)
                    <div class="template-card border-2 border-gray-200 dark:border-gray-600 rounded-xl p-4 bg-white dark:bg-gray-700 relative group hover:shadow-2xl transition-all duration-300"
                         data-template="{{ $key }}" data-template-type="{{ $template['type'] ?? 'general' }}" data-suitable-for="{{ $template['suitable_for'] ?? '' }}">

                        <!-- AI Recommendation Badge -->
                        <div class="ai-recommendation-badge hidden absolute -top-2 -right-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white text-xs px-3 py-1 rounded-full font-semibold shadow-lg z-10">
                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            {{ __('messages.ai_recommended') }}
                        </div>

                        <!-- Simple Template Preview -->
                        <div class="aspect-[3/4] bg-gray-800 rounded-lg mb-4 overflow-hidden border border-gray-600 relative">
                            <div class="template-preview-{{ $key }} w-full h-full p-4 text-white">
                                @if($key === 'modern')
                                    <!-- Modern Template -->
                                    <div class="bg-gradient-to-r from-blue-600 to-purple-600 p-3 rounded-t mb-3">
                                        <div class="font-bold text-sm">أحمد محمد</div>
                                        <div class="text-xs opacity-90">مطور برمجيات</div>
                                    </div>
                                    <div class="space-y-2 text-xs">
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-blue-400 rounded-full"></div>
                                            <span><EMAIL></span>
                                        </div>
                                        <div class="flex items-center space-x-2">
                                            <div class="w-2 h-2 bg-purple-400 rounded-full"></div>
                                            <span>+966 50 123 4567</span>
                                        </div>
                                        <div class="mt-3">
                                            <div class="text-blue-400 font-semibold mb-1">الخبرات</div>
                                            <div>شركة التقنية المتقدمة</div>
                                            <div class="text-gray-400">2020 - الآن</div>
                                        </div>
                                        <div class="mt-3">
                                            <div class="text-blue-400 font-semibold mb-1">المهارات</div>
                                            <div class="flex flex-wrap gap-1">
                                                <span class="bg-blue-600 px-2 py-1 rounded text-xs">React</span>
                                                <span class="bg-purple-600 px-2 py-1 rounded text-xs">Node.js</span>
                                            </div>
                                        </div>
                                    </div>
                                @elseif($key === 'professional')
                                    <!-- Professional Template -->
                                    <div class="border-b-2 border-gray-400 pb-2 mb-3">
                                        <div class="font-bold text-sm">سارة أحمد</div>
                                        <div class="text-xs text-gray-300">محاسبة قانونية</div>
                                        <div class="text-xs text-gray-400"><EMAIL> | +966 55 123 4567</div>
                                    </div>
                                    <div class="space-y-2 text-xs">
                                        <div>
                                            <div class="font-semibold text-gray-200 uppercase tracking-wide mb-1">الملخص المهني</div>
                                            <div class="text-gray-300">محاسبة معتمدة مع خبرة 8 سنوات في المراجعة المالية...</div>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-200 uppercase tracking-wide mb-1">الخبرة المهنية</div>
                                            <div>
                                                <div class="font-medium">شركة المراجعة المالية</div>
                                                <div class="text-gray-400">محاسب أول | 2018 - الآن</div>
                                            </div>
                                        </div>
                                    </div>
                                @elseif($key === 'creative')
                                    <!-- Creative Template -->
                                    <div class="bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500 p-3 rounded-lg mb-3">
                                        <div class="font-bold text-sm">ليلى حسن</div>
                                        <div class="text-xs opacity-90">مصممة جرافيك</div>
                                    </div>
                                    <div class="space-y-2 text-xs">
                                        <div class="bg-gradient-to-r from-purple-600 to-pink-600 p-2 rounded">
                                            <div class="font-semibold text-white mb-1">نبذة إبداعية</div>
                                            <div class="text-white opacity-90">مصممة شغوفة بالإبداع البصري...</div>
                                        </div>
                                        <div class="space-y-1">
                                            <div class="flex items-center">
                                                <div class="w-3 h-1 bg-gradient-to-r from-purple-500 to-pink-500 rounded mr-2"></div>
                                                <span>Adobe Creative Suite</span>
                                            </div>
                                            <div class="flex items-center">
                                                <div class="w-3 h-1 bg-gradient-to-r from-pink-500 to-orange-500 rounded mr-2"></div>
                                                <span>UI/UX Design</span>
                                            </div>
                                        </div>
                                    </div>
                                @else
                                    <!-- Minimal Template -->
                                    <div class="border-l-4 border-gray-400 pl-3 mb-3">
                                        <div class="font-bold text-sm">د. محمد علي</div>
                                        <div class="text-xs text-gray-300">أستاذ جامعي - علوم الحاسوب</div>
                                    </div>
                                    <div class="space-y-2 text-xs">
                                        <div>
                                            <div class="font-medium text-gray-200">معلومات الاتصال</div>
                                            <div class="text-gray-300"><EMAIL></div>
                                            <div class="text-gray-300">+966 50 123 4567</div>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-200">التعليم</div>
                                            <div class="text-gray-300">دكتوراه في علوم الحاسوب</div>
                                            <div class="text-gray-400">جامعة الملك سعود، 2015</div>
                                        </div>
                                        <div>
                                            <div class="font-medium text-gray-200">المنشورات</div>
                                            <div class="text-gray-300">15 بحث محكم</div>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <h3 class="font-bold text-white mb-2">{{ $template['name'] }}</h3>
                        <p class="text-sm text-gray-300 mb-3">{{ $template['description'] }}</p>

                        <!-- Template Suitability Info -->
                        <div class="template-suitability text-xs text-gray-400 mb-3">
                            <span class="font-medium">{{ __('messages.best_for') }}:</span> {{ $template['suitable_for'] ?? __('messages.all_fields') }}
                        </div>

                        <!-- AI Compatibility Score -->
                        <div class="ai-compatibility-score hidden mb-3">
                            <div class="flex items-center justify-between text-xs">
                                <span class="text-gray-600 dark:text-gray-400">{{ __('messages.ai_compatibility') }}</span>
                                <div class="flex items-center">
                                    <div class="compatibility-stars flex mr-1"></div>
                                    <span class="compatibility-percentage font-medium text-green-600"></span>
                                </div>
                            </div>
                        </div>

                        <input type="radio" name="template" value="{{ $key }}" class="hidden">
                    </div>
                    @endforeach
                </div>
            </div>

            <!-- Step 2: Personal Information -->
            <div class="form-section" data-step="2">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-4">
                        {{ __('messages.personal_information') }}
                    </h2>
                    <p class="text-gray-300">
                        {{ __('messages.enter_basic_info') }}
                    </p>

                    <!-- AI Assistant Button -->
                    <div class="mt-4">
                        <button type="button" onclick="getAISuggestions('personal_info')"
                                class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 shadow-lg">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            {{ __('messages.ai_optimize_profile') }}
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            {{ __('messages.full_name') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="personal_info[full_name]" required
                               class="w-full px-4 py-3 border border-gray-600 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            {{ __('messages.email') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="email" name="personal_info[email]" required
                               class="w-full px-4 py-3 border border-gray-600 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            {{ __('messages.phone') }} <span class="text-red-500">*</span>
                        </label>
                        <input type="tel" name="personal_info[phone]" required
                               class="w-full px-4 py-3 border border-gray-600 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            {{ __('messages.address') }}
                        </label>
                        <input type="text" name="personal_info[address]"
                               class="w-full px-4 py-3 border border-gray-600 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            {{ __('LinkedIn') }}
                        </label>
                        <input type="url" name="personal_info[linkedin]"
                               class="w-full px-4 py-3 border border-gray-600 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">
                            {{ __('messages.website') }}
                        </label>
                        <input type="url" name="personal_info[website]"
                               class="w-full px-4 py-3 border border-gray-600 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                </div>

                <div class="mt-6">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block text-sm font-medium text-gray-300">
                            {{ __('messages.brief_summary') }}
                        </label>
                        <button type="button" class="ai-suggest-btn text-sm bg-gradient-to-r from-purple-500 to-blue-600 text-white px-3 py-1 rounded-lg hover:from-purple-600 hover:to-blue-700 transition-colors" data-section="summary">
                            <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            {{ __('messages.smart_suggestions') }}
                        </button>
                    </div>
                    <textarea name="summary" rows="4"
                              class="w-full px-4 py-3 border border-gray-600 bg-gray-700 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                              placeholder="{{ __('messages.brief_about_yourself') }}"></textarea>
                    <p class="text-xs text-gray-400 mt-1">{{ __('messages.ai_will_help_summary') }}</p>
                </div>
            </div>

            <!-- Step 3: Experience & Education -->
            <div class="form-section" data-step="3">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-4">
                        {{ __('messages.experience_education') }}
                    </h2>
                    <p class="text-gray-300">
                        {{ __('messages.add_experience_education') }}
                    </p>

                    <!-- AI Assistant Buttons -->
                    <div class="mt-4 flex flex-wrap justify-center gap-3">
                        <button type="button" onclick="getAISuggestions('experience')"
                                class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-600 to-teal-600 text-white rounded-lg hover:from-green-700 hover:to-teal-700 transition-all duration-300 shadow-lg">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                            </svg>
                            {{ __('messages.ai_enhance_experience') }}
                        </button>
                        <button type="button" onclick="getAISuggestions('education')"
                                class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-300 shadow-lg">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                            </svg>
                            {{ __('messages.ai_optimize_education') }}
                        </button>
                    </div>
                </div>

                <!-- Experience Section -->
                <div class="mb-8">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-white">{{ __('messages.work_experiences') }}</h3>
                        <button type="button" id="addExperience"
                                class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            {{ __('messages.add_experience') }}
                        </button>
                    </div>
                    <div id="experienceContainer" class="space-y-4"></div>
                </div>

                <!-- Education Section -->
                <div>
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.education') }}</h3>
                        <button type="button" id="addEducation" 
                                class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            {{ __('messages.add_qualification') }}
                        </button>
                    </div>
                    <div id="educationContainer"></div>
                </div>
            </div>

            <!-- Step 4: Skills & Languages -->
            <div class="form-section" data-step="4">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-4">
                        {{ __('messages.skills_languages') }}
                    </h2>
                    <p class="text-gray-300">
                        {{ __('messages.add_skills_languages') }}
                    </p>

                    <!-- AI Assistant Buttons -->
                    <div class="mt-4 flex flex-wrap justify-center gap-3">
                        <button type="button" onclick="getAISuggestions('skills')"
                                class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-orange-600 to-red-600 text-white rounded-lg hover:from-orange-700 hover:to-red-700 transition-all duration-300 shadow-lg">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            {{ __('messages.ai_suggest_skills') }}
                        </button>
                        <button type="button" onclick="getAISuggestions('languages')"
                                class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-cyan-600 to-blue-600 text-white rounded-lg hover:from-cyan-700 hover:to-blue-700 transition-all duration-300 shadow-lg">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                            </svg>
                            {{ __('messages.ai_optimize_languages') }}
                        </button>
                    </div>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Skills -->
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.skills') }}</h3>
                            <button type="button" id="addSkill" 
                                    class="inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                {{ __('messages.add_skill') }}
                            </button>
                        </div>
                        <div id="skillsContainer"></div>
                    </div>

                    <!-- Languages -->
                    <div>
                        <div class="flex items-center justify-between mb-4">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.languages') }}</h3>
                            <button type="button" id="addLanguage" 
                                    class="inline-flex items-center px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                {{ __('messages.add_language') }}
                            </button>
                        </div>
                        <div id="languagesContainer"></div>
                    </div>
                </div>

                <!-- Certifications -->
                <div class="mt-8">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.certifications_courses') }}</h3>
                        <button type="button" id="addCertification" 
                                class="inline-flex items-center px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            {{ __('messages.add_certification') }}
                        </button>
                    </div>
                    <div id="certificationsContainer"></div>
                </div>
            </div>

            <!-- Step 5: Review & Generate -->
            <div class="form-section" data-step="5">
                <div class="text-center mb-8">
                    <h2 class="text-2xl font-bold text-white mb-4">
                        {{ __('messages.review_generate') }}
                    </h2>
                    <p class="text-gray-300">
                        {{ __('messages.review_information_generate') }}
                    </p>

                    <!-- AI Final Review Buttons -->
                    <div class="mt-4 flex flex-wrap justify-center gap-3">
                        <button type="button" onclick="performAIReview()"
                                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-lg hover:from-emerald-700 hover:to-green-700 transition-all duration-300 shadow-lg">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {{ __('messages.ai_final_review') }}
                        </button>
                        <button type="button" onclick="optimizeWithAI()"
                                class="inline-flex items-center px-6 py-3 bg-gradient-to-r from-violet-600 to-purple-600 text-white rounded-lg hover:from-violet-700 hover:to-purple-700 transition-all duration-300 shadow-lg">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            {{ __('messages.ai_optimize_cv') }}
                        </button>
                    </div>
                </div>

                <!-- CV Preview -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Preview Panel -->
                    <div class="bg-gray-700 rounded-lg p-6 border border-gray-600">
                        <h3 class="text-lg font-semibold text-white mb-4">{{ __('messages.cv_preview') }}</h3>
                        <div id="cvPreview" class="bg-white rounded-lg p-4 min-h-[600px] shadow-lg">
                            <div class="text-center text-gray-500 py-20">
                                <svg class="w-16 h-16 mx-auto mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <p>{{ __('messages.cv_preview_will_appear_here') }}</p>
                            </div>
                        </div>

                        <!-- Preview Button -->
                        <button type="button" id="generatePreviewBtn" class="w-full mt-4 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                            <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                            </svg>
                            {{ __('messages.generate_preview') }}
                        </button>
                    </div>

                    <!-- Summary Panel -->
                    <div class="space-y-6">
                        <!-- Personal Info Summary -->
                        <div class="bg-gray-700 rounded-lg p-6 border border-gray-600">
                            <h4 class="text-lg font-semibold text-white mb-3">{{ __('messages.personal_information') }}</h4>
                            <div id="personalSummary" class="text-gray-300 text-sm space-y-1">
                                <p class="text-gray-400">{{ __('messages.no_information_entered') }}</p>
                            </div>
                        </div>

                        <!-- Experience Summary -->
                        <div class="bg-gray-700 rounded-lg p-6 border border-gray-600">
                            <h4 class="text-lg font-semibold text-white mb-3">{{ __('messages.work_experiences') }}</h4>
                            <div id="experienceSummary" class="text-gray-300 text-sm">
                                <p class="text-gray-400">{{ __('messages.no_experiences_added') }}</p>
                            </div>
                        </div>

                        <!-- Generation Options -->
                        <div class="bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-white mb-4">{{ __('messages.generation_options') }}</h4>
                            <div class="space-y-3">
                                <label class="flex items-center text-white">
                                    <input type="checkbox" name="ai_enhance" checked class="rounded border-gray-300 text-blue-600 mr-3">
                                    {{ __('messages.ai_enhance_content') }}
                                </label>
                                <label class="flex items-center text-white">
                                    <input type="checkbox" name="optimize_keywords" checked class="rounded border-gray-300 text-blue-600 mr-3">
                                    {{ __('messages.optimize_keywords') }}
                                </label>
                                <label class="flex items-center text-white">
                                    <input type="checkbox" name="professional_formatting" checked class="rounded border-gray-300 text-blue-600 mr-3">
                                    {{ __('messages.professional_formatting') }}
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Navigation Buttons -->
            <div class="flex justify-between mt-12 pt-8 border-t border-gray-200 dark:border-gray-600">
                <button type="button" id="prevBtn" 
                        class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                        style="display: none;">
                    {{ __('messages.previous') }}
                </button>
                
                <div class="flex space-x-4 rtl:space-x-reverse">
                    <button type="button" id="nextBtn"
                            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                        {{ __('messages.next') }}
                    </button>

                    <button type="submit" id="submitBtn"
                            class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                            style="display: none;">
                        {{ __('messages.create_cv') }}
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    let currentStep = 1;
    const totalSteps = 5;

    // Template selection
    document.querySelectorAll('.template-card').forEach(card => {
        card.addEventListener('click', function() {
            // Remove selected class from all cards
            document.querySelectorAll('.template-card').forEach(c => c.classList.remove('selected'));

            // Add selected class to clicked card
            this.classList.add('selected');

            // Check the radio button
            this.querySelector('input[type="radio"]').checked = true;

            // Show selection feedback
            const templateName = this.querySelector('h3').textContent;
            showNotification(`تم اختيار قالب: ${templateName}`, 'success');

            // Enable next button if it was disabled
            const nextBtn = document.getElementById('nextBtn');
            if (nextBtn) {
                nextBtn.disabled = false;
                nextBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            }
        });
    });

    // AI Template Recommendations
    document.getElementById('getAIRecommendations').addEventListener('click', function() {
        const jobField = document.getElementById('aiJobField').value;
        const experienceLevel = document.getElementById('aiExperienceLevel').value;
        const industryType = document.getElementById('aiIndustryType').value;

        if (!jobField && !experienceLevel && !industryType) {
            showNotification('{{ __("messages.please_fill_at_least_one_field") }}', 'error');
            return;
        }

        getAITemplateRecommendations(jobField, experienceLevel, industryType);
    });

    // Get AI Template Recommendations
    function getAITemplateRecommendations(jobField, experienceLevel, industryType) {
        const button = document.getElementById('getAIRecommendations');
        const originalText = button.innerHTML;

        button.disabled = true;
        button.innerHTML = `
            <svg class="w-5 h-5 inline mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ __('messages.analyzing') }}...
        `;

        const requestData = {
            job_field: jobField,
            experience_level: experienceLevel,
            industry_type: industryType,
            language: '{{ app()->getLocale() }}'
        };

        fetch('{{ route("cv.template.recommendations") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayAIRecommendations(data.recommendations);
                highlightRecommendedTemplates(data.recommended_templates, data.compatibility_scores);
                showDetailedAnalysis(data.detailed_analysis);
                showNotification('{{ __("messages.recommendations_generated_successfully") }}', 'success');
            } else {
                showNotification(data.message || '{{ __("messages.error_getting_recommendations") }}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{{ __("messages.error_getting_recommendations") }}', 'error');
        })
        .finally(() => {
            button.disabled = false;
            button.innerHTML = originalText;
        });
    }

    // Display AI Recommendations
    function displayAIRecommendations(recommendations) {
        const recommendationsContainer = document.getElementById('aiRecommendations');
        const recommendationsList = document.getElementById('recommendationsList');

        recommendationsList.innerHTML = '';

        recommendations.forEach((recommendation, index) => {
            const recommendationElement = document.createElement('div');
            recommendationElement.className = 'flex items-start space-x-3 p-3 bg-white dark:bg-gray-800 rounded-lg border border-green-200 dark:border-green-700';
            recommendationElement.innerHTML = `
                <div class="flex-shrink-0 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center text-white text-sm font-bold">
                    ${index + 1}
                </div>
                <div class="flex-1">
                    <p class="text-gray-900 dark:text-white font-medium">${recommendation.title}</p>
                    <p class="text-gray-600 dark:text-gray-300 text-sm mt-1">${recommendation.description}</p>
                    ${recommendation.template ? `
                        <button type="button" class="mt-2 text-green-600 dark:text-green-400 text-sm font-medium hover:underline select-template-btn" data-template="${recommendation.template}">
                            {{ __('messages.select_this_template') }}
                        </button>
                    ` : ''}
                </div>
            `;
            recommendationsList.appendChild(recommendationElement);
        });

        // Add event listeners for template selection buttons
        recommendationsList.querySelectorAll('.select-template-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const templateKey = this.dataset.template;
                selectTemplate(templateKey);
            });
        });

        recommendationsContainer.classList.remove('hidden');
    }

    // Highlight Recommended Templates
    function highlightRecommendedTemplates(recommendedTemplates, compatibilityScores = {}) {
        // Remove existing highlights
        document.querySelectorAll('.template-card').forEach(card => {
            card.classList.remove('ring-2', 'ring-green-500');
            card.querySelector('.ai-recommendation-badge').classList.add('hidden');
            card.querySelector('.ai-compatibility-score').classList.add('hidden');
        });

        // Highlight recommended templates
        recommendedTemplates.forEach((templateKey, index) => {
            const templateCard = document.querySelector(`[data-template="${templateKey}"]`);
            if (templateCard) {
                templateCard.classList.add('ring-2', 'ring-green-500');
                templateCard.querySelector('.ai-recommendation-badge').classList.remove('hidden');

                // Show compatibility score
                const compatibilityElement = templateCard.querySelector('.ai-compatibility-score');
                const score = compatibilityScores[templateKey] || (95 - (index * 10)); // Default scoring

                compatibilityElement.classList.remove('hidden');
                updateCompatibilityScore(compatibilityElement, score);

                // Auto-select the first recommended template
                if (index === 0) {
                    templateCard.click();
                }
            }
        });
    }

    // Update Compatibility Score
    function updateCompatibilityScore(element, score) {
        const starsContainer = element.querySelector('.compatibility-stars');
        const percentageElement = element.querySelector('.compatibility-percentage');

        // Clear existing stars
        starsContainer.innerHTML = '';

        // Add stars based on score
        const starCount = Math.ceil(score / 20); // 5 stars max
        for (let i = 0; i < 5; i++) {
            const star = document.createElement('svg');
            star.className = `w-3 h-3 ${i < starCount ? 'text-yellow-400' : 'text-gray-300'}`;
            star.setAttribute('fill', 'currentColor');
            star.setAttribute('viewBox', '0 0 20 20');
            star.innerHTML = '<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>';
            starsContainer.appendChild(star);
        }

        percentageElement.textContent = `${score}%`;
    }

    // Select Template Function
    function selectTemplate(templateKey) {
        const templateCard = document.querySelector(`[data-template="${templateKey}"]`);
        if (templateCard) {
            templateCard.click();
            templateCard.scrollIntoView({ behavior: 'smooth', block: 'center' });
            showNotification('{{ __("messages.template_selected_successfully") }}', 'success');
        }
    }

    // Template Preview Functionality
    document.querySelectorAll('.preview-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.stopPropagation();
            const templateKey = this.dataset.template;
            showTemplatePreview(templateKey);
        });
    });

    // Show Template Preview Modal
    function showTemplatePreview(templateKey) {
        const templates = @json($templates);
        const template = templates[templateKey];

        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50 p-4';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
                <div class="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-600">
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">${template.name}</h3>
                        <p class="text-gray-600 dark:text-gray-300 mt-1">${template.description}</p>
                    </div>
                    <button type="button" onclick="this.closest('.fixed').remove()" class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <div class="p-6 overflow-y-auto max-h-[calc(90vh-140px)]">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Template Preview -->
                        <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                            <h4 class="font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.template_preview') }}</h4>
                            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg aspect-[3/4] overflow-hidden">
                                <div class="template-preview-${templateKey}-full w-full h-full p-4 text-sm">
                                    ${getFullTemplatePreview(templateKey)}
                                </div>
                            </div>
                        </div>

                        <!-- Template Features -->
                        <div class="space-y-4">
                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.template_features') }}</h4>
                                <div class="space-y-2">
                                    ${getTemplateFeatures(templateKey)}
                                </div>
                            </div>

                            <div>
                                <h4 class="font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.best_for') }}</h4>
                                <p class="text-gray-600 dark:text-gray-300">${template.suitable_for}</p>
                            </div>

                            <div class="pt-4">
                                <button type="button" onclick="selectTemplateFromPreview('${templateKey}')" class="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 rounded-lg font-semibold hover:from-blue-700 hover:to-purple-700 transition-colors">
                                    {{ __('messages.select_this_template') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
    }

    // Get Full Template Preview
    function getFullTemplatePreview(templateKey) {
        const previews = {
            'modern': `
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-4 rounded-t-lg">
                    <h1 class="text-2xl font-bold">أحمد محمد علي</h1>
                    <p class="text-lg opacity-90">مطور برمجيات متقدم</p>
                    <div class="flex items-center space-x-4 mt-2 text-sm">
                        <span><EMAIL></span>
                        <span>+966 50 123 4567</span>
                        <span>الرياض، السعودية</span>
                    </div>
                </div>
                <div class="p-4 space-y-4">
                    <div>
                        <h3 class="text-lg font-bold text-blue-600 border-b-2 border-blue-600 pb-1">الملخص المهني</h3>
                        <p class="text-gray-700 mt-2">مطور برمجيات متخصص مع خبرة 5 سنوات في تطوير تطبيقات الويب والهاتف المحمول باستخدام أحدث التقنيات.</p>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-blue-600 border-b-2 border-blue-600 pb-1">الخبرة المهنية</h3>
                        <div class="mt-2">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="font-semibold">مطور برمجيات أول</h4>
                                    <p class="text-blue-600">شركة التقنية المتقدمة</p>
                                </div>
                                <span class="text-gray-500 text-sm">2020 - الآن</span>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-blue-600 border-b-2 border-blue-600 pb-1">المهارات التقنية</h3>
                        <div class="flex flex-wrap gap-2 mt-2">
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">React.js</span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Node.js</span>
                            <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm">Python</span>
                        </div>
                    </div>
                </div>
            `,
            'professional': `
                <div class="border-b-4 border-gray-800 pb-4 mb-4">
                    <h1 class="text-2xl font-bold text-gray-800">سارة أحمد الزهراني</h1>
                    <p class="text-lg text-gray-600">محاسبة قانونية معتمدة</p>
                    <div class="text-sm text-gray-500 mt-2">
                        <EMAIL> | +966 55 123 4567 | جدة، السعودية
                    </div>
                </div>
                <div class="space-y-4">
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 uppercase tracking-wide border-b border-gray-300 pb-1">الملخص المهني</h3>
                        <p class="text-gray-700 mt-2 leading-relaxed">محاسبة قانونية معتمدة مع خبرة 8 سنوات في المراجعة المالية وإعداد التقارير المالية للشركات الكبرى.</p>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 uppercase tracking-wide border-b border-gray-300 pb-1">الخبرة المهنية</h3>
                        <div class="mt-2 space-y-3">
                            <div>
                                <div class="flex justify-between items-start">
                                    <div>
                                        <h4 class="font-bold text-gray-800">محاسب أول</h4>
                                        <p class="text-gray-600">شركة المراجعة المالية المحدودة</p>
                                        <p class="text-gray-500 text-sm">إعداد التقارير المالية والمراجعة الداخلية</p>
                                    </div>
                                    <span class="text-gray-500 text-sm">2018 - الآن</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            'creative': `
                <div class="bg-gradient-to-br from-purple-500 via-pink-500 to-orange-500 text-white p-6 rounded-lg">
                    <h1 class="text-3xl font-bold">ليلى حسن</h1>
                    <p class="text-xl opacity-90">مصممة جرافيك إبداعية</p>
                    <div class="flex items-center space-x-4 mt-3 text-sm">
                        <span><EMAIL></span>
                        <span>+966 50 987 6543</span>
                    </div>
                </div>
                <div class="p-4 space-y-4">
                    <div class="bg-gradient-to-r from-purple-100 to-pink-100 p-4 rounded-lg">
                        <h3 class="text-lg font-bold text-purple-800">نبذة إبداعية</h3>
                        <p class="text-purple-700 mt-2">مصممة شغوفة بالإبداع البصري مع خبرة 6 سنوات في تصميم الهوية البصرية والتسويق الرقمي.</p>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800">المشاريع المميزة</h3>
                        <div class="space-y-2 mt-2">
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full mr-3"></div>
                                <span>تصميم هوية بصرية لـ 50+ علامة تجارية</span>
                            </div>
                            <div class="flex items-center">
                                <div class="w-4 h-4 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full mr-3"></div>
                                <span>حملات تسويقية رقمية حائزة على جوائز</span>
                            </div>
                        </div>
                    </div>
                </div>
            `,
            'minimal': `
                <div class="border-l-4 border-gray-800 pl-4 mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">د. محمد علي الأحمد</h1>
                    <p class="text-lg text-gray-600">أستاذ جامعي - علوم الحاسوب</p>
                </div>
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">معلومات الاتصال</h3>
                        <div class="text-gray-600 space-y-1">
                            <p><EMAIL></p>
                            <p>+966 50 123 4567</p>
                            <p>جامعة الملك سعود، الرياض</p>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">التعليم</h3>
                        <div class="space-y-2">
                            <div>
                                <p class="font-medium text-gray-800">دكتوراه في علوم الحاسوب</p>
                                <p class="text-gray-600">جامعة الملك سعود، 2015</p>
                            </div>
                        </div>
                    </div>
                    <div>
                        <h3 class="text-lg font-bold text-gray-800 mb-2">الإنجازات الأكاديمية</h3>
                        <ul class="text-gray-600 space-y-1">
                            <li>• 25+ بحث محكم في مجلات عالمية</li>
                            <li>• إشراف على 15 رسالة دكتوراه</li>
                            <li>• جائزة أفضل باحث للعام 2022</li>
                        </ul>
                    </div>
                </div>
            `
        };

        return previews[templateKey] || '';
    }

    // Get Template Features
    function getTemplateFeatures(templateKey) {
        const features = {
            'modern': [
                '{{ __("messages.modern_gradient_header") }}',
                '{{ __("messages.clean_sections") }}',
                '{{ __("messages.skill_tags") }}',
                '{{ __("messages.perfect_for_tech") }}'
            ],
            'professional': [
                '{{ __("messages.classic_layout") }}',
                '{{ __("messages.formal_typography") }}',
                '{{ __("messages.structured_sections") }}',
                '{{ __("messages.corporate_friendly") }}'
            ],
            'creative': [
                '{{ __("messages.colorful_design") }}',
                '{{ __("messages.creative_elements") }}',
                '{{ __("messages.visual_appeal") }}',
                '{{ __("messages.artistic_fields") }}'
            ],
            'minimal': [
                '{{ __("messages.clean_simple") }}',
                '{{ __("messages.focus_content") }}',
                '{{ __("messages.academic_style") }}',
                '{{ __("messages.professional_minimal") }}'
            ]
        };

        return features[templateKey]?.map(feature =>
            `<div class="flex items-center text-gray-600 dark:text-gray-300">
                <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                ${feature}
            </div>`
        ).join('') || '';
    }

    // Select Template from Preview
    function selectTemplateFromPreview(templateKey) {
        selectTemplate(templateKey);
        document.querySelector('.fixed').remove();
    }

    // Generate AI Job Description
    function generateAIJobDescription(jobTitle, company, index) {
        const button = document.querySelector(`.ai-suggest-btn[data-index="${index}"]`);
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = `
            <svg class="w-3 h-3 inline mr-1 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ __('messages.generating') }}...
        `;
        button.disabled = true;

        // Make API call
        fetch('{{ route("cv.ai-job-description") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                job_title: jobTitle,
                company: company,
                language: '{{ app()->getLocale() }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const textarea = document.querySelector(`textarea[name="experience[${index}][description]"]`);
                textarea.value = data.description;
                showNotification('{{ __("messages.ai_description_generated") }}', 'success');
            } else {
                showNotification(data.message || '{{ __("messages.ai_generation_failed") }}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{{ __("messages.ai_generation_failed") }}', 'error');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    }

    // Generate CV Preview
    document.getElementById('generatePreviewBtn').addEventListener('click', function() {
        const button = this;
        const originalText = button.innerHTML;

        // Show loading state
        button.innerHTML = `
            <svg class="w-4 h-4 inline mr-2 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            {{ __('messages.generating_preview') }}...
        `;
        button.disabled = true;

        // Collect form data
        const formData = new FormData(document.getElementById('cvBuilderForm'));

        // Generate preview
        fetch('{{ route("cv.generate-preview") }}', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                document.getElementById('cvPreview').innerHTML = data.preview;
                showNotification('{{ __("messages.preview_generated_successfully") }}', 'success');
            } else {
                showNotification(data.message || '{{ __("messages.preview_generation_failed") }}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{{ __("messages.preview_generation_failed") }}', 'error');
        })
        .finally(() => {
            button.innerHTML = originalText;
            button.disabled = false;
        });
    });

    // Update summaries when moving to step 5
    function updateSummaries() {
        updatePersonalSummary();
        updateExperienceSummary();
    }

    // Update personal summary
    function updatePersonalSummary() {
        const fullName = document.querySelector('input[name="personal_info[full_name]"]')?.value || '';
        const email = document.querySelector('input[name="personal_info[email]"]')?.value || '';
        const phone = document.querySelector('input[name="personal_info[phone]"]')?.value || '';
        const address = document.querySelector('input[name="personal_info[address]"]')?.value || '';

        const summaryDiv = document.getElementById('personalSummary');
        if (fullName || email || phone || address) {
            summaryDiv.innerHTML = `
                ${fullName ? `<p><strong>{{ __('messages.name') }}:</strong> ${fullName}</p>` : ''}
                ${email ? `<p><strong>{{ __('messages.email') }}:</strong> ${email}</p>` : ''}
                ${phone ? `<p><strong>{{ __('messages.phone') }}:</strong> ${phone}</p>` : ''}
                ${address ? `<p><strong>{{ __('messages.address') }}:</strong> ${address}</p>` : ''}
            `;
        } else {
            summaryDiv.innerHTML = '<p class="text-gray-400">{{ __("messages.no_information_entered") }}</p>';
        }
    }

    // Update experience summary
    function updateExperienceSummary() {
        const experiences = document.querySelectorAll('.experience-item');
        const summaryDiv = document.getElementById('experienceSummary');

        if (experiences.length > 0) {
            let html = '';
            experiences.forEach((exp, index) => {
                const company = exp.querySelector('input[name*="[company]"]')?.value || '';
                const position = exp.querySelector('input[name*="[position]"]')?.value || '';
                if (company || position) {
                    html += `<p><strong>${position || 'منصب غير محدد'}</strong> - ${company || 'شركة غير محددة'}</p>`;
                }
            });
            summaryDiv.innerHTML = html || '<p class="text-gray-400">{{ __("messages.no_experiences_added") }}</p>';
        } else {
            summaryDiv.innerHTML = '<p class="text-gray-400">{{ __("messages.no_experiences_added") }}</p>';
        }
    }

    // Show Detailed Analysis
    function showDetailedAnalysis(detailedAnalysis) {
        if (!detailedAnalysis || Object.keys(detailedAnalysis).length === 0) return;

        // Create or update detailed analysis section
        let analysisSection = document.getElementById('detailedAnalysis');
        if (!analysisSection) {
            analysisSection = document.createElement('div');
            analysisSection.id = 'detailedAnalysis';
            analysisSection.className = 'mt-8';
            document.getElementById('aiRecommendations').appendChild(analysisSection);
        }

        analysisSection.innerHTML = `
            <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-6 mt-4">
                <div class="flex items-center mb-4">
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center mr-3">
                        <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                        </svg>
                    </div>
                    <h4 class="text-lg font-semibold text-blue-800 dark:text-blue-200">{{ __('messages.detailed_analysis') }}</h4>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    ${detailedAnalysis.profile_summary ? `
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                            <h5 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">{{ __('messages.profile_summary') }}</h5>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">${detailedAnalysis.profile_summary}</p>
                        </div>
                    ` : ''}

                    ${detailedAnalysis.template_reasoning ? `
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                            <h5 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">{{ __('messages.template_reasoning') }}</h5>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">${detailedAnalysis.template_reasoning}</p>
                        </div>
                    ` : ''}

                    ${detailedAnalysis.design_tips && detailedAnalysis.design_tips.length > 0 ? `
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                            <h5 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">{{ __('messages.design_tips') }}</h5>
                            <ul class="text-gray-600 dark:text-gray-300 text-sm space-y-1">
                                ${detailedAnalysis.design_tips.map(tip => `<li class="flex items-start"><span class="text-blue-500 mr-2">•</span>${tip}</li>`).join('')}
                            </ul>
                        </div>
                    ` : ''}

                    ${detailedAnalysis.industry_insights ? `
                        <div class="bg-white dark:bg-gray-800 p-4 rounded-lg">
                            <h5 class="font-semibold text-gray-800 dark:text-gray-200 mb-2">{{ __('messages.industry_insights') }}</h5>
                            <p class="text-gray-600 dark:text-gray-300 text-sm">${detailedAnalysis.industry_insights}</p>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    // Step navigation
    document.getElementById('nextBtn').addEventListener('click', function() {
        if (validateCurrentStep()) {
            if (currentStep < totalSteps) {
                currentStep++;
                showStep(currentStep);

                // Update summaries when reaching step 5
                if (currentStep === 5) {
                    updateSummaries();
                }
            }
        }
    });

    document.getElementById('prevBtn').addEventListener('click', function() {
        if (currentStep > 1) {
            currentStep--;
            showStep(currentStep);
        }
    });

    function showStep(step) {
        // Hide all sections
        document.querySelectorAll('.form-section').forEach(section => {
            section.classList.remove('active');
            section.style.display = 'none';
        });

        // Show current section
        const currentSection = document.querySelector(`[data-step="${step}"]`);
        if (currentSection) {
            currentSection.classList.add('active');
            currentSection.style.display = 'block';
        }

        // Update step indicators with smooth transitions
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const stepNumber = index + 1;
            indicator.classList.remove('active', 'completed');

            if (stepNumber < step) {
                indicator.classList.add('completed');
                indicator.style.backgroundColor = '#10b981'; // green
                indicator.style.color = 'white';
            } else if (stepNumber === step) {
                indicator.classList.add('active');
                indicator.style.backgroundColor = '#3b82f6'; // blue
                indicator.style.color = 'white';
            } else {
                indicator.style.backgroundColor = '#374151'; // gray
                indicator.style.color = '#9ca3af';
            }
        });

        // Update connecting lines
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const line = indicator.nextElementSibling;
            if (line && (line.classList.contains('w-12') || line.classList.contains('w-16'))) {
                if (index + 1 < step) {
                    line.style.backgroundColor = '#10b981'; // green for completed
                } else {
                    line.style.backgroundColor = '#374151'; // gray for incomplete
                }
            }
        });

        // Update navigation buttons
        document.getElementById('prevBtn').style.display = step > 1 ? 'block' : 'none';
        document.getElementById('nextBtn').style.display = step < totalSteps ? 'block' : 'none';
        document.getElementById('submitBtn').style.display = step === totalSteps ? 'block' : 'none';
    }

    function validateCurrentStep() {
        const currentSection = document.querySelector(`[data-step="${currentStep}"]`);
        const requiredFields = currentSection.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('border-red-500');
                isValid = false;
            } else {
                field.classList.remove('border-red-500');
            }
        });

        if (currentStep === 1) {
            const selectedTemplate = document.querySelector('input[name="template"]:checked');
            if (!selectedTemplate) {
                showNotification('{{ __("messages.please_choose_template") }}', 'error');
                isValid = false;
            }
        }

        return isValid;
    }

    // Dynamic form sections
    let experienceCount = 0;
    let educationCount = 0;
    let skillCount = 0;
    let languageCount = 0;
    let certificationCount = 0;

    // Add Experience
    document.getElementById('addExperience').addEventListener('click', function() {
        const container = document.getElementById('experienceContainer');
        const experienceHtml = `
            <div class="experience-item bg-gray-700 p-6 rounded-lg mb-4 border border-gray-600">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="font-semibold text-white">{{ __('messages.work_experience_item') }} #${experienceCount + 1}</h4>
                    <button type="button" class="remove-item text-red-400 hover:text-red-300">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.company_name_field') }} *</label>
                        <input type="text" name="experience[${experienceCount}][company]" required class="w-full px-3 py-2 border border-gray-600 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.position') }} *</label>
                        <input type="text" name="experience[${experienceCount}][position]" required class="w-full px-3 py-2 border border-gray-600 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.start_date') }} *</label>
                        <input type="month" name="experience[${experienceCount}][start_date]" required class="w-full px-3 py-2 border border-gray-600 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-300 mb-2">{{ __('messages.end_date') }}</label>
                        <input type="month" name="experience[${experienceCount}][end_date]" class="w-full px-3 py-2 border border-gray-600 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <div class="mt-2">
                            <label class="inline-flex items-center">
                                <input type="checkbox" name="experience[${experienceCount}][current]" class="rounded border-gray-600 text-blue-600 bg-gray-800 focus:ring-blue-500">
                                <span class="ml-2 text-sm text-gray-300">{{ __('messages.current_job') }}</span>
                            </label>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block text-sm font-medium text-gray-300">{{ __('messages.task_description') }}</label>
                        <button type="button" class="ai-suggest-btn text-xs bg-gradient-to-r from-purple-500 to-blue-600 text-white px-2 py-1 rounded hover:from-purple-600 hover:to-blue-700 transition-colors" data-section="experience" data-index="${experienceCount}">
                            <svg class="w-3 h-3 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                            {{ __('messages.ai_suggest') }}
                        </button>
                    </div>
                    <textarea name="experience[${experienceCount}][description]" rows="3" class="w-full px-3 py-2 border border-gray-600 bg-gray-800 text-white rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="{{ __('messages.describe_responsibilities') }}"></textarea>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', experienceHtml);
        experienceCount++;

        // Add remove functionality
        container.lastElementChild.querySelector('.remove-item').addEventListener('click', function() {
            this.closest('.experience-item').remove();
        });

        // Add AI suggestion functionality
        const aiButton = container.lastElementChild.querySelector('.ai-suggest-btn[data-section="experience"]');
        if (aiButton) {
            aiButton.addEventListener('click', function() {
                const index = this.dataset.index;
                const jobTitle = container.querySelector(`input[name="experience[${index}][position]"]`).value;
                const company = container.querySelector(`input[name="experience[${index}][company]"]`).value;

                if (jobTitle && company) {
                    generateAIJobDescription(jobTitle, company, index);
                } else {
                    showNotification('{{ __("messages.fill_job_title_company_first") }}', 'warning');
                }
            });
        }
    });

    // Add Education
    document.getElementById('addEducation').addEventListener('click', function() {
        const container = document.getElementById('educationContainer');
        const educationHtml = `
            <div class="education-item bg-gray-50 dark:bg-gray-700 p-6 rounded-lg mb-4">
                <div class="flex justify-between items-center mb-4">
                    <h4 class="font-semibold text-gray-900 dark:text-white">{{ __('messages.education_item') }} #${educationCount + 1}</h4>
                    <button type="button" class="remove-item text-red-600 hover:text-red-800">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.institution_name') }}</label>
                        <input type="text" name="education[${educationCount}][institution]" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.degree') }}</label>
                        <input type="text" name="education[${educationCount}][degree]" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.specialization') }}</label>
                        <input type="text" name="education[${educationCount}][field]" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.graduation_year') }}</label>
                        <input type="date" name="education[${educationCount}][end_date]" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                    </div>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', educationHtml);
        educationCount++;

        container.lastElementChild.querySelector('.remove-item').addEventListener('click', function() {
            this.closest('.education-item').remove();
        });
    });

    // Add Skill
    document.getElementById('addSkill').addEventListener('click', function() {
        const container = document.getElementById('skillsContainer');
        const skillHtml = `
            <div class="skill-item flex items-center space-x-2 rtl:space-x-reverse mb-3">
                <input type="text" name="skills[]" placeholder="{{ __('messages.skill_name_placeholder') }}" class="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                <button type="button" class="remove-item text-red-600 hover:text-red-800 p-2">
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                </button>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', skillHtml);

        container.lastElementChild.querySelector('.remove-item').addEventListener('click', function() {
            this.closest('.skill-item').remove();
        });
    });

    // Add Language
    document.getElementById('addLanguage').addEventListener('click', function() {
        const container = document.getElementById('languagesContainer');
        const languageHtml = `
            <div class="language-item bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                <div class="flex justify-between items-center mb-3">
                    <h5 class="font-medium text-gray-900 dark:text-white">{{ __('messages.language_number') }} #${languageCount + 1}</h5>
                    <button type="button" class="remove-item text-red-600 hover:text-red-800">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-2 gap-3">
                    <input type="text" name="languages[${languageCount}][language]" placeholder="{{ __('messages.language_name_placeholder') }}" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                    <select name="languages[${languageCount}][level]" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                        <option value="">{{ __('messages.proficiency_level') }}</option>
                        <option value="beginner">{{ __('messages.beginner') }}</option>
                        <option value="intermediate">{{ __('messages.intermediate') }}</option>
                        <option value="advanced">{{ __('messages.advanced') }}</option>
                        <option value="excellent">{{ __('messages.excellent') }}</option>
                        <option value="native">{{ __('messages.mother_tongue') }}</option>
                    </select>
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', languageHtml);
        languageCount++;

        container.lastElementChild.querySelector('.remove-item').addEventListener('click', function() {
            this.closest('.language-item').remove();
        });
    });

    // Add Certification
    document.getElementById('addCertification').addEventListener('click', function() {
        const container = document.getElementById('certificationsContainer');
        const certificationHtml = `
            <div class="certification-item bg-gray-50 dark:bg-gray-700 p-4 rounded-lg mb-3">
                <div class="flex justify-between items-center mb-3">
                    <h5 class="font-medium text-gray-900 dark:text-white">{{ __('messages.certificate_number') }} #${certificationCount + 1}</h5>
                    <button type="button" class="remove-item text-red-600 hover:text-red-800">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-3">
                    <input type="text" name="certifications[${certificationCount}][name]" placeholder="{{ __('messages.certificate_name') }}" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                    <input type="text" name="certifications[${certificationCount}][issuer]" placeholder="{{ __('messages.issuing_authority') }}" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                    <input type="date" name="certifications[${certificationCount}][date]" class="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-600 dark:text-white">
                </div>
            </div>
        `;
        container.insertAdjacentHTML('beforeend', certificationHtml);
        certificationCount++;

        container.lastElementChild.querySelector('.remove-item').addEventListener('click', function() {
            this.closest('.certification-item').remove();
        });
    });

    // Form submission
    document.getElementById('cvBuilderForm').addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);
        const submitBtn = document.getElementById('submitBtn');
        const originalText = submitBtn.textContent;

        submitBtn.disabled = true;
        submitBtn.textContent = '{{ __("messages.creating") }}';

        fetch('{{ route("cv.store") }}', {
            method: 'POST',
            body: formData,
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                window.location.href = data.preview_url;
            } else {
                alert(data.message || '{{ __("messages.cv_creation_error") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ __("messages.cv_creation_error") }}');
        })
        .finally(() => {
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
        });
    });

    // AI Quick Generate functionality
    document.getElementById('aiQuickGenerate').addEventListener('click', function() {
        showAIQuickGenerateModal();
    });

    // AI Suggestions for sections
    document.querySelectorAll('.ai-suggest-btn').forEach(button => {
        button.addEventListener('click', function() {
            const section = this.dataset.section;
            getAISuggestions(section);
        });
    });

    // Show AI Quick Generate Modal
    function showAIQuickGenerateModal() {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md w-full mx-4">
                <div class="text-center mb-6">
                    <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.ai_quick_create_full') }}</h3>
                    <p class="text-gray-600 dark:text-gray-300">{{ __('messages.enter_basic_info_ai') }}</p>
                </div>

                <form id="aiQuickForm">
                    <div class="space-y-4">
                        <input type="text" id="aiName" placeholder="{{ __('messages.full_name') }}" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white" required>
                        <input type="email" id="aiEmail" placeholder="{{ __('messages.email') }}" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white" required>
                        <input type="tel" id="aiPhone" placeholder="{{ __('messages.phone') }}" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white" required>
                        <input type="text" id="aiJobTitle" placeholder="{{ __('messages.desired_job_title') }}" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                        <input type="text" id="aiIndustry" placeholder="{{ __('messages.field_industry') }}" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                        <select id="aiExperienceLevel" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white">
                            <option value="">{{ __('messages.experience_level') }}</option>
                            <option value="beginner">{{ __('messages.beginner_0_2_years') }}</option>
                            <option value="intermediate">{{ __('messages.intermediate_2_5_years') }}</option>
                            <option value="advanced">{{ __('messages.advanced_5_10_years') }}</option>
                            <option value="expert">{{ __('messages.expert_10_plus_years') }}</option>
                        </select>
                        <textarea id="aiSkills" placeholder="{{ __('messages.main_skills_comma_separated') }}" rows="3" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg dark:bg-gray-700 dark:text-white"></textarea>
                    </div>

                    <div class="flex space-x-3 mt-6">
                        <button type="submit" class="flex-1 bg-gradient-to-r from-purple-500 to-blue-600 text-white py-3 rounded-lg font-semibold hover:from-purple-600 hover:to-blue-700 transition-colors">
                            <span class="generate-text">{{ __('messages.create_cv') }}</span>
                            <span class="loading-text hidden">{{ __('messages.creating') }}</span>
                        </button>
                        <button type="button" onclick="this.closest('.fixed').remove()" class="px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            {{ __('messages.cancel') }}
                        </button>
                    </div>
                </form>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle AI form submission
        document.getElementById('aiQuickForm').addEventListener('submit', function(e) {
            e.preventDefault();
            generateCVWithAI();
        });
    }

    // Generate CV with AI
    function generateCVWithAI() {
        const formData = {
            personal_info: {
                full_name: document.getElementById('aiName').value,
                email: document.getElementById('aiEmail').value,
                phone: document.getElementById('aiPhone').value
            },
            job_title: document.getElementById('aiJobTitle').value,
            industry: document.getElementById('aiIndustry').value,
            experience_level: document.getElementById('aiExperienceLevel').value,
            skills: document.getElementById('aiSkills').value.split(',').map(s => s.trim()).filter(s => s),
            language: '{{ app()->getLocale() }}'
        };

        const submitBtn = document.querySelector('#aiQuickForm button[type="submit"]');
        const generateText = submitBtn.querySelector('.generate-text');
        const loadingText = submitBtn.querySelector('.loading-text');

        submitBtn.disabled = true;
        generateText.classList.add('hidden');
        loadingText.classList.remove('hidden');

        fetch('{{ route("cv.generate.ai") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(formData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Fill the form with AI-generated content
                fillFormWithAIData(data, formData);
                // Close modal
                document.querySelector('.fixed').remove();
                // Show success message
                showNotification('{{ __("messages.cv_created_successfully") }}', 'success');
            } else {
                showNotification(data.message || '{{ __("messages.cv_creation_error") }}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{{ __("messages.cv_creation_error") }}', 'error');
        })
        .finally(() => {
            submitBtn.disabled = false;
            generateText.classList.remove('hidden');
            loadingText.classList.add('hidden');
        });
    }

    // Fill form with AI-generated data
    function fillFormWithAIData(aiData, originalData) {
        // Fill personal info
        document.querySelector('input[name="personal_info[full_name]"]').value = originalData.personal_info.full_name;
        document.querySelector('input[name="personal_info[email]"]').value = originalData.personal_info.email;
        document.querySelector('input[name="personal_info[phone]"]').value = originalData.personal_info.phone;

        // Fill summary if AI provided it
        if (aiData.sections && aiData.sections.summary) {
            document.querySelector('textarea[name="summary"]').value = aiData.sections.summary;
        }

        // Move to step 2 to show the filled data
        currentStep = 2;
        showStep(currentStep);
    }

    // Get AI suggestions for specific sections
    function getAISuggestions(section) {
        const currentContent = getCurrentSectionContent(section);
        const jobTitle = document.querySelector('input[name="personal_info[job_title]"]')?.value || '';
        const industry = document.querySelector('input[name="personal_info[industry]"]')?.value || '';

        const requestData = {
            section: section,
            current_content: currentContent,
            job_title: jobTitle,
            industry: industry,
            language: '{{ app()->getLocale() }}'
        };

        fetch('{{ route("cv.suggestions") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify(requestData)
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showSuggestionsModal(section, data.suggestions);
            } else {
                showNotification(data.message || '{{ __("messages.error_getting_suggestions") }}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{{ __("messages.error_getting_suggestions") }}', 'error');
        });
    }

    // Get current content of a section
    function getCurrentSectionContent(section) {
        switch(section) {
            case 'summary':
                return document.querySelector('textarea[name="summary"]').value;
            default:
                return '';
        }
    }

    // Show suggestions modal
    function showSuggestionsModal(section, suggestions) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-2xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="text-center mb-6">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.ai_suggestions') }}</h3>
                    <p class="text-gray-600 dark:text-gray-300">{{ __('messages.choose_suggestion_or_reference') }}</p>
                </div>

                <div class="space-y-3 mb-6">
                    ${suggestions.map((suggestion, index) => `
                        <div class="p-4 border border-gray-200 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer suggestion-item" data-suggestion="${suggestion}">
                            <p class="text-gray-900 dark:text-white">${suggestion}</p>
                        </div>
                    `).join('')}
                </div>

                <div class="flex space-x-3">
                    <button type="button" onclick="this.closest('.fixed').remove()" class="flex-1 px-6 py-3 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        {{ __('messages.close') }}
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // Handle suggestion selection
        modal.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                const suggestion = this.dataset.suggestion;
                applySuggestionToSection(section, suggestion);
                modal.remove();
                showNotification('{{ __("messages.suggestion_applied_successfully") }}', 'success');
            });
        });
    }

    // Apply suggestion to section
    function applySuggestionToSection(section, suggestion) {
        switch(section) {
            case 'summary':
                document.querySelector('textarea[name="summary"]').value = suggestion;
                break;
        }
    }

    // Show notification
    function showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // AI Final Review Function
    function performAIReview() {
        const formData = new FormData(document.getElementById('cvBuilderForm'));
        const cvData = {};

        // Collect all form data
        for (let [key, value] of formData.entries()) {
            cvData[key] = value;
        }

        showNotification('{{ __("messages.ai_reviewing_cv") }}', 'info');

        fetch('{{ route("cv.ai-review") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                cv_data: cvData,
                language: '{{ app()->getLocale() }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAIReviewModal(data.review);
            } else {
                showNotification(data.message || '{{ __("messages.error_ai_review") }}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{{ __("messages.error_ai_review") }}', 'error');
        });
    }

    // AI Optimization Function
    function optimizeWithAI() {
        const formData = new FormData(document.getElementById('cvBuilderForm'));
        const cvData = {};

        // Collect all form data
        for (let [key, value] of formData.entries()) {
            cvData[key] = value;
        }

        showNotification('{{ __("messages.ai_optimizing_cv") }}', 'info');

        fetch('{{ route("cv.ai-optimize") }}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                cv_data: cvData,
                language: '{{ app()->getLocale() }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showOptimizationModal(data.optimizations);
            } else {
                showNotification(data.message || '{{ __("messages.error_ai_optimization") }}', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('{{ __("messages.error_ai_optimization") }}', 'error');
        });
    }

    // Show AI Review Modal
    function showAIReviewModal(review) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.ai_review_results') }}</h3>
                    <p class="text-gray-600 dark:text-gray-300">{{ __('messages.ai_analysis_feedback') }}</p>
                </div>

                <div class="space-y-6 mb-6">
                    <div class="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                        <h4 class="font-semibold text-green-800 dark:text-green-200 mb-2">{{ __('messages.strengths') }}</h4>
                        <p class="text-green-700 dark:text-green-300">${review.strengths || '{{ __("messages.no_strengths_found") }}'}</p>
                    </div>

                    <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
                        <h4 class="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">{{ __('messages.improvements') }}</h4>
                        <p class="text-yellow-700 dark:text-yellow-300">${review.improvements || '{{ __("messages.no_improvements_needed") }}'}</p>
                    </div>

                    <div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                        <h4 class="font-semibold text-blue-800 dark:text-blue-200 mb-2">{{ __('messages.overall_score') }}</h4>
                        <div class="flex items-center">
                            <div class="w-full bg-gray-200 rounded-full h-2.5 mr-3">
                                <div class="bg-blue-600 h-2.5 rounded-full" style="width: ${review.score || 0}%"></div>
                            </div>
                            <span class="text-blue-700 dark:text-blue-300 font-semibold">${review.score || 0}%</span>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3">
                    <button onclick="this.closest('.fixed').remove()"
                            class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                        {{ __('messages.close') }}
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Show Optimization Modal
    function showOptimizationModal(optimizations) {
        const modal = document.createElement('div');
        modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        modal.innerHTML = `
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-4xl w-full mx-4 max-h-96 overflow-y-auto">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.ai_optimization_results') }}</h3>
                    <p class="text-gray-600 dark:text-gray-300">{{ __('messages.apply_optimizations') }}</p>
                </div>

                <div class="space-y-4 mb-6">
                    ${Object.entries(optimizations).map(([section, content]) => `
                        <div class="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h4 class="font-semibold text-gray-900 dark:text-white">${section}</h4>
                                <button onclick="applyOptimization('${section}', this)"
                                        class="px-3 py-1 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors text-sm">
                                    {{ __('messages.apply') }}
                                </button>
                            </div>
                            <p class="text-gray-700 dark:text-gray-300 text-sm">${content}</p>
                        </div>
                    `).join('')}
                </div>

                <div class="flex justify-end space-x-3">
                    <button onclick="this.closest('.fixed').remove()"
                            class="px-6 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors">
                        {{ __('messages.close') }}
                    </button>
                    <button onclick="applyAllOptimizations()"
                            class="px-6 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                        {{ __('messages.apply_all') }}
                    </button>
                </div>
            </div>
        `;
        document.body.appendChild(modal);
    }

    // Apply single optimization
    function applyOptimization(section, button) {
        // Implementation for applying specific optimization
        button.textContent = '{{ __("messages.applied") }}';
        button.disabled = true;
        button.classList.remove('bg-blue-600', 'hover:bg-blue-700');
        button.classList.add('bg-green-600');
        showNotification('{{ __("messages.optimization_applied") }}', 'success');
    }

    // Apply all optimizations
    function applyAllOptimizations() {
        const buttons = document.querySelectorAll('.fixed button[onclick*="applyOptimization"]');
        buttons.forEach(button => {
            if (!button.disabled) {
                button.click();
            }
        });
        showNotification('{{ __("messages.all_optimizations_applied") }}', 'success');
    }

    // Initialize the form
    showStep(1);

    // Ensure all steps are properly initialized
    console.log('CV Builder initialized with', totalSteps, 'steps');

    // Debug: Check if all step elements exist
    for (let i = 1; i <= totalSteps; i++) {
        const stepElement = document.querySelector(`[data-step="${i}"]`);
        const indicatorElement = document.querySelector(`.step-indicator[data-step="${i}"]`);
        console.log(`Step ${i}:`, {
            stepElement: !!stepElement,
            indicatorElement: !!indicatorElement
        });
    }
});
</script>
@endpush

@endsection
