@extends('layouts.app')

@section('title', __('messages.cv_preview'))
@section('description', __('messages.review_before_download'))

@push('styles')
<style>
    .cv-preview {
        background: white;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        min-height: 297mm; /* A4 height */
        width: 210mm; /* A4 width */
        margin: 0 auto;
        padding: 20mm;
        font-family: 'Arial', sans-serif;
        line-height: 1.6;
        color: #333;
    }
    
    .cv-header {
        border-bottom: 3px solid #3B82F6;
        padding-bottom: 20px;
        margin-bottom: 30px;
    }
    
    .cv-name {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1F2937;
        margin-bottom: 10px;
    }
    
    .cv-contact {
        display: flex;
        flex-wrap: wrap;
        gap: 20px;
        color: #6B7280;
        font-size: 0.9rem;
    }
    
    .cv-section {
        margin-bottom: 30px;
    }
    
    .cv-section-title {
        font-size: 1.3rem;
        font-weight: bold;
        color: #3B82F6;
        border-bottom: 2px solid #E5E7EB;
        padding-bottom: 5px;
        margin-bottom: 15px;
    }
    
    .cv-item {
        margin-bottom: 20px;
    }
    
    .cv-item-header {
        display: flex;
        justify-content: between;
        align-items: flex-start;
        margin-bottom: 5px;
    }
    
    .cv-item-title {
        font-weight: bold;
        color: #1F2937;
    }
    
    .cv-item-subtitle {
        color: #6B7280;
        font-style: italic;
    }
    
    .cv-item-date {
        color: #9CA3AF;
        font-size: 0.9rem;
        margin-left: auto;
    }
    
    .cv-skills {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
    }
    
    .cv-skill {
        background: #F3F4F6;
        padding: 5px 12px;
        border-radius: 15px;
        font-size: 0.9rem;
        color: #374151;
    }
    
    .cv-languages {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
    }
    
    .cv-language {
        display: flex;
        justify-content: space-between;
        padding: 8px 0;
        border-bottom: 1px solid #E5E7EB;
    }
    
    @media print {
        body { margin: 0; }
        .cv-preview { 
            box-shadow: none; 
            margin: 0;
            width: 100%;
            min-height: auto;
        }
        .no-print { display: none !important; }
    }
</style>
@endpush

@section('content')
<div class="min-h-screen bg-gray-100 dark:bg-gray-900 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header Actions -->
        <div class="no-print flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                    {{ __('messages.cv_preview') }}
                </h1>
                <p class="text-gray-600 dark:text-gray-300">
                    {{ __('messages.review_before_download') }}
                </p>
            </div>
            
            <div class="flex space-x-4 rtl:space-x-reverse">
                <a href="{{ route('cv.create') }}" 
                   class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"></path>
                    </svg>
                    {{ __('messages.edit') }}
                </a>
                
                <button onclick="window.print()" 
                        class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"></path>
                    </svg>
                    {{ __('messages.print_cv') }}
                </button>
                
                <a href="{{ route('cv.download') }}" 
                   class="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    {{ __('messages.download_pdf') }}
                </a>
            </div>
        </div>

        <!-- CV Preview -->
        <div class="cv-preview">
            <!-- Header -->
            <div class="cv-header">
                <h1 class="cv-name">{{ $cvData['personal_info']['full_name'] ?? '' }}</h1>
                <div class="cv-contact">
                    @if(!empty($cvData['personal_info']['email']))
                        <span>📧 {{ $cvData['personal_info']['email'] }}</span>
                    @endif
                    @if(!empty($cvData['personal_info']['phone']))
                        <span>📱 {{ $cvData['personal_info']['phone'] }}</span>
                    @endif
                    @if(!empty($cvData['personal_info']['address']))
                        <span>📍 {{ $cvData['personal_info']['address'] }}</span>
                    @endif
                    @if(!empty($cvData['personal_info']['linkedin']))
                        <span>💼 {{ $cvData['personal_info']['linkedin'] }}</span>
                    @endif
                    @if(!empty($cvData['personal_info']['website']))
                        <span>🌐 {{ $cvData['personal_info']['website'] }}</span>
                    @endif
                </div>
            </div>

            <!-- Summary -->
            @if(!empty($cvData['summary']))
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.professional_summary') }}</h2>
                <p>{{ $cvData['summary'] }}</p>
            </div>
            @endif

            <!-- Experience -->
            @if(!empty($cvData['experience']) && count($cvData['experience']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.work_experience') }}</h2>
                @foreach($cvData['experience'] as $exp)
                    @if(!empty($exp['company']) && !empty($exp['position']))
                    <div class="cv-item">
                        <div class="cv-item-header">
                            <div>
                                <div class="cv-item-title">{{ $exp['position'] }}</div>
                                <div class="cv-item-subtitle">{{ $exp['company'] }}</div>
                            </div>
                            @if(!empty($exp['start_date']))
                            <div class="cv-item-date">
                                {{ date('Y/m', strtotime($exp['start_date'])) }} - 
                                {{ !empty($exp['end_date']) ? date('Y/m', strtotime($exp['end_date'])) : __('messages.now') }}
                            </div>
                            @endif
                        </div>
                        @if(!empty($exp['description']))
                        <p>{{ $exp['description'] }}</p>
                        @endif
                    </div>
                    @endif
                @endforeach
            </div>
            @endif

            <!-- Education -->
            @if(!empty($cvData['education']) && count($cvData['education']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.education') }}</h2>
                @foreach($cvData['education'] as $edu)
                    @if(!empty($edu['institution']) && !empty($edu['degree']))
                    <div class="cv-item">
                        <div class="cv-item-header">
                            <div>
                                <div class="cv-item-title">{{ $edu['degree'] }}</div>
                                <div class="cv-item-subtitle">
                                    {{ $edu['institution'] }}
                                    @if(!empty($edu['field']))
                                        - {{ $edu['field'] }}
                                    @endif
                                </div>
                            </div>
                            @if(!empty($edu['end_date']))
                            <div class="cv-item-date">{{ date('Y', strtotime($edu['end_date'])) }}</div>
                            @endif
                        </div>
                    </div>
                    @endif
                @endforeach
            </div>
            @endif

            <!-- Skills -->
            @if(!empty($cvData['skills']) && count($cvData['skills']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.skills') }}</h2>
                <div class="cv-skills">
                    @foreach($cvData['skills'] as $skill)
                        @if(!empty($skill))
                        <span class="cv-skill">{{ $skill }}</span>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Languages -->
            @if(!empty($cvData['languages']) && count($cvData['languages']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.languages') }}</h2>
                <div class="cv-languages">
                    @foreach($cvData['languages'] as $lang)
                        @if(!empty($lang['language']))
                        <div class="cv-language">
                            <span>{{ $lang['language'] }}</span>
                            <span>{{ $lang['level'] ?? '' }}</span>
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Certifications -->
            @if(!empty($cvData['certifications']) && count($cvData['certifications']) > 0)
            <div class="cv-section">
                <h2 class="cv-section-title">{{ __('messages.certifications') }}</h2>
                @foreach($cvData['certifications'] as $cert)
                    @if(!empty($cert['name']))
                    <div class="cv-item">
                        <div class="cv-item-header">
                            <div>
                                <div class="cv-item-title">{{ $cert['name'] }}</div>
                                @if(!empty($cert['issuer']))
                                <div class="cv-item-subtitle">{{ $cert['issuer'] }}</div>
                                @endif
                            </div>
                            @if(!empty($cert['date']))
                            <div class="cv-item-date">{{ date('Y/m', strtotime($cert['date'])) }}</div>
                            @endif
                        </div>
                    </div>
                    @endif
                @endforeach
            </div>
            @endif
        </div>
    </div>
</div>
@endsection
