<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV - {{ $cv['personal_info']['name'] ?? 'Professional CV' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: #333;
            background: #fff;
        }

        .cv-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #667eea;
        }

        .name {
            font-size: 32px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 10px;
        }

        .contact-info {
            display: flex;
            justify-content: center;
            gap: 20px;
            flex-wrap: wrap;
            color: #666;
            font-size: 14px;
        }

        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #e2e8f0;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .summary {
            font-size: 16px;
            line-height: 1.8;
            color: #4a5568;
            text-align: justify;
        }

        .experience-item, .education-item {
            margin-bottom: 20px;
            padding-left: 20px;
            border-left: 3px solid #667eea;
            position: relative;
        }

        .experience-item::before, .education-item::before {
            content: '';
            position: absolute;
            left: -6px;
            top: 5px;
            width: 10px;
            height: 10px;
            background: #667eea;
            border-radius: 50%;
        }

        .job-title, .degree {
            font-size: 18px;
            font-weight: bold;
            color: #2d3748;
            margin-bottom: 5px;
        }

        .company, .institution {
            font-size: 16px;
            color: #667eea;
            font-weight: 600;
            margin-bottom: 5px;
        }

        .date {
            font-size: 14px;
            color: #718096;
            margin-bottom: 10px;
            font-style: italic;
        }

        .description {
            font-size: 14px;
            line-height: 1.6;
            color: #4a5568;
        }

        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .skill-item {
            background: #f7fafc;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            color: #2d3748;
            border: 1px solid #e2e8f0;
            text-align: center;
        }

        .languages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        .language-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            background: #f7fafc;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .language-name {
            font-weight: 600;
            color: #2d3748;
        }

        .language-level {
            font-size: 12px;
            color: #667eea;
            background: #e6fffa;
            padding: 2px 8px;
            border-radius: 12px;
        }

        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
            }
            
            .cv-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="header">
            <h1 class="name">{{ $cv['personal_info']['name'] ?? 'Your Name' }}</h1>
            <div class="contact-info">
                @if(!empty($cv['personal_info']['email']))
                <div class="contact-item">
                    <span>📧</span>
                    <span>{{ $cv['personal_info']['email'] }}</span>
                </div>
                @endif
                
                @if(!empty($cv['personal_info']['phone']))
                <div class="contact-item">
                    <span>📱</span>
                    <span>{{ $cv['personal_info']['phone'] }}</span>
                </div>
                @endif
                
                @if(!empty($cv['personal_info']['location']))
                <div class="contact-item">
                    <span>📍</span>
                    <span>{{ $cv['personal_info']['location'] }}</span>
                </div>
                @endif
            </div>
        </div>

        <!-- Professional Summary -->
        @if(!empty($cv['summary']))
        <div class="section">
            <h2 class="section-title">Professional Summary</h2>
            <div class="summary">{{ $cv['summary'] }}</div>
        </div>
        @endif

        <!-- Experience -->
        @if(!empty($cv['experience']) && is_array($cv['experience']))
        <div class="section">
            <h2 class="section-title">Work Experience</h2>
            @foreach($cv['experience'] as $exp)
                @if(!empty($exp['job_title']) || !empty($exp['company']))
                <div class="experience-item">
                    @if(!empty($exp['job_title']))
                    <div class="job-title">{{ $exp['job_title'] }}</div>
                    @endif
                    
                    @if(!empty($exp['company']))
                    <div class="company">{{ $exp['company'] }}</div>
                    @endif
                    
                    @if(!empty($exp['start_date']) || !empty($exp['end_date']))
                    <div class="date">
                        {{ $exp['start_date'] ?? '' }}
                        @if(!empty($exp['start_date']) && !empty($exp['end_date'])) - @endif
                        {{ $exp['end_date'] ?? '' }}
                    </div>
                    @endif
                    
                    @if(!empty($exp['description']))
                    <div class="description">{{ $exp['description'] }}</div>
                    @endif
                </div>
                @endif
            @endforeach
        </div>
        @endif

        <!-- Education -->
        @if(!empty($cv['education']) && is_array($cv['education']))
        <div class="section">
            <h2 class="section-title">Education</h2>
            @foreach($cv['education'] as $edu)
                @if(!empty($edu['degree']) || !empty($edu['institution']))
                <div class="education-item">
                    @if(!empty($edu['degree']))
                    <div class="degree">{{ $edu['degree'] }}</div>
                    @endif
                    
                    @if(!empty($edu['institution']))
                    <div class="institution">{{ $edu['institution'] }}</div>
                    @endif
                    
                    @if(!empty($edu['year']))
                    <div class="date">{{ $edu['year'] }}</div>
                    @endif
                </div>
                @endif
            @endforeach
        </div>
        @endif

        <div class="two-column">
            <!-- Skills -->
            @if(!empty($cv['skills']) && is_array($cv['skills']))
            <div class="section">
                <h2 class="section-title">Skills</h2>
                <div class="skills-grid">
                    @foreach($cv['skills'] as $skill)
                        @if(!empty($skill))
                        <div class="skill-item">{{ $skill }}</div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Languages -->
            @if(!empty($cv['languages']) && is_array($cv['languages']))
            <div class="section">
                <h2 class="section-title">Languages</h2>
                <div class="languages-grid">
                    @foreach($cv['languages'] as $lang)
                        @if(!empty($lang['language']))
                        <div class="language-item">
                            <span class="language-name">{{ $lang['language'] }}</span>
                            @if(!empty($lang['level']))
                            <span class="language-level">{{ $lang['level'] }}</span>
                            @endif
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif
        </div>

        <!-- AI Achievements (for Professional/Premium levels) -->
        @if(!empty($cv['achievements']) && is_array($cv['achievements']))
        <div class="section">
            <h2 class="section-title">Key Achievements</h2>
            <ul style="padding-left: 20px;">
                @foreach($cv['achievements'] as $achievement)
                <li style="margin-bottom: 8px; color: #4a5568;">{{ $achievement }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <!-- Career Recommendations (Premium only) -->
        @if(!empty($cv['career_recommendations']) && is_array($cv['career_recommendations']))
        <div class="section">
            <h2 class="section-title">Career Development Recommendations</h2>
            <ul style="padding-left: 20px;">
                @foreach($cv['career_recommendations'] as $recommendation)
                <li style="margin-bottom: 8px; color: #4a5568;">{{ $recommendation }}</li>
                @endforeach
            </ul>
        </div>
        @endif

        <!-- Suggested Certifications (Premium only) -->
        @if(!empty($cv['suggested_certifications']) && is_array($cv['suggested_certifications']))
        <div class="section">
            <h2 class="section-title">Suggested Certifications</h2>
            <div class="skills-grid">
                @foreach($cv['suggested_certifications'] as $cert)
                <div class="skill-item" style="background: #fff5f5; border-color: #fed7d7; color: #c53030;">{{ $cert }}</div>
                @endforeach
            </div>
        </div>
        @endif

        <!-- Footer -->
        <div style="margin-top: 40px; text-align: center; font-size: 12px; color: #a0aec0; border-top: 1px solid #e2e8f0; padding-top: 20px;">
            Generated by MonOri AI - {{ ucfirst($level ?? 'basic') }} Level
            @if($level === 'premium')
            | Premium AI-Optimized CV
            @elseif($level === 'professional')
            | Professional AI-Enhanced CV
            @else
            | Basic AI-Assisted CV
            @endif
        </div>
    </div>
</body>
</html>
