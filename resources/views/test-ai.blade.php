<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>اختبار الذكاء الاصطناعي</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 dark:bg-gray-900">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-2xl mx-auto">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-8 text-center">
                اختبار الذكاء الاصطناعي - إنشاء السيرة الذاتية
            </h1>
            
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                <form id="testForm">
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                الاسم الكامل
                            </label>
                            <input type="text" id="fullName" class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white" value="أحمد محمد علي">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                البريد الإلكتروني
                            </label>
                            <input type="email" id="email" class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white" value="<EMAIL>">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                رقم الهاتف
                            </label>
                            <input type="tel" id="phone" class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white" value="+212612345678">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                المسمى الوظيفي المرغوب
                            </label>
                            <input type="text" id="jobTitle" class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white" value="مطور ويب">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                المجال
                            </label>
                            <input type="text" id="industry" class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white" value="تكنولوجيا المعلومات">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                مستوى الخبرة
                            </label>
                            <select id="experienceLevel" class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white">
                                <option value="مبتدئ">مبتدئ</option>
                                <option value="متوسط" selected>متوسط</option>
                                <option value="متقدم">متقدم</option>
                                <option value="خبير">خبير</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                المهارات
                            </label>
                            <textarea id="skills" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-lg dark:bg-gray-700 dark:text-white">PHP, Laravel, JavaScript, Vue.js, MySQL, HTML, CSS</textarea>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <button type="submit" class="w-full bg-blue-600 text-white py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                            <span class="generate-text">إنشاء السيرة الذاتية بالذكاء الاصطناعي</span>
                            <span class="loading-text hidden">جاري الإنشاء...</span>
                        </button>
                    </div>
                </form>
                
                <div id="result" class="mt-8 hidden">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">النتيجة:</h3>
                    <div id="resultContent" class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const formData = {
                personal_info: {
                    full_name: document.getElementById('fullName').value,
                    email: document.getElementById('email').value,
                    phone: document.getElementById('phone').value
                },
                job_title: document.getElementById('jobTitle').value,
                industry: document.getElementById('industry').value,
                experience_level: document.getElementById('experienceLevel').value,
                skills: document.getElementById('skills').value.split(',').map(s => s.trim()).filter(s => s),
                language: 'ar'
            };

            const submitBtn = document.querySelector('button[type="submit"]');
            const generateText = submitBtn.querySelector('.generate-text');
            const loadingText = submitBtn.querySelector('.loading-text');
            
            submitBtn.disabled = true;
            generateText.classList.add('hidden');
            loadingText.classList.remove('hidden');

            fetch('/test-ai/generate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                },
                body: JSON.stringify(formData)
            })
            .then(response => response.json())
            .then(data => {
                console.log('Response:', data);
                
                const resultDiv = document.getElementById('result');
                const resultContent = document.getElementById('resultContent');
                
                if (data.success) {
                    resultContent.innerHTML = `
                        <h4 class="font-bold mb-2">محتوى السيرة الذاتية:</h4>
                        <pre class="whitespace-pre-wrap text-sm">${data.cv_content}</pre>
                        
                        ${data.sections ? `
                        <h4 class="font-bold mt-4 mb-2">الأقسام المستخرجة:</h4>
                        <div class="space-y-2">
                            ${Object.entries(data.sections).map(([key, value]) => `
                                <div>
                                    <strong>${key}:</strong> ${value}
                                </div>
                            `).join('')}
                        </div>
                        ` : ''}
                    `;
                } else {
                    resultContent.innerHTML = `
                        <div class="text-red-600">
                            <strong>خطأ:</strong> ${data.message || 'حدث خطأ غير معروف'}
                        </div>
                    `;
                }
                
                resultDiv.classList.remove('hidden');
            })
            .catch(error => {
                console.error('Error:', error);
                
                const resultDiv = document.getElementById('result');
                const resultContent = document.getElementById('resultContent');
                
                resultContent.innerHTML = `
                    <div class="text-red-600">
                        <strong>خطأ في الشبكة:</strong> ${error.message}
                    </div>
                `;
                
                resultDiv.classList.remove('hidden');
            })
            .finally(() => {
                submitBtn.disabled = false;
                generateText.classList.remove('hidden');
                loadingText.classList.add('hidden');
            });
        });
    </script>
</body>
</html>
