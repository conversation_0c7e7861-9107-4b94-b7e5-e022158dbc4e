@extends('layouts.app')

@section('title', 'Video Demo - MonOri AI')

@section('content')
<style>
    .video-container {
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        min-height: 100vh;
        position: relative;
        overflow: hidden;
    }
    
    .video-player {
        background: #000;
        border-radius: 20px;
        overflow: hidden;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
        position: relative;
    }
    
    .video-content {
        width: 100%;
        height: 500px;
        background: linear-gradient(45deg, #667eea, #764ba2);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }
    
    .video-slide {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transform: translateX(100%);
        transition: all 0.8s ease-in-out;
        padding: 2rem;
        text-align: center;
    }
    
    .video-slide.active {
        opacity: 1;
        transform: translateX(0);
    }
    
    .video-slide.prev {
        transform: translateX(-100%);
    }
    
    .slide-content {
        color: white;
        max-width: 600px;
    }
    
    .slide-title {
        font-size: 2.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        text-shadow: 0 2px 10px rgba(0,0,0,0.3);
    }
    
    .slide-text {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 2rem;
        text-shadow: 0 1px 5px rgba(0,0,0,0.3);
    }
    
    .slide-icon {
        font-size: 4rem;
        margin-bottom: 1rem;
        display: block;
    }
    
    .video-controls {
        background: rgba(0,0,0,0.8);
        padding: 1rem 2rem;
        display: flex;
        align-items: center;
        justify-content: space-between;
    }
    
    .play-pause-btn {
        background: #667eea;
        color: white;
        border: none;
        border-radius: 50%;
        width: 50px;
        height: 50px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .play-pause-btn:hover {
        background: #5a67d8;
        transform: scale(1.1);
    }
    
    .progress-bar {
        flex: 1;
        margin: 0 1rem;
        height: 6px;
        background: rgba(255,255,255,0.3);
        border-radius: 3px;
        overflow: hidden;
        cursor: pointer;
    }
    
    .progress-fill {
        height: 100%;
        background: #667eea;
        width: 0%;
        transition: width 0.1s ease;
    }
    
    .time-display {
        color: white;
        font-size: 0.9rem;
        min-width: 80px;
        text-align: right;
    }
    
    .audio-controls {
        background: white;
        border-radius: 15px;
        padding: 1.5rem;
        margin-top: 2rem;
        box-shadow: 0 10px 40px rgba(0,0,0,0.1);
    }
    
    .voice-selector {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .voice-option {
        background: #f8f9fa;
        border: 2px solid #e9ecef;
        border-radius: 10px;
        padding: 1rem;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
    }
    
    .voice-option:hover {
        border-color: #667eea;
        background: #f0f4ff;
    }
    
    .voice-option.selected {
        border-color: #667eea;
        background: #667eea;
        color: white;
    }
    
    .generate-btn {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 25px;
        padding: 1rem 2rem;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
    }
    
    .generate-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .script-display {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 1.5rem;
        margin-top: 2rem;
        border-left: 4px solid #667eea;
    }
    
    .script-text {
        font-size: 1rem;
        line-height: 1.8;
        color: #495057;
        white-space: pre-line;
    }
</style>

<div class="video-container">
    <div class="container mx-auto px-4 py-12">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-4xl font-bold text-white mb-4">
                @if(app()->getLocale() === 'ar')
                    فيديو تعريفي بالموقع
                @elseif(app()->getLocale() === 'fr')
                    Vidéo de présentation du site
                @else
                    Website Introduction Video
                @endif
            </h1>
            <p class="text-xl text-white/80">
                @if(app()->getLocale() === 'ar')
                    شاهد كيف يعمل موقع التوجيه المهني بالذكاء الاصطناعي
                @elseif(app()->getLocale() === 'fr')
                    Découvrez comment fonctionne le site d'orientation professionnelle avec IA
                @else
                    See how the AI career guidance website works
                @endif
            </p>
        </div>

        <!-- Video Player -->
        <div class="max-w-4xl mx-auto mb-8">
            <div class="video-player">
                <div class="video-content" id="videoContent">
                    <!-- Slide 1: Welcome -->
                    <div class="video-slide active" data-duration="5">
                        <div class="slide-content">
                            <div class="slide-icon">🎯</div>
                            <h2 class="slide-title">مرحباً بكم في MonOri AI</h2>
                            <p class="slide-text">موقع التوجيه المهني بالذكاء الاصطناعي<br>اكتشف مستقبلك المهني بطريقة ذكية ومبتكرة</p>
                        </div>
                    </div>

                    <!-- Slide 2: Problem -->
                    <div class="video-slide" data-duration="6">
                        <div class="slide-content">
                            <div class="slide-icon">🤔</div>
                            <h2 class="slide-title">هل تواجه هذه التحديات؟</h2>
                            <p class="slide-text">• محتار في اختيار التخصص المناسب؟<br>• لا تعرف ما هي نقاط قوتك؟<br>• تبحث عن توجيه مهني موثوق؟</p>
                        </div>
                    </div>

                    <!-- Slide 3: Solution -->
                    <div class="video-slide" data-duration="7">
                        <div class="slide-content">
                            <div class="slide-icon">🚀</div>
                            <h2 class="slide-title">الحل مع الذكاء الاصطناعي</h2>
                            <p class="slide-text">نستخدم أحدث تقنيات الذكاء الاصطناعي لتحليل شخصيتك ومهاراتك<br>ونقدم لك توجيه مهني مخصص ودقيق</p>
                        </div>
                    </div>

                    <!-- Slide 4: Features -->
                    <div class="video-slide" data-duration="8">
                        <div class="slide-content">
                            <div class="slide-icon">⭐</div>
                            <h2 class="slide-title">مميزاتنا الرائعة</h2>
                            <p class="slide-text">• تحليل الشخصية بالذكاء الاصطناعي<br>• إنشاء السيرة الذاتية الاحترافية<br>• محاكاة المقابلات الوظيفية<br>• توصيات وظيفية مخصصة</p>
                        </div>
                    </div>

                    <!-- Slide 5: How it works -->
                    <div class="video-slide" data-duration="6">
                        <div class="slide-content">
                            <div class="slide-icon">⚡</div>
                            <h2 class="slide-title">كيف يعمل؟</h2>
                            <p class="slide-text">1. أجب على الأسئلة البسيطة<br>2. الذكاء الاصطناعي يحلل إجاباتك<br>3. احصل على توجيه مهني مخصص<br>4. ابدأ رحلتك المهنية بثقة</p>
                        </div>
                    </div>

                    <!-- Slide 6: CTA -->
                    <div class="video-slide" data-duration="5">
                        <div class="slide-content">
                            <div class="slide-icon">🎉</div>
                            <h2 class="slide-title">ابدأ رحلتك الآن!</h2>
                            <p class="slide-text">انضم إلى آلاف المستخدمين الذين اكتشفوا مستقبلهم المهني معنا<br>التسجيل مجاني والبداية سهلة</p>
                        </div>
                    </div>
                </div>

                <!-- Video Controls -->
                <div class="video-controls">
                    <button class="play-pause-btn" id="playPauseBtn" onclick="togglePlayPause()">
                        <svg id="playIcon" class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M8 5v14l11-7z"/>
                        </svg>
                        <svg id="pauseIcon" class="w-6 h-6 hidden" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
                        </svg>
                    </button>
                    
                    <div class="progress-bar" onclick="seekTo(event)">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    
                    <div class="time-display" id="timeDisplay">0:00 / 0:37</div>
                </div>
            </div>
        </div>

        <!-- Audio Controls -->
        <div class="max-w-4xl mx-auto">
            <div class="audio-controls">
                <h3 class="text-xl font-bold text-gray-800 mb-4">إعدادات الصوت</h3>
                
                <div class="voice-selector">
                    <div class="voice-option selected" data-voice="arabic-male">
                        <h4 class="font-semibold">صوت ذكوري عربي</h4>
                        <p class="text-sm text-gray-600">نبرة واضحة ومهنية</p>
                    </div>
                    <div class="voice-option" data-voice="arabic-female">
                        <h4 class="font-semibold">صوت أنثوي عربي</h4>
                        <p class="text-sm text-gray-600">نبرة لطيفة وودودة</p>
                    </div>
                    <div class="voice-option" data-voice="english-male">
                        <h4 class="font-semibold">English Male</h4>
                        <p class="text-sm text-gray-600">Professional tone</p>
                    </div>
                    <div class="voice-option" data-voice="french-female">
                        <h4 class="font-semibold">Français Féminin</h4>
                        <p class="text-sm text-gray-600">Ton élégant</p>
                    </div>
                </div>

                <button class="generate-btn" onclick="generateVideoWithAudio()">
                    <svg class="w-5 h-5 inline mr-2" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                    </svg>
                    إنشاء الفيديو مع الصوت
                </button>
            </div>
        </div>

        <!-- Script Display -->
        <div class="max-w-4xl mx-auto">
            <div class="script-display">
                <h3 class="text-lg font-bold text-gray-800 mb-3">نص الفيديو:</h3>
                <div class="script-text" id="scriptText">
🎙️ [صوت راوي – نبرة زوينة، شبابية]

السلام عليكم ومرحبا بكم فـ موقع التوجيه المهني بالذكاء الاصطناعي!

📌 واش باقي ما عارف شنو تبغي دير فالمستقبل؟
📌 محتار بين شعب كثيرة؟ ولا باغي تعرف شنو المسار اللي يليق بك؟

ماشي مشكل، حنا هنا باش نعاونك!

🔍 الموقع ديالنا كيستعمل الذكاء الاصطناعي باش يحلل الشخصية ديالك، المهارات اللي عندك، والحوايج اللي كتعجبك، وكيعطيك توجيه مهني ديال بصّح!

⏱️ فغير شحال ديال الدقايق، غادي تعرف:
• شكون نتا على المستوى المهني
• شنو أنسب الميادين ليك
• شنو خاصك تطور فـ راسك
• وشنو هي الخطوات الجاية

سواء كنتي:
✅ تلميذ فـ الإعدادي أو الثانوي
✅ طالب فـ الجامعة
✅ ولا حتى واحد باغي يبدل المسار ديالو…

راه الموقع داير لك الحل!

🎯 ما تضيعش الوقت، جرب دابا وخلي الذكاء الاصطناعي يوريك الطريق.
راك تستاهل مستقبل زوين ومناسب ليك.

🌐 زور موقعنا فـ MonOri AI

التوجيه المهني ما بقاش صعيب… الذكاء الاصطناعي غادي يسهّلها عليك!
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-8">
            <a href="{{ route('cv.builder') }}" class="bg-white text-blue-600 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-100 transform hover:scale-105 transition-all duration-300 inline-flex items-center space-x-2 rtl:space-x-reverse mr-4">
                <span>جرب منشئ السيرة الذاتية</span>
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                </svg>
            </a>
            
            <a href="{{ route('home') }}" class="border-2 border-white text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-white hover:text-blue-600 transform hover:scale-105 transition-all duration-300 inline-flex items-center space-x-2 rtl:space-x-reverse">
                <span>العودة للرئيسية</span>
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                </svg>
            </a>
        </div>
    </div>
</div>

<script>
let currentSlide = 0;
let isPlaying = false;
let slideInterval;
let totalDuration = 37; // Total video duration in seconds
let currentTime = 0;
let progressInterval;

const slides = document.querySelectorAll('.video-slide');
const playIcon = document.getElementById('playIcon');
const pauseIcon = document.getElementById('pauseIcon');
const progressFill = document.getElementById('progressFill');
const timeDisplay = document.getElementById('timeDisplay');

// Voice selection
document.querySelectorAll('.voice-option').forEach(option => {
    option.addEventListener('click', function() {
        document.querySelectorAll('.voice-option').forEach(opt => opt.classList.remove('selected'));
        this.classList.add('selected');
    });
});

function togglePlayPause() {
    if (isPlaying) {
        pauseVideo();
    } else {
        playVideo();
    }
}

function playVideo() {
    isPlaying = true;
    playIcon.classList.add('hidden');
    pauseIcon.classList.remove('hidden');
    
    startSlideShow();
    startProgressUpdate();
}

function pauseVideo() {
    isPlaying = false;
    playIcon.classList.remove('hidden');
    pauseIcon.classList.add('hidden');
    
    clearInterval(slideInterval);
    clearInterval(progressInterval);
}

function startSlideShow() {
    slideInterval = setInterval(() => {
        nextSlide();
    }, parseInt(slides[currentSlide].dataset.duration) * 1000);
}

function startProgressUpdate() {
    progressInterval = setInterval(() => {
        currentTime += 0.1;
        if (currentTime >= totalDuration) {
            currentTime = totalDuration;
            pauseVideo();
            resetVideo();
        }
        updateProgress();
    }, 100);
}

function nextSlide() {
    slides[currentSlide].classList.remove('active');
    slides[currentSlide].classList.add('prev');
    
    currentSlide = (currentSlide + 1) % slides.length;
    
    if (currentSlide === 0) {
        // Video ended, reset
        pauseVideo();
        resetVideo();
        return;
    }
    
    slides[currentSlide].classList.remove('prev');
    slides[currentSlide].classList.add('active');
    
    clearInterval(slideInterval);
    if (isPlaying) {
        startSlideShow();
    }
}

function resetVideo() {
    currentSlide = 0;
    currentTime = 0;
    
    slides.forEach((slide, index) => {
        slide.classList.remove('active', 'prev');
        if (index === 0) {
            slide.classList.add('active');
        }
    });
    
    updateProgress();
}

function updateProgress() {
    const percentage = (currentTime / totalDuration) * 100;
    progressFill.style.width = percentage + '%';
    
    const minutes = Math.floor(currentTime / 60);
    const seconds = Math.floor(currentTime % 60);
    const totalMinutes = Math.floor(totalDuration / 60);
    const totalSeconds = totalDuration % 60;
    
    timeDisplay.textContent = `${minutes}:${seconds.toString().padStart(2, '0')} / ${totalMinutes}:${totalSeconds.toString().padStart(2, '0')}`;
}

function seekTo(event) {
    const progressBar = event.currentTarget;
    const rect = progressBar.getBoundingClientRect();
    const clickX = event.clientX - rect.left;
    const percentage = clickX / rect.width;
    
    currentTime = percentage * totalDuration;
    
    // Calculate which slide should be active based on time
    let accumulatedTime = 0;
    let targetSlide = 0;
    
    for (let i = 0; i < slides.length; i++) {
        const slideDuration = parseInt(slides[i].dataset.duration);
        if (currentTime <= accumulatedTime + slideDuration) {
            targetSlide = i;
            break;
        }
        accumulatedTime += slideDuration;
    }
    
    // Update slide
    slides[currentSlide].classList.remove('active');
    currentSlide = targetSlide;
    slides[currentSlide].classList.add('active');
    
    updateProgress();
    
    if (isPlaying) {
        clearInterval(slideInterval);
        startSlideShow();
    }
}

function generateVideoWithAudio() {
    const selectedVoice = document.querySelector('.voice-option.selected').dataset.voice;
    
    // Show loading state
    const btn = event.target;
    const originalText = btn.innerHTML;
    btn.innerHTML = '<svg class="w-5 h-5 inline mr-2 animate-spin" fill="currentColor" viewBox="0 0 24 24"><path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/></svg>جاري الإنشاء...';
    btn.disabled = true;
    
    // Simulate video generation
    setTimeout(() => {
        alert(`تم إنشاء الفيديو بنجاح!\nالصوت المحدد: ${selectedVoice}\nيمكنك الآن تحميل الفيديو أو مشاركته.`);
        
        btn.innerHTML = originalText;
        btn.disabled = false;
    }, 3000);
}

// Initialize
updateProgress();
</script>
@endsection
