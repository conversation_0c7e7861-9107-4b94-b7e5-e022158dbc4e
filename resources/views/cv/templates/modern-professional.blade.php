<!DOCTYPE html>
<html lang="{{ $language }}" dir="{{ $isRTL ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $content['full_name'] ?? 'CV' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Arial', sans-serif;
            line-height: 1.6;
            color: {{ $template->color_scheme['text'] ?? '#1e293b' }};
            background: #ffffff;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 30px;
            background: white;
            min-height: 297mm;
        }
        
        /* Header Section */
        .header {
            background: linear-gradient(135deg, {{ $template->color_scheme['primary'] ?? '#2563eb' }}, {{ $template->color_scheme['secondary'] ?? '#64748b' }});
            color: white;
            padding: 40px;
            margin: -30px -30px 30px -30px;
            position: relative;
            overflow: hidden;
        }
        
        .header::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 200px;
            height: 200px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
        }
        
        .header-content {
            display: grid;
            grid-template-columns: 1fr auto;
            gap: 30px;
            align-items: center;
            position: relative;
            z-index: 1;
        }
        
        .name-section h1 {
            font-size: 42px;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .job-title {
            font-size: 18px;
            opacity: 0.9;
            font-weight: 300;
            letter-spacing: 1px;
        }
        
        .contact-info {
            text-align: {{ $isRTL ? 'left' : 'right' }};
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
            {{ $isRTL ? 'flex-direction: row-reverse;' : '' }}
        }
        
        .contact-icon {
            width: 16px;
            height: 16px;
            margin-{{ $isRTL ? 'left' : 'right' }}: 8px;
            opacity: 0.8;
        }
        
        /* Two Column Layout */
        .main-content {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 40px;
            margin-top: 30px;
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 35px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: 700;
            color: {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            margin-bottom: 20px;
            padding-bottom: 8px;
            border-bottom: 3px solid {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            position: relative;
        }
        
        .section-title::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 0;
            width: 50px;
            height: 3px;
            background: {{ $template->color_scheme['secondary'] ?? '#64748b' }};
        }
        
        /* Professional Summary */
        .summary {
            font-size: 15px;
            line-height: 1.8;
            text-align: justify;
            color: #4b5563;
            background: {{ $template->color_scheme['accent'] ?? '#f8fafc' }};
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            {{ $isRTL ? 'border-left: none; border-right: 4px solid ' . ($template->color_scheme['primary'] ?? '#2563eb') . ';' : '' }}
        }
        
        /* Experience */
        .experience-item {
            margin-bottom: 25px;
            padding: 20px;
            background: {{ $template->color_scheme['accent'] ?? '#f8fafc' }};
            border-radius: 8px;
            border-left: 4px solid {{ $template->color_scheme['secondary'] ?? '#64748b' }};
            {{ $isRTL ? 'border-left: none; border-right: 4px solid ' . ($template->color_scheme['secondary'] ?? '#64748b') . ';' : '' }}
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 12px;
            {{ $isRTL ? 'flex-direction: row-reverse;' : '' }}
        }
        
        .job-title-main {
            font-size: 18px;
            font-weight: 600;
            color: {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            margin-bottom: 4px;
        }
        
        .company-name {
            font-size: 16px;
            color: #6b7280;
            font-weight: 500;
        }
        
        .job-duration {
            font-size: 14px;
            color: #9ca3af;
            background: white;
            padding: 4px 12px;
            border-radius: 20px;
            white-space: nowrap;
        }
        
        .job-description {
            font-size: 14px;
            line-height: 1.7;
            color: #4b5563;
            white-space: pre-line;
        }
        
        /* Education */
        .education-item {
            margin-bottom: 20px;
            padding: 15px;
            background: white;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }
        
        .degree {
            font-size: 16px;
            font-weight: 600;
            color: {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            margin-bottom: 4px;
        }
        
        .institution {
            font-size: 14px;
            color: #6b7280;
            margin-bottom: 4px;
        }
        
        .year {
            font-size: 13px;
            color: #9ca3af;
        }
        
        /* Skills */
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 8px;
        }
        
        .skill-item {
            background: {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 13px;
            text-align: center;
            font-weight: 500;
        }
        
        /* Languages */
        .language-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #e5e7eb;
            {{ $isRTL ? 'flex-direction: row-reverse;' : '' }}
        }
        
        .language-name {
            font-weight: 500;
            color: #374151;
        }
        
        .language-level {
            font-size: 12px;
            background: {{ $template->color_scheme['secondary'] ?? '#64748b' }};
            color: white;
            padding: 2px 8px;
            border-radius: 10px;
        }
        
        /* Achievements */
        .achievement-item {
            margin-bottom: 12px;
            padding-left: 20px;
            {{ $isRTL ? 'padding-left: 0; padding-right: 20px;' : '' }}
            position: relative;
        }
        
        .achievement-item::before {
            content: '★';
            position: absolute;
            left: 0;
            {{ $isRTL ? 'left: auto; right: 0;' : '' }}
            color: {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            font-weight: bold;
        }
        
        /* Core Competencies */
        .competency-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
        }
        
        .competency-item {
            background: white;
            border: 2px solid {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            color: {{ $template->color_scheme['primary'] ?? '#2563eb' }};
            padding: 10px 15px;
            border-radius: 25px;
            font-size: 13px;
            text-align: center;
            font-weight: 500;
        }
        
        @media print {
            body { print-color-adjust: exact; }
            .cv-container { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="header">
            <div class="header-content">
                <div class="name-section">
                    <h1>{{ $content['full_name'] ?? 'Full Name' }}</h1>
                    <div class="job-title">
                        @if($language === 'ar')
                            محترف متخصص
                        @elseif($language === 'fr')
                            Professionnel Spécialisé
                        @else
                            Professional Specialist
                        @endif
                    </div>
                </div>
                <div class="contact-info">
                    @if(!empty($content['email']))
                    <div class="contact-item">
                        <span class="contact-icon">✉</span>
                        <span>{{ $content['email'] }}</span>
                    </div>
                    @endif
                    @if(!empty($content['phone']))
                    <div class="contact-item">
                        <span class="contact-icon">📞</span>
                        <span>{{ $content['phone'] }}</span>
                    </div>
                    @endif
                    @if(!empty($content['address']))
                    <div class="contact-item">
                        <span class="contact-icon">📍</span>
                        <span>{{ $content['address'] }}</span>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- Left Column -->
            <div class="left-column">
                <!-- Professional Summary -->
                @if(!empty($content['professional_summary']))
                <div class="section">
                    <h2 class="section-title">
                        @if($language === 'ar')
                            الملخص المهني
                        @elseif($language === 'fr')
                            Résumé Professionnel
                        @else
                            Professional Summary
                        @endif
                    </h2>
                    <div class="summary">{{ $content['professional_summary'] }}</div>
                </div>
                @endif

                <!-- Experience -->
                @if(!empty($content['experience']) && is_array($content['experience']))
                <div class="section">
                    <h2 class="section-title">
                        @if($language === 'ar')
                            الخبرة العملية
                        @elseif($language === 'fr')
                            Expérience Professionnelle
                        @else
                            Professional Experience
                        @endif
                    </h2>
                    @foreach($content['experience'] as $exp)
                        @if(!empty($exp['job_title']) && !empty($exp['company']))
                        <div class="experience-item">
                            <div class="job-header">
                                <div>
                                    <div class="job-title-main">{{ $exp['job_title'] }}</div>
                                    <div class="company-name">{{ $exp['company'] }}</div>
                                </div>
                                @if(!empty($exp['start_date']))
                                <div class="job-duration">
                                    {{ $exp['start_date'] }}
                                    @if(!empty($exp['end_date']))
                                        - {{ $exp['end_date'] }}
                                    @else
                                        - 
                                        @if($language === 'ar')
                                            الحاضر
                                        @elseif($language === 'fr')
                                            Présent
                                        @else
                                            Present
                                        @endif
                                    @endif
                                </div>
                                @endif
                            </div>
                            @if(!empty($exp['description']))
                            <div class="job-description">{{ $exp['description'] }}</div>
                            @endif
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif

                <!-- Education -->
                @if(!empty($content['education']) && is_array($content['education']))
                <div class="section">
                    <h2 class="section-title">
                        @if($language === 'ar')
                            التعليم
                        @elseif($language === 'fr')
                            Éducation
                        @else
                            Education
                        @endif
                    </h2>
                    @foreach($content['education'] as $edu)
                        @if(!empty($edu['degree']) && !empty($edu['institution']))
                        <div class="education-item">
                            <div class="degree">{{ $edu['degree'] }}</div>
                            <div class="institution">{{ $edu['institution'] }}</div>
                            @if(!empty($edu['year']))
                            <div class="year">{{ $edu['year'] }}</div>
                            @endif
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif
            </div>

            <!-- Right Column -->
            <div class="right-column">
                <!-- Key Achievements -->
                @if(!empty($content['key_achievements']) && is_array($content['key_achievements']))
                <div class="section">
                    <h2 class="section-title">
                        @if($language === 'ar')
                            الإنجازات الرئيسية
                        @elseif($language === 'fr')
                            Réalisations Clés
                        @else
                            Key Achievements
                        @endif
                    </h2>
                    @foreach($content['key_achievements'] as $achievement)
                        <div class="achievement-item">{{ $achievement }}</div>
                    @endforeach
                </div>
                @endif

                <!-- Core Competencies -->
                @if(!empty($content['core_competencies']) && is_array($content['core_competencies']))
                <div class="section">
                    <h2 class="section-title">
                        @if($language === 'ar')
                            الكفاءات الأساسية
                        @elseif($language === 'fr')
                            Compétences Fondamentales
                        @else
                            Core Competencies
                        @endif
                    </h2>
                    <div class="competency-grid">
                        @foreach($content['core_competencies'] as $competency)
                            @if(!empty($competency))
                            <div class="competency-item">{{ $competency }}</div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Technical Skills -->
                @if(!empty($content['skills']) && is_array($content['skills']))
                <div class="section">
                    <h2 class="section-title">
                        @if($language === 'ar')
                            المهارات التقنية
                        @elseif($language === 'fr')
                            Compétences Techniques
                        @else
                            Technical Skills
                        @endif
                    </h2>
                    <div class="skills-grid">
                        @foreach($content['skills'] as $skill)
                            @if(!empty($skill))
                            <div class="skill-item">{{ $skill }}</div>
                            @endif
                        @endforeach
                    </div>
                </div>
                @endif

                <!-- Languages -->
                @if(!empty($content['languages']) && is_array($content['languages']))
                <div class="section">
                    <h2 class="section-title">
                        @if($language === 'ar')
                            اللغات
                        @elseif($language === 'fr')
                            Langues
                        @else
                            Languages
                        @endif
                    </h2>
                    @foreach($content['languages'] as $lang)
                        @if(!empty($lang['language']))
                        <div class="language-item">
                            <div class="language-name">{{ $lang['language'] }}</div>
                            @if(!empty($lang['level']))
                            <div class="language-level">{{ $lang['level'] }}</div>
                            @endif
                        </div>
                        @endif
                    @endforeach
                </div>
                @endif
            </div>
        </div>
    </div>
</body>
</html>
