<!DOCTYPE html>
<html lang="{{ $language }}" dir="{{ $isRTL ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ $content['full_name'] ?? 'CV' }}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Times New Roman', serif;
            line-height: 1.6;
            color: {{ $template->color_scheme['text'] ?? '#111827' }};
            background: #ffffff;
        }
        
        .cv-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 40px;
            background: white;
            min-height: 297mm;
        }
        
        /* Header Section */
        .header {
            text-align: center;
            padding-bottom: 30px;
            border-bottom: 3px solid {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            margin-bottom: 40px;
        }
        
        .name {
            font-size: 36px;
            font-weight: bold;
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            margin-bottom: 8px;
            letter-spacing: 2px;
            text-transform: uppercase;
        }
        
        .job-title {
            font-size: 18px;
            color: {{ $template->color_scheme['secondary'] ?? '#6b7280' }};
            font-style: italic;
            margin-bottom: 20px;
        }
        
        .contact-info {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            font-size: 14px;
            color: {{ $template->color_scheme['secondary'] ?? '#6b7280' }};
        }
        
        .contact-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
        
        /* Section Styling */
        .section {
            margin-bottom: 35px;
        }
        
        .section-title {
            font-size: 20px;
            font-weight: bold;
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            margin-bottom: 20px;
            text-transform: uppercase;
            letter-spacing: 1px;
            border-bottom: 2px solid {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            padding-bottom: 5px;
        }
        
        /* Professional Summary */
        .summary {
            font-size: 16px;
            line-height: 1.8;
            text-align: justify;
            color: #374151;
            font-style: italic;
            padding: 20px;
            background: {{ $template->color_scheme['accent'] ?? '#f9fafb' }};
            border-left: 5px solid {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            {{ $isRTL ? 'border-left: none; border-right: 5px solid ' . ($template->color_scheme['primary'] ?? '#1f2937') . ';' : '' }}
        }
        
        /* Experience */
        .experience-item {
            margin-bottom: 30px;
            padding-bottom: 25px;
            border-bottom: 1px solid #e5e7eb;
        }
        
        .experience-item:last-child {
            border-bottom: none;
        }
        
        .job-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
            margin-bottom: 15px;
            {{ $isRTL ? 'flex-direction: row-reverse;' : '' }}
        }
        
        .job-title-main {
            font-size: 18px;
            font-weight: bold;
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            margin-bottom: 5px;
        }
        
        .company-name {
            font-size: 16px;
            color: {{ $template->color_scheme['secondary'] ?? '#6b7280' }};
            font-weight: 500;
        }
        
        .job-duration {
            font-size: 14px;
            color: {{ $template->color_scheme['secondary'] ?? '#6b7280' }};
            font-style: italic;
            text-align: {{ $isRTL ? 'left' : 'right' }};
        }
        
        .job-description {
            font-size: 15px;
            line-height: 1.7;
            color: #4b5563;
            text-align: justify;
            white-space: pre-line;
        }
        
        /* Education */
        .education-item {
            margin-bottom: 20px;
            padding: 15px 0;
            border-bottom: 1px dotted #d1d5db;
        }
        
        .education-item:last-child {
            border-bottom: none;
        }
        
        .degree {
            font-size: 16px;
            font-weight: bold;
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            margin-bottom: 5px;
        }
        
        .institution {
            font-size: 15px;
            color: {{ $template->color_scheme['secondary'] ?? '#6b7280' }};
            margin-bottom: 5px;
        }
        
        .year {
            font-size: 14px;
            color: #9ca3af;
            font-style: italic;
        }
        
        /* Skills */
        .skills-list {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .skill-item {
            font-size: 14px;
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            padding: 5px 0;
            border-bottom: 1px solid {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            font-weight: 500;
        }
        
        /* Languages */
        .language-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #e5e7eb;
            {{ $isRTL ? 'flex-direction: row-reverse;' : '' }}
        }
        
        .language-item:last-child {
            border-bottom: none;
        }
        
        .language-name {
            font-weight: 500;
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
        }
        
        .language-level {
            font-size: 14px;
            color: {{ $template->color_scheme['secondary'] ?? '#6b7280' }};
            font-style: italic;
        }
        
        /* Achievements */
        .achievement-item {
            margin-bottom: 15px;
            padding-left: 25px;
            {{ $isRTL ? 'padding-left: 0; padding-right: 25px;' : '' }}
            position: relative;
            font-size: 15px;
            line-height: 1.6;
        }
        
        .achievement-item::before {
            content: '•';
            position: absolute;
            left: 0;
            {{ $isRTL ? 'left: auto; right: 0;' : '' }}
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            font-weight: bold;
            font-size: 18px;
        }
        
        /* Core Competencies */
        .competency-list {
            columns: 2;
            column-gap: 30px;
        }
        
        .competency-item {
            font-size: 15px;
            color: {{ $template->color_scheme['primary'] ?? '#1f2937' }};
            margin-bottom: 10px;
            padding-left: 20px;
            {{ $isRTL ? 'padding-left: 0; padding-right: 20px;' : '' }}
            position: relative;
            break-inside: avoid;
        }
        
        .competency-item::before {
            content: '▪';
            position: absolute;
            left: 0;
            {{ $isRTL ? 'left: auto; right: 0;' : '' }}
            color: {{ $template->color_scheme['secondary'] ?? '#6b7280' }};
        }
        
        @media print {
            body { print-color-adjust: exact; }
            .cv-container { padding: 30px; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="header">
            <h1 class="name">{{ $content['full_name'] ?? 'Full Name' }}</h1>
            <div class="job-title">
                @if($language === 'ar')
                    محترف متخصص
                @elseif($language === 'fr')
                    Professionnel Spécialisé
                @else
                    Professional Specialist
                @endif
            </div>
            <div class="contact-info">
                @if(!empty($content['email']))
                <div class="contact-item">
                    <span>✉</span>
                    <span>{{ $content['email'] }}</span>
                </div>
                @endif
                @if(!empty($content['phone']))
                <div class="contact-item">
                    <span>☎</span>
                    <span>{{ $content['phone'] }}</span>
                </div>
                @endif
                @if(!empty($content['address']))
                <div class="contact-item">
                    <span>⌂</span>
                    <span>{{ $content['address'] }}</span>
                </div>
                @endif
            </div>
        </div>

        <!-- Professional Summary -->
        @if(!empty($content['professional_summary']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    الملخص المهني
                @elseif($language === 'fr')
                    Résumé Professionnel
                @else
                    Professional Summary
                @endif
            </h2>
            <div class="summary">{{ $content['professional_summary'] }}</div>
        </div>
        @endif

        <!-- Experience -->
        @if(!empty($content['experience']) && is_array($content['experience']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    الخبرة العملية
                @elseif($language === 'fr')
                    Expérience Professionnelle
                @else
                    Professional Experience
                @endif
            </h2>
            @foreach($content['experience'] as $exp)
                @if(!empty($exp['job_title']) && !empty($exp['company']))
                <div class="experience-item">
                    <div class="job-header">
                        <div>
                            <div class="job-title-main">{{ $exp['job_title'] }}</div>
                            <div class="company-name">{{ $exp['company'] }}</div>
                        </div>
                        @if(!empty($exp['start_date']))
                        <div class="job-duration">
                            {{ $exp['start_date'] }}
                            @if(!empty($exp['end_date']))
                                - {{ $exp['end_date'] }}
                            @else
                                - 
                                @if($language === 'ar')
                                    الحاضر
                                @elseif($language === 'fr')
                                    Présent
                                @else
                                    Present
                                @endif
                            @endif
                        </div>
                        @endif
                    </div>
                    @if(!empty($exp['description']))
                    <div class="job-description">{{ $exp['description'] }}</div>
                    @endif
                </div>
                @endif
            @endforeach
        </div>
        @endif

        <!-- Key Achievements -->
        @if(!empty($content['key_achievements']) && is_array($content['key_achievements']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    الإنجازات الرئيسية
                @elseif($language === 'fr')
                    Réalisations Clés
                @else
                    Key Achievements
                @endif
            </h2>
            @foreach($content['key_achievements'] as $achievement)
                <div class="achievement-item">{{ $achievement }}</div>
            @endforeach
        </div>
        @endif

        <!-- Core Competencies -->
        @if(!empty($content['core_competencies']) && is_array($content['core_competencies']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    الكفاءات الأساسية
                @elseif($language === 'fr')
                    Compétences Fondamentales
                @else
                    Core Competencies
                @endif
            </h2>
            <div class="competency-list">
                @foreach($content['core_competencies'] as $competency)
                    @if(!empty($competency))
                    <div class="competency-item">{{ $competency }}</div>
                    @endif
                @endforeach
            </div>
        </div>
        @endif

        <!-- Education -->
        @if(!empty($content['education']) && is_array($content['education']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    التعليم
                @elseif($language === 'fr')
                    Éducation
                @else
                    Education
                @endif
            </h2>
            @foreach($content['education'] as $edu)
                @if(!empty($edu['degree']) && !empty($edu['institution']))
                <div class="education-item">
                    <div class="degree">{{ $edu['degree'] }}</div>
                    <div class="institution">{{ $edu['institution'] }}</div>
                    @if(!empty($edu['year']))
                    <div class="year">{{ $edu['year'] }}</div>
                    @endif
                </div>
                @endif
            @endforeach
        </div>
        @endif

        <!-- Technical Skills -->
        @if(!empty($content['skills']) && is_array($content['skills']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    المهارات التقنية
                @elseif($language === 'fr')
                    Compétences Techniques
                @else
                    Technical Skills
                @endif
            </h2>
            <div class="skills-list">
                @foreach($content['skills'] as $skill)
                    @if(!empty($skill))
                    <div class="skill-item">{{ $skill }}</div>
                    @endif
                @endforeach
            </div>
        </div>
        @endif

        <!-- Languages -->
        @if(!empty($content['languages']) && is_array($content['languages']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    اللغات
                @elseif($language === 'fr')
                    Langues
                @else
                    Languages
                @endif
            </h2>
            @foreach($content['languages'] as $lang)
                @if(!empty($lang['language']))
                <div class="language-item">
                    <div class="language-name">{{ $lang['language'] }}</div>
                    @if(!empty($lang['level']))
                    <div class="language-level">{{ $lang['level'] }}</div>
                    @endif
                </div>
                @endif
            @endforeach
        </div>
        @endif
    </div>
</body>
</html>
