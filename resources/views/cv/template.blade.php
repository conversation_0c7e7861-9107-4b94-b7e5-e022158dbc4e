<!DOCTYPE html>
<html lang="{{ $language }}" dir="{{ $isRTL ? 'rtl' : 'ltr' }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CV - {{ $content['full_name'] ?? 'Professional CV' }}</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap');
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: {{ $isRTL ? "'Noto Sans Arabic', Arial, sans-serif" : "'Inter', Arial, sans-serif" }};
            line-height: 1.6;
            color: #333;
            background: #fff;
            font-size: 14px;
        }
        
        .cv-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 40px;
            background: white;
        }
        
        .header {
            text-align: {{ $isRTL ? 'right' : 'left' }};
            border-bottom: 3px solid #6366f1;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .name {
            font-size: 32px;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
        }
        
        .contact-info {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            margin-top: 10px;
            {{ $isRTL ? 'direction: rtl;' : '' }}
        }
        
        .contact-item {
            font-size: 13px;
            color: #6b7280;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #6366f1;
            margin-bottom: 15px;
            padding-bottom: 5px;
            border-bottom: 2px solid #e5e7eb;
            text-align: {{ $isRTL ? 'right' : 'left' }};
        }
        
        .summary {
            font-size: 14px;
            line-height: 1.7;
            color: #4b5563;
            text-align: {{ $isRTL ? 'right' : 'justify' }};
        }
        
        .experience-item, .education-item {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #f3f4f6;
        }
        
        .experience-item:last-child, .education-item:last-child {
            border-bottom: none;
        }
        
        .job-title, .degree {
            font-size: 16px;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 5px;
        }
        
        .company, .institution {
            font-size: 14px;
            font-weight: 500;
            color: #6366f1;
            margin-bottom: 5px;
        }
        
        .date-range {
            font-size: 12px;
            color: #6b7280;
            margin-bottom: 10px;
            font-style: italic;
        }
        
        .description {
            font-size: 13px;
            line-height: 1.6;
            color: #4b5563;
            text-align: {{ $isRTL ? 'right' : 'justify' }};
        }
        
        .skills-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 10px;
        }
        
        .skill-item {
            background: #f8fafc;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 13px;
            color: #374151;
            border-left: 3px solid #6366f1;
            {{ $isRTL ? 'border-right: 3px solid #6366f1; border-left: none;' : '' }}
        }
        
        .languages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 10px;
        }
        
        .language-item {
            text-align: {{ $isRTL ? 'right' : 'left' }};
        }
        
        .language-name {
            font-weight: 500;
            color: #1f2937;
            font-size: 14px;
        }
        
        .language-level {
            font-size: 12px;
            color: #6b7280;
            margin-top: 2px;
        }
        
        .two-column {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        
        @media print {
            body { print-color-adjust: exact; }
            .cv-container { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="cv-container">
        <!-- Header -->
        <div class="header">
            <h1 class="name">{{ $content['full_name'] ?? '' }}</h1>
            <div class="contact-info">
                @if(!empty($content['email']))
                    <div class="contact-item">📧 {{ $content['email'] }}</div>
                @endif
                @if(!empty($content['phone']))
                    <div class="contact-item">📱 {{ $content['phone'] }}</div>
                @endif
                @if(!empty($content['address']))
                    <div class="contact-item">📍 {{ $content['address'] }}</div>
                @endif
            </div>
        </div>

        <!-- Professional Summary -->
        @if(!empty($content['professional_summary']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    الملخص المهني
                @elseif($language === 'fr')
                    Résumé Professionnel
                @else
                    Professional Summary
                @endif
            </h2>
            <div class="summary">{{ $content['professional_summary'] }}</div>
        </div>
        @endif

        <!-- Experience -->
        @if(!empty($content['experience']) && is_array($content['experience']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    الخبرة العملية
                @elseif($language === 'fr')
                    Expérience Professionnelle
                @else
                    Work Experience
                @endif
            </h2>
            @foreach($content['experience'] as $exp)
                @if(!empty($exp['job_title']) && !empty($exp['company']))
                <div class="experience-item">
                    <div class="job-title">{{ $exp['job_title'] }}</div>
                    <div class="company">{{ $exp['company'] }}</div>
                    @if(!empty($exp['start_date']) || !empty($exp['end_date']))
                    <div class="date-range">
                        {{ $exp['start_date'] ?? '' }}
                        @if(!empty($exp['start_date']) && !empty($exp['end_date'])) - @endif
                        {{ $exp['end_date'] ?? '' }}
                    </div>
                    @endif
                    @if(!empty($exp['description']))
                    <div class="description">{{ $exp['description'] }}</div>
                    @endif
                </div>
                @endif
            @endforeach
        </div>
        @endif

        <!-- Education -->
        @if(!empty($content['education']) && is_array($content['education']))
        <div class="section">
            <h2 class="section-title">
                @if($language === 'ar')
                    التعليم
                @elseif($language === 'fr')
                    Formation
                @else
                    Education
                @endif
            </h2>
            @foreach($content['education'] as $edu)
                @if(!empty($edu['degree']) && !empty($edu['institution']))
                <div class="education-item">
                    <div class="degree">{{ $edu['degree'] }}</div>
                    <div class="institution">{{ $edu['institution'] }}</div>
                    @if(!empty($edu['year']))
                    <div class="date-range">{{ $edu['year'] }}</div>
                    @endif
                </div>
                @endif
            @endforeach
        </div>
        @endif

        <div class="two-column">
            <!-- Skills -->
            @if(!empty($content['skills']) && is_array($content['skills']))
            <div class="section">
                <h2 class="section-title">
                    @if($language === 'ar')
                        المهارات
                    @elseif($language === 'fr')
                        Compétences
                    @else
                        Skills
                    @endif
                </h2>
                <div class="skills-grid">
                    @foreach($content['skills'] as $skill)
                        @if(!empty($skill))
                        <div class="skill-item">{{ $skill }}</div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif

            <!-- Languages -->
            @if(!empty($content['languages']) && is_array($content['languages']))
            <div class="section">
                <h2 class="section-title">
                    @if($language === 'ar')
                        اللغات
                    @elseif($language === 'fr')
                        Langues
                    @else
                        Languages
                    @endif
                </h2>
                <div class="languages-grid">
                    @foreach($content['languages'] as $lang)
                        @if(!empty($lang['language']))
                        <div class="language-item">
                            <div class="language-name">{{ $lang['language'] }}</div>
                            @if(!empty($lang['level']))
                            <div class="language-level">{{ $lang['level'] }}</div>
                            @endif
                        </div>
                        @endif
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
</body>
</html>
