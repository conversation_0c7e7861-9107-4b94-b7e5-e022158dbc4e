@extends('layouts.app')

@section('title', __('messages.career_guidance') . ' - MonOri AI')
@section('description', 'احصل على توصيات مهنية مخصصة باستخدام الذكاء الاصطناعي المتقدم')

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-green-50 via-white to-teal-50 dark:from-gray-900 dark:via-gray-800 dark:to-teal-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                <span class="gradient-text">{{ __('messages.career_guidance') }}</span>
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                احصل على توصيات مهنية مخصصة تناسب شخصيتك ومهاراتك وأهدافك المستقبلية
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="startCareerAssessment()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2m4 6h.01M5 20h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                    <span>ابدأ تقييم مهني شامل</span>
                </button>
                <button onclick="exploreCareerPaths()" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-green-500 dark:hover:border-green-400 transition-all duration-300">
                    استكشف المسارات المهنية
                </button>
            </div>
        </div>
    </div>
</section>

<!-- Assessment Form -->
<section id="assessment-form" class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">تقييم مهني شامل</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">أجب على الأسئلة التالية للحصول على توصيات مهنية مخصصة</p>
        </div>

        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
            <form id="career-assessment-form" class="space-y-8">
                <!-- Personal Information -->
                <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">المعلومات الشخصية</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">العمر</label>
                            <select class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">اختر عمرك</option>
                                <option value="16-20">16-20 سنة</option>
                                <option value="21-25">21-25 سنة</option>
                                <option value="26-30">26-30 سنة</option>
                                <option value="31-35">31-35 سنة</option>
                                <option value="36+">36+ سنة</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المستوى التعليمي</label>
                            <select class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                                <option value="">اختر مستواك التعليمي</option>
                                <option value="high-school">ثانوية عامة</option>
                                <option value="diploma">دبلوم</option>
                                <option value="bachelor">بكالوريوس</option>
                                <option value="master">ماجستير</option>
                                <option value="phd">دكتوراه</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Interests -->
                <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">الاهتمامات والميول</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">اختر المجالات التي تثير اهتمامك (يمكنك اختيار أكثر من مجال):</p>
                    <div class="grid grid-cols-2 md:grid-cols-3 gap-4">
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" value="technology" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">التكنولوجيا</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" value="healthcare" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">الصحة</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" value="education" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">التعليم</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" value="business" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">الأعمال</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" value="arts" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">الفنون</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="checkbox" value="science" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">العلوم</span>
                        </label>
                    </div>
                </div>

                <!-- Skills -->
                <div class="border-b border-gray-200 dark:border-gray-700 pb-8">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">المهارات والقدرات</h3>
                    <p class="text-gray-600 dark:text-gray-300 mb-4">قيم مستوى مهاراتك في المجالات التالية:</p>
                    <div class="space-y-4">
                        <div class="skill-rating">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التواصل والعرض</label>
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                <span class="text-sm text-gray-500">ضعيف</span>
                                <div class="flex space-x-1 rtl:space-x-reverse">
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="communication" data-rating="1">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="communication" data-rating="2">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="communication" data-rating="3">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="communication" data-rating="4">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="communication" data-rating="5">★</button>
                                </div>
                                <span class="text-sm text-gray-500">ممتاز</span>
                            </div>
                        </div>

                        <div class="skill-rating">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">التفكير التحليلي</label>
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                <span class="text-sm text-gray-500">ضعيف</span>
                                <div class="flex space-x-1 rtl:space-x-reverse">
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="analytical" data-rating="1">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="analytical" data-rating="2">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="analytical" data-rating="3">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="analytical" data-rating="4">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="analytical" data-rating="5">★</button>
                                </div>
                                <span class="text-sm text-gray-500">ممتاز</span>
                            </div>
                        </div>

                        <div class="skill-rating">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">الإبداع والابتكار</label>
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                <span class="text-sm text-gray-500">ضعيف</span>
                                <div class="flex space-x-1 rtl:space-x-reverse">
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="creativity" data-rating="1">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="creativity" data-rating="2">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="creativity" data-rating="3">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="creativity" data-rating="4">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="creativity" data-rating="5">★</button>
                                </div>
                                <span class="text-sm text-gray-500">ممتاز</span>
                            </div>
                        </div>

                        <div class="skill-rating">
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">القيادة وإدارة الفرق</label>
                            <div class="flex items-center space-x-2 rtl:space-x-reverse">
                                <span class="text-sm text-gray-500">ضعيف</span>
                                <div class="flex space-x-1 rtl:space-x-reverse">
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="leadership" data-rating="1">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="leadership" data-rating="2">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="leadership" data-rating="3">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="leadership" data-rating="4">★</button>
                                    <button type="button" class="skill-star w-8 h-8 text-gray-300 hover:text-yellow-400" data-skill="leadership" data-rating="5">★</button>
                                </div>
                                <span class="text-sm text-gray-500">ممتاز</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Goals -->
                <div>
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">الأهداف المهنية</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ما هو هدفك المهني الرئيسي؟</label>
                            <textarea rows="3" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-gray-700 dark:text-white" placeholder="اكتب هدفك المهني..."></textarea>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">ما هي أولوياتك في العمل؟</label>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="salary" class="mr-2 rtl:ml-2 rtl:mr-0">
                                    <span class="text-gray-700 dark:text-gray-300">راتب عالي</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="balance" class="mr-2 rtl:ml-2 rtl:mr-0">
                                    <span class="text-gray-700 dark:text-gray-300">توازن العمل والحياة</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="growth" class="mr-2 rtl:ml-2 rtl:mr-0">
                                    <span class="text-gray-700 dark:text-gray-300">النمو المهني</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="priority" value="impact" class="mr-2 rtl:ml-2 rtl:mr-0">
                                    <span class="text-gray-700 dark:text-gray-300">التأثير الاجتماعي</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Submit Button -->
                <div class="text-center pt-8">
                    <button type="submit" class="btn-primary text-white px-12 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        <span>احصل على توصياتك المهنية</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Career Recommendations Section -->
<section id="career-recommendations" class="hidden py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">توصياتك المهنية المخصصة</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">بناءً على تحليل ملفك الشخصي، إليك أفضل المسارات المهنية المناسبة لك</p>
        </div>

        <!-- Top Recommendations -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
            <!-- Recommendation 1 -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 border-l-4 border-green-500">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mr-4 rtl:ml-4 rtl:mr-0">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">مطور تطبيقات</h3>
                        <div class="flex items-center">
                            <span class="text-green-500 font-semibold">95% توافق</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    مناسب جداً لشخصيتك التحليلية وحبك للتكنولوجيا. راتب متوقع: 8,000-15,000 درهم
                </p>
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        نمو مهني سريع
                    </div>
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        طلب عالي في السوق
                    </div>
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        إمكانية العمل عن بُعد
                    </div>
                </div>
                <button class="w-full bg-green-500 text-white py-3 rounded-lg font-semibold hover:bg-green-600 transition-colors duration-200">
                    تفاصيل أكثر
                </button>
            </div>

            <!-- Recommendation 2 -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 border-l-4 border-blue-500">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mr-4 rtl:ml-4 rtl:mr-0">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">مدير مشاريع</h3>
                        <div class="flex items-center">
                            <span class="text-blue-500 font-semibold">88% توافق</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    يناسب مهاراتك القيادية وقدرتك على التنظيم. راتب متوقع: 10,000-18,000 درهم
                </p>
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        مسؤوليات متنوعة
                    </div>
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        فرص قيادية
                    </div>
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        تطوير مهارات إدارية
                    </div>
                </div>
                <button class="w-full bg-blue-500 text-white py-3 rounded-lg font-semibold hover:bg-blue-600 transition-colors duration-200">
                    تفاصيل أكثر
                </button>
            </div>

            <!-- Recommendation 3 -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 border-l-4 border-purple-500">
                <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mr-4 rtl:ml-4 rtl:mr-0">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">مصمم UX/UI</h3>
                        <div class="flex items-center">
                            <span class="text-purple-500 font-semibold">82% توافق</span>
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 dark:text-gray-300 mb-4">
                    يجمع بين إبداعك وحبك للتكنولوجيا. راتب متوقع: 7,000-14,000 درهم
                </p>
                <div class="space-y-2 mb-4">
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        مجال إبداعي
                    </div>
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        تأثير مباشر على المستخدمين
                    </div>
                    <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                        <svg class="w-4 h-4 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        مجال متنامي
                    </div>
                </div>
                <button class="w-full bg-purple-500 text-white py-3 rounded-lg font-semibold hover:bg-purple-600 transition-colors duration-200">
                    تفاصيل أكثر
                </button>
            </div>
        </div>

        <!-- Action Plan -->
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">خطة العمل المقترحة</h3>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">1</span>
                    </div>
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">تطوير المهارات</h4>
                    <p class="text-gray-600 dark:text-gray-300">ابدأ بتعلم المهارات الأساسية للمجال المختار</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">2</span>
                    </div>
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">بناء المحفظة</h4>
                    <p class="text-gray-600 dark:text-gray-300">أنشئ مشاريع تطبيقية لعرض مهاراتك</p>
                </div>
                <div class="text-center">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white">3</span>
                    </div>
                    <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-2">التقديم للوظائف</h4>
                    <p class="text-gray-600 dark:text-gray-300">ابحث عن الفرص المناسبة وقدم طلباتك</p>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
// Career Assessment Functions
function startCareerAssessment() {
    document.getElementById('assessment-form').scrollIntoView({ behavior: 'smooth' });
}

function exploreCareerPaths() {
    // Show sample recommendations
    document.getElementById('career-recommendations').classList.remove('hidden');
    document.getElementById('career-recommendations').scrollIntoView({ behavior: 'smooth' });
}

// Skill Rating System
document.addEventListener('DOMContentLoaded', function() {
    const skillStars = document.querySelectorAll('.skill-star');

    skillStars.forEach(star => {
        star.addEventListener('click', function() {
            const skill = this.dataset.skill;
            const rating = parseInt(this.dataset.rating);

            // Reset all stars for this skill
            const skillStars = document.querySelectorAll(`[data-skill="${skill}"]`);
            skillStars.forEach(s => {
                s.classList.remove('text-yellow-400');
                s.classList.add('text-gray-300');
            });

            // Fill stars up to the selected rating
            for (let i = 0; i < rating; i++) {
                skillStars[i].classList.remove('text-gray-300');
                skillStars[i].classList.add('text-yellow-400');
            }
        });

        star.addEventListener('mouseenter', function() {
            const skill = this.dataset.skill;
            const rating = parseInt(this.dataset.rating);

            // Highlight stars on hover
            const skillStars = document.querySelectorAll(`[data-skill="${skill}"]`);
            skillStars.forEach((s, index) => {
                if (index < rating) {
                    s.classList.add('text-yellow-300');
                }
            });
        });

        star.addEventListener('mouseleave', function() {
            const skill = this.dataset.skill;

            // Remove hover effect
            const skillStars = document.querySelectorAll(`[data-skill="${skill}"]`);
            skillStars.forEach(s => {
                s.classList.remove('text-yellow-300');
            });
        });
    });

    // Form submission
    document.getElementById('career-assessment-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // Show loading
        showAssessmentLoading();

        // Simulate AI processing
        setTimeout(() => {
            hideAssessmentLoading();
            showCareerRecommendations();
        }, 3000);
    });
});

function showAssessmentLoading() {
    const loadingHTML = `
        <div id="assessment-loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="animate-spin w-16 h-16 border-4 border-green-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">جاري تحليل ملفك المهني</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">الذكاء الاصطناعي يحلل إجاباتك ويبحث عن أفضل المسارات المهنية...</p>
                <div class="text-sm text-gray-500 dark:text-gray-400" id="assessment-step">تحليل الاهتمامات والمهارات...</div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loadingHTML);

    // Simulate analysis steps
    const steps = [
        "تحليل الاهتمامات والمهارات...",
        "مقارنة مع قاعدة بيانات الوظائف...",
        "تقييم التوافق مع المسارات المهنية...",
        "إنشاء التوصيات المخصصة..."
    ];

    let stepIndex = 0;
    const stepInterval = setInterval(() => {
        stepIndex++;
        if (stepIndex < steps.length) {
            document.getElementById('assessment-step').textContent = steps[stepIndex];
        } else {
            clearInterval(stepInterval);
        }
    }, 750);
}

function hideAssessmentLoading() {
    const loadingElement = document.getElementById('assessment-loading');
    if (loadingElement) {
        loadingElement.remove();
    }
}

function showCareerRecommendations() {
    document.getElementById('career-recommendations').classList.remove('hidden');
    document.getElementById('career-recommendations').scrollIntoView({ behavior: 'smooth' });
}
</script>
@endpush
@endsection
