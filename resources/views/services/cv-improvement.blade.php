@extends('layouts.app')

@section('title', __('messages.cv_improvement') . ' - MonOri AI')
@section('description', __('messages.cv_improvement_description'))

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-orange-50 via-white to-red-50 dark:from-gray-900 dark:via-gray-800 dark:to-red-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                <span class="gradient-text">{{ __('messages.cv_improvement') }}</span>
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                {{ __('messages.cv_improvement_hero_description') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="uploadCV()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <span>{{ __('messages.upload_cv') }}</span>
                </button>
                <button onclick="createNewCV()" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-orange-500 dark:hover:border-orange-400 transition-all duration-300">
                    {{ __('messages.create_new_cv') }}
                </button>
                <form action="{{ route('services.demo.cv-improvement') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-colors">
                        🎯 {{ __('messages.try_demo') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Upload Section -->
<section id="upload-section" class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.upload_cv_for_analysis') }}</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">{{ __('messages.supported_file_formats') }}</p>
        </div>

        <!-- Upload Area -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
            <div id="upload-area" class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl p-12 text-center hover:border-orange-500 dark:hover:border-orange-400 transition-colors duration-200 cursor-pointer">
                <div class="mb-4">
                    <svg class="w-16 h-16 text-gray-400 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">اسحب وأفلت ملف السيرة الذاتية هنا</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">أو انقر للاختيار من جهازك</p>
                <input type="file" id="cv-file" accept=".pdf,.doc,.docx" class="hidden">
                <button onclick="document.getElementById('cv-file').click()" class="bg-orange-500 text-white px-6 py-3 rounded-lg font-semibold hover:bg-orange-600 transition-colors duration-200">
                    اختيار ملف
                </button>
            </div>
            
            <!-- File Info -->
            <div id="file-info" class="hidden mt-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <svg class="w-8 h-8 text-orange-500 mr-3 rtl:ml-3 rtl:mr-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <div>
                            <p class="font-semibold text-gray-900 dark:text-white" id="file-name">CV.pdf</p>
                            <p class="text-sm text-gray-500 dark:text-gray-400" id="file-size">2.5 MB</p>
                        </div>
                    </div>
                    <button onclick="removeFile()" class="text-red-500 hover:text-red-700">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Analyze Button -->
            <div id="analyze-section" class="hidden text-center mt-8">
                <button onclick="analyzeCV()" class="btn-primary text-white px-12 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    <span>تحليل السيرة الذاتية بالذكاء الاصطناعي</span>
                </button>
            </div>
        </div>
    </div>
</section>

<!-- CV Analysis Results -->
<section id="analysis-results" class="hidden py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">نتائج تحليل سيرتك الذاتية</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">تحليل شامل مدعوم بالذكاء الاصطناعي</p>
        </div>

        <!-- Overall Score -->
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 mb-8">
            <div class="text-center mb-8">
                <div class="relative w-32 h-32 mx-auto mb-4">
                    <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-300 dark:text-gray-600" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        <path class="text-orange-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="75, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-3xl font-bold text-gray-900 dark:text-white">75%</span>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">النتيجة الإجمالية</h3>
                <p class="text-gray-600 dark:text-gray-300">سيرة ذاتية جيدة مع إمكانيات للتحسين</p>
            </div>
        </div>

        <!-- Detailed Analysis -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Strengths -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    نقاط القوة
                </h3>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">تنسيق واضح ومنظم</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">خبرة عملية متنوعة</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">مهارات تقنية محدثة</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">معلومات الاتصال كاملة</span>
                    </li>
                </ul>
            </div>

            <!-- Areas for Improvement -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    مجالات التحسين
                </h3>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">إضافة ملخص مهني قوي</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">تحسين وصف الإنجازات</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">إضافة كلمات مفتاحية للمجال</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">تحسين ترتيب الأقسام</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="generateImprovedCV()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                    <span>إنشاء نسخة محسنة</span>
                </button>
                <button onclick="downloadReport()" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-orange-500 dark:hover:border-orange-400 transition-all duration-300 inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>تحميل تقرير التحليل</span>
                </button>
                <button onclick="analyzeAnother()" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200">
                    تحليل سيرة ذاتية أخرى
                </button>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
// CV Improvement Functions
let uploadedFile = null;

function uploadCV() {
    document.getElementById('upload-section').scrollIntoView({ behavior: 'smooth' });
}

function createNewCV() {
    // Redirect to CV builder create page
    window.location.href = '{{ route("cv.create") }}';
}

// File Upload Handling
document.addEventListener('DOMContentLoaded', function() {
    const uploadArea = document.getElementById('upload-area');
    const fileInput = document.getElementById('cv-file');
    const fileInfo = document.getElementById('file-info');
    const analyzeSection = document.getElementById('analyze-section');

    // Drag and drop functionality
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.classList.add('border-orange-500', 'bg-orange-50', 'dark:bg-orange-900/20');
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-orange-500', 'bg-orange-50', 'dark:bg-orange-900/20');
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.classList.remove('border-orange-500', 'bg-orange-50', 'dark:bg-orange-900/20');

        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileUpload(files[0]);
        }
    });

    // File input change
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            handleFileUpload(e.target.files[0]);
        }
    });

    // Click to upload
    uploadArea.addEventListener('click', function() {
        fileInput.click();
    });
});

function handleFileUpload(file) {
    // Validate file type
    const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'];
    if (!allowedTypes.includes(file.type)) {
        alert('يرجى رفع ملف بصيغة PDF أو DOC أو DOCX');
        return;
    }

    // Validate file size (max 10MB)
    if (file.size > 10 * 1024 * 1024) {
        alert('حجم الملف كبير جداً. الحد الأقصى 10 ميجابايت');
        return;
    }

    uploadedFile = file;

    // Show file info
    document.getElementById('file-name').textContent = file.name;
    document.getElementById('file-size').textContent = formatFileSize(file.size);
    document.getElementById('file-info').classList.remove('hidden');
    document.getElementById('analyze-section').classList.remove('hidden');
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function removeFile() {
    uploadedFile = null;
    document.getElementById('file-info').classList.add('hidden');
    document.getElementById('analyze-section').classList.add('hidden');
    document.getElementById('cv-file').value = '';
}

function analyzeCV() {
    if (!uploadedFile) {
        alert('يرجى رفع ملف السيرة الذاتية أولاً');
        return;
    }

    // Show loading
    showAnalysisLoading();

    // Simulate AI analysis
    setTimeout(() => {
        hideAnalysisLoading();
        showAnalysisResults();
    }, 4000);
}

function showAnalysisLoading() {
    const loadingHTML = `
        <div id="analysis-loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="animate-spin w-16 h-16 border-4 border-orange-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">جاري تحليل سيرتك الذاتية</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">الذكاء الاصطناعي يحلل محتوى السيرة الذاتية...</p>
                <div class="text-sm text-gray-500 dark:text-gray-400" id="analysis-step">استخراج النصوص والمعلومات...</div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loadingHTML);

    // Simulate analysis steps
    const steps = [
        "استخراج النصوص والمعلومات...",
        "تحليل التنسيق والهيكل...",
        "تقييم المحتوى والكلمات المفتاحية...",
        "مقارنة مع معايير الصناعة...",
        "إنشاء التوصيات والاقتراحات..."
    ];

    let stepIndex = 0;
    const stepInterval = setInterval(() => {
        stepIndex++;
        if (stepIndex < steps.length) {
            document.getElementById('analysis-step').textContent = steps[stepIndex];
        } else {
            clearInterval(stepInterval);
        }
    }, 800);
}

function hideAnalysisLoading() {
    const loadingElement = document.getElementById('analysis-loading');
    if (loadingElement) {
        loadingElement.remove();
    }
}

function showAnalysisResults() {
    document.getElementById('analysis-results').classList.remove('hidden');
    document.getElementById('analysis-results').scrollIntoView({ behavior: 'smooth' });
}

function generateImprovedCV() {
    // Show loading for CV generation
    showCVGenerationLoading();

    // Simulate CV generation
    setTimeout(() => {
        hideCVGenerationLoading();
        alert('تم إنشاء النسخة المحسنة من سيرتك الذاتية! سيتم تحميلها قريباً.');
    }, 3000);
}

function showCVGenerationLoading() {
    const loadingHTML = `
        <div id="cv-generation-loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="animate-pulse w-16 h-16 bg-orange-500 rounded-xl mx-auto mb-4 flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">جاري إنشاء النسخة المحسنة</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">الذكاء الاصطناعي ينشئ سيرة ذاتية محسنة...</p>
                <div class="text-sm text-gray-500 dark:text-gray-400" id="generation-step">تطبيق التحسينات المقترحة...</div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loadingHTML);

    // Simulate generation steps
    const steps = [
        "تطبيق التحسينات المقترحة...",
        "إعادة تنسيق المحتوى...",
        "إضافة الكلمات المفتاحية...",
        "تحسين التصميم والألوان...",
        "إنشاء ملف PDF النهائي..."
    ];

    let stepIndex = 0;
    const stepInterval = setInterval(() => {
        stepIndex++;
        if (stepIndex < steps.length) {
            document.getElementById('generation-step').textContent = steps[stepIndex];
        } else {
            clearInterval(stepInterval);
        }
    }, 600);
}

function hideCVGenerationLoading() {
    const loadingElement = document.getElementById('cv-generation-loading');
    if (loadingElement) {
        loadingElement.remove();
    }
}

function downloadReport() {
    // Simulate report download
    alert('سيتم تحميل تقرير التحليل الكامل قريباً!');
}

function analyzeAnother() {
    // Reset the form
    removeFile();
    document.getElementById('analysis-results').classList.add('hidden');
    document.getElementById('upload-section').scrollIntoView({ behavior: 'smooth' });
}
</script>
@endpush
@endsection
