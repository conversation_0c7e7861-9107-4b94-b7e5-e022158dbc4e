@extends('layouts.app')

@section('title', __('messages.cv_improvement_title'))
@section('description', __('messages.cv_improvement_description'))

@push('styles')
<style>
    .service-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        position: relative;
        overflow: hidden;
    }

    .service-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.1);
    }

    .level-card {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
        border: 3px solid transparent;
        position: relative;
        overflow: hidden;
    }

    .level-card:hover {
        transform: translateY(-10px);
        box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    }

    .level-card.recommended {
        border-color: #f59e0b;
        transform: scale(1.05);
    }

    .level-card.recommended::before {
        content: 'Recommended';
        position: absolute;
        top: 0;
        right: 0;
        background: linear-gradient(135deg, #f59e0b, #ef4444);
        color: white;
        padding: 0.5rem 1.5rem;
        font-size: 0.8rem;
        font-weight: 600;
        border-radius: 0 20px 0 20px;
    }

    .price-tag {
        font-size: 2.5rem;
        font-weight: 800;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }

    .feature-list {
        list-style: none;
        padding: 0;
    }

    .feature-list li {
        padding: 0.5rem 0;
        display: flex;
        align-items: center;
    }

    .feature-list li::before {
        content: '✓';
        color: #10b981;
        font-weight: bold;
        margin-right: 0.5rem;
        width: 20px;
        height: 20px;
        background: rgba(16, 185, 129, 0.1);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.8rem;
    }

    .cta-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        text-decoration: none;
        display: inline-block;
        transition: all 0.3s ease;
        border: none;
        cursor: pointer;
        width: 100%;
        text-align: center;
    }

    .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        color: white;
    }

    .cta-button.free {
        background: linear-gradient(135deg, #10b981, #059669);
    }

    .cta-button.premium {
        background: linear-gradient(135deg, #f59e0b, #ef4444);
    }

    @media (max-width: 768px) {
        .level-card.recommended {
            transform: none;
        }
    }
</style>
@endpush

@section('content')
<!-- Hero Section -->
<section class="service-hero py-20">
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h1 class="text-4xl md:text-6xl font-bold text-white mb-6">
            AI-Powered CV Improvement
        </h1>
        <p class="text-xl text-blue-100 mb-8 max-w-3xl mx-auto">
            Transform your CV with intelligent AI optimization across multiple professional levels
        </p>
        
        <!-- AI Features -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-12">
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">AI-Powered Enhancement</h3>
                <p class="text-blue-100 text-sm">Advanced AI algorithms optimize your content for maximum impact</p>
            </div>
            
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Instant PDF Generation</h3>
                <p class="text-blue-100 text-sm">Get your professionally formatted CV as a PDF instantly</p>
            </div>
            
            <div class="bg-white/10 backdrop-blur-lg rounded-2xl p-6">
                <div class="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-semibold text-white mb-2">Multiple Service Levels</h3>
                <p class="text-blue-100 text-sm">Choose from Basic, Professional, or Premium AI enhancement</p>
            </div>
        </div>
    </div>
</section>

<!-- Service Levels -->
<section class="py-20 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Choose Your Enhancement Level
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                Select the perfect AI enhancement level for your career goals
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- Basic Level -->
            <div class="level-card">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Basic Level</h3>
                    <div class="price-tag">FREE</div>
                    <p class="text-gray-600 mt-2">Perfect for getting started with CV creation</p>
                </div>
                
                <ul class="feature-list mb-8">
                    <li>Basic CV creation and formatting</li>
                    <li>Standard templates</li>
                    <li>PDF download</li>
                    <li>Basic AI suggestions</li>
                </ul>
                
                <a href="{{ route('cv-improvement.basic') }}" class="cta-button free">
                    Start Free
                </a>
            </div>

            <!-- Professional Level -->
            <div class="level-card recommended">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Professional Level</h3>
                    <div class="price-tag">50 DH</div>
                    <p class="text-gray-600 mt-2">Advanced AI optimization for career growth</p>
                </div>
                
                <ul class="feature-list mb-8">
                    <li>AI content enhancement</li>
                    <li>Professional templates</li>
                    <li>Keyword optimization</li>
                    <li>Achievements section</li>
                    <li>Industry-specific optimization</li>
                </ul>
                
                <button onclick="orderService('cv_improvement_professional')" class="cta-button">
                    Upgrade Now
                </button>
            </div>

            <!-- Premium Level -->
            <div class="level-card">
                <div class="text-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 mb-2">Premium Level</h3>
                    <div class="price-tag">100 DH</div>
                    <p class="text-gray-600 mt-2">Ultimate AI-powered career optimization</p>
                </div>
                
                <ul class="feature-list mb-8">
                    <li>Advanced AI optimization</li>
                    <li>Premium templates</li>
                    <li>Career recommendations</li>
                    <li>Certification suggestions</li>
                    <li>ATS optimization</li>
                    <li>Personal consultation</li>
                </ul>
                
                <button onclick="orderService('cv_improvement_premium')" class="cta-button premium">
                    Get Premium
                </button>
            </div>
        </div>
    </div>
</section>
@endsection

@push('scripts')
<script>
function orderService(serviceType) {
    @auth
        // Redirect to payment page
        window.location.href = `/payment/service?service=${serviceType}`;
    @else
        // Redirect to login
        window.location.href = '{{ route("login") }}';
    @endauth
}
</script>
@endpush
