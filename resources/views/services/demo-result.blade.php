@extends('layouts.app')

@section('title', $title . ' - <PERSON><PERSON><PERSON> AI')

@section('content')
<!-- Header -->
<section class="py-12 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">{{ $title }}</h1>
            @if(isset($is_demo) && $is_demo)
                <div class="bg-yellow-500/20 text-white px-6 py-3 rounded-lg inline-block mb-4">
                    <i class="fas fa-star mr-2"></i>
                    {{ __('messages.demo_results') }}
                </div>
            @endif
            @if(isset($results['score']))
                <div class="mt-4">
                    <span class="bg-white/20 text-white px-4 py-2 rounded-full text-sm">
                        {{ __('messages.service_score') }}: {{ $results['score'] }}%
                    </span>
                </div>
            @endif
        </div>
    </div>
</section>

<!-- Results Section -->
<section class="py-12 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        @if($service_type === 'personality_analysis')
            <!-- Personality Analysis Results -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-user-circle text-blue-500 mr-3"></i>
                    {{ __('messages.personality_type') }}
                </h2>
                <p class="text-lg text-gray-700 dark:text-gray-300 mb-6">
                    {{ $results['personality_type'] ?? 'N/A' }}
                </p>
                
                @if(isset($results['strengths']) && is_array($results['strengths']))
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        {{ __('messages.strengths') }}
                    </h3>
                    <ul class="space-y-2 mb-6">
                        @foreach($results['strengths'] as $strength)
                            <li class="flex items-start">
                                <i class="fas fa-check-circle text-green-500 mt-1 mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">{{ $strength }}</span>
                            </li>
                        @endforeach
                    </ul>
                @endif

                @if(isset($results['recommendations']) && is_array($results['recommendations']))
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        {{ __('messages.recommendations') }}
                    </h3>
                    <ul class="space-y-2">
                        @foreach($results['recommendations'] as $recommendation)
                            <li class="flex items-start">
                                <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">{{ $recommendation }}</span>
                            </li>
                        @endforeach
                    </ul>
                @endif
            </div>

        @elseif($service_type === 'cv_improvement')
            <!-- CV Improvement Results -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-file-alt text-green-500 mr-3"></i>
                    {{ __('messages.cv_improvements') }}
                </h2>
                
                @if(isset($results['improvements']) && is_array($results['improvements']))
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        {{ __('messages.improvements') }}
                    </h3>
                    <ul class="space-y-2 mb-6">
                        @foreach($results['improvements'] as $improvement)
                            <li class="flex items-start">
                                <i class="fas fa-arrow-up text-blue-500 mt-1 mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">{{ $improvement }}</span>
                            </li>
                        @endforeach
                    </ul>
                @endif

                @if(isset($results['suggestions']) && is_array($results['suggestions']))
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        {{ __('messages.suggestions') }}
                    </h3>
                    <ul class="space-y-2">
                        @foreach($results['suggestions'] as $suggestion)
                            <li class="flex items-start">
                                <i class="fas fa-star text-yellow-500 mt-1 mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">{{ $suggestion }}</span>
                            </li>
                        @endforeach
                    </ul>
                @endif
            </div>

        @elseif($service_type === 'interview_simulation')
            <!-- Interview Simulation Results -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                    <i class="fas fa-comments text-purple-500 mr-3"></i>
                    {{ __('messages.interview_feedback') }}
                </h2>
                
                @if(isset($results['feedback']) && is_array($results['feedback']))
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        {{ __('messages.feedback') }}
                    </h3>
                    <ul class="space-y-2 mb-6">
                        @foreach($results['feedback'] as $feedback)
                            <li class="flex items-start">
                                <i class="fas fa-comment text-blue-500 mt-1 mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">{{ $feedback }}</span>
                            </li>
                        @endforeach
                    </ul>
                @endif

                @if(isset($results['tips']) && is_array($results['tips']))
                    <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">
                        {{ __('messages.tips') }}
                    </h3>
                    <ul class="space-y-2">
                        @foreach($results['tips'] as $tip)
                            <li class="flex items-start">
                                <i class="fas fa-lightbulb text-yellow-500 mt-1 mr-3"></i>
                                <span class="text-gray-700 dark:text-gray-300">{{ $tip }}</span>
                            </li>
                        @endforeach
                    </ul>
                @endif
            </div>
        @endif

        <!-- Login Prompt for Demo Users -->
        @if(isset($show_login_prompt) && $show_login_prompt)
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl p-8 text-center text-white">
                <h3 class="text-2xl font-bold mb-4">{{ __('messages.unlock_full_features') }}</h3>
                <p class="text-lg mb-6">{{ __('messages.demo_limitation_message') }}</p>
                <div class="space-x-4 rtl:space-x-reverse">
                    <a href="{{ route('register') }}" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        {{ __('messages.sign_up_now') }}
                    </a>
                    <a href="{{ route('login') }}" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        {{ __('messages.login') }}
                    </a>
                </div>
            </div>
        @endif

        <!-- Action Buttons -->
        <div class="mt-8 text-center space-x-4 rtl:space-x-reverse">
            <a href="{{ route('services') }}" class="btn-secondary">
                {{ __('messages.back_to_services') }}
            </a>
            @auth
                <a href="{{ route('ai-service.history') }}" class="btn-primary">
                    {{ __('messages.view_history') }}
                </a>
            @endauth
        </div>
    </div>
</section>
@endsection
