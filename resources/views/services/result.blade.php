@extends('layouts.app')

@section('title', $result->title . ' - <PERSON><PERSON>ri AI')
@section('description', $result->description)

@section('content')
<!-- Header -->
<section class="py-12 bg-gradient-to-br from-blue-600 via-purple-600 to-pink-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">{{ $result->title }}</h1>
            <p class="text-xl text-white/90">{{ $result->description }}</p>
            <div class="mt-4 flex items-center justify-center space-x-4 rtl:space-x-reverse">
                <span class="bg-white/20 text-white px-4 py-2 rounded-full text-sm">
                    {{ __('messages.service_date') }}: {{ $result->created_at->format('Y-m-d') }}
                </span>
                @if($result->score)
                    <span class="bg-white/20 text-white px-4 py-2 rounded-full text-sm">
                        {{ __('messages.service_score') }}: {{ $result->score }}%
                    </span>
                @endif
            </div>
        </div>
    </div>
</section>

<!-- Results Section -->
<section class="py-12 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        
        @if($result->service_type === 'personality_analysis')
            <!-- Personality Analysis Results -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.analysis_results') }}</h2>
                
                @if(isset($result->results['personality_type']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.personality_type') }}</h3>
                        <p class="text-gray-600 dark:text-gray-300 bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                            {{ $result->results['personality_type'] }}
                        </p>
                    </div>
                @endif

                @if(isset($result->results['strengths']) && is_array($result->results['strengths']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.strengths') }}</h3>
                        <ul class="list-disc list-inside space-y-2">
                            @foreach($result->results['strengths'] as $strength)
                                <li class="text-gray-600 dark:text-gray-300">{{ $strength }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(isset($result->results['recommendations']) && is_array($result->results['recommendations']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.recommendations') }}</h3>
                        <ul class="list-disc list-inside space-y-2">
                            @foreach($result->results['recommendations'] as $recommendation)
                                <li class="text-gray-600 dark:text-gray-300">{{ $recommendation }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(isset($result->results['career_suggestions']) && is_array($result->results['career_suggestions']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.career_suggestions') }}</h3>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                            @foreach($result->results['career_suggestions'] as $suggestion)
                                <span class="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300 px-3 py-2 rounded-lg text-sm text-center">
                                    {{ $suggestion }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif

                @if(isset($result->results['summary']))
                    <div class="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.summary') }}</h3>
                        <p class="text-gray-600 dark:text-gray-300">{{ $result->results['summary'] }}</p>
                    </div>
                @endif
            </div>

        @elseif($result->service_type === 'cv_improvement')
            <!-- CV Improvement Results -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.improvement_results') }}</h2>
                
                @if(isset($result->results['overall_score']))
                    <div class="mb-6">
                        <div class="flex items-center justify-between mb-2">
                            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ __('messages.overall_score') }}</h3>
                            <span class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $result->results['overall_score'] }}%</span>
                        </div>
                        <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                            <div class="bg-blue-500 h-3 rounded-full" style="width: {{ $result->results['overall_score'] }}%"></div>
                        </div>
                    </div>
                @endif

                @if(isset($result->results['improvements']) && is_array($result->results['improvements']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.improvements') }}</h3>
                        <ul class="list-disc list-inside space-y-2">
                            @foreach($result->results['improvements'] as $improvement)
                                <li class="text-gray-600 dark:text-gray-300">{{ $improvement }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(isset($result->results['missing_sections']) && is_array($result->results['missing_sections']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.missing_sections') }}</h3>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                            @foreach($result->results['missing_sections'] as $section)
                                <span class="bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 px-3 py-2 rounded-lg text-sm text-center">
                                    {{ $section }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif

                @if(isset($result->results['keyword_suggestions']) && is_array($result->results['keyword_suggestions']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.keyword_suggestions') }}</h3>
                        <div class="flex flex-wrap gap-2">
                            @foreach($result->results['keyword_suggestions'] as $keyword)
                                <span class="bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-300 px-3 py-1 rounded-full text-sm">
                                    {{ $keyword }}
                                </span>
                            @endforeach
                        </div>
                    </div>
                @endif
            </div>

        @elseif($result->service_type === 'interview_simulation')
            <!-- Interview Simulation Results -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
                <h2 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.interview_results') }}</h2>
                
                <!-- Performance Metrics -->
                @if(isset($result->results['overall_performance']))
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                        <div class="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-blue-600 dark:text-blue-400">{{ $result->results['overall_performance'] ?? 0 }}%</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">{{ __('messages.overall_performance') }}</div>
                        </div>
                        <div class="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-green-600 dark:text-green-400">{{ $result->results['communication_skills'] ?? 0 }}%</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">{{ __('messages.communication_skills') }}</div>
                        </div>
                        <div class="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-purple-600 dark:text-purple-400">{{ $result->results['technical_knowledge'] ?? 0 }}%</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">{{ __('messages.technical_knowledge') }}</div>
                        </div>
                        <div class="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg text-center">
                            <div class="text-2xl font-bold text-orange-600 dark:text-orange-400">{{ $result->results['confidence_level'] ?? 0 }}%</div>
                            <div class="text-sm text-gray-600 dark:text-gray-300">{{ __('messages.confidence_level') }}</div>
                        </div>
                    </div>
                @endif

                @if(isset($result->results['strengths']) && is_array($result->results['strengths']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.strengths') }}</h3>
                        <ul class="list-disc list-inside space-y-2">
                            @foreach($result->results['strengths'] as $strength)
                                <li class="text-gray-600 dark:text-gray-300">{{ $strength }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                @if(isset($result->results['areas_for_improvement']) && is_array($result->results['areas_for_improvement']))
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.areas_for_improvement') }}</h3>
                        <ul class="list-disc list-inside space-y-2">
                            @foreach($result->results['areas_for_improvement'] as $area)
                                <li class="text-gray-600 dark:text-gray-300">{{ $area }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif
            </div>
        @endif

        <!-- Actions -->
        <div class="flex justify-center space-x-4 rtl:space-x-reverse">
            <a href="{{ route('profile') }}" class="bg-gray-600 text-white px-6 py-3 rounded-lg hover:bg-gray-700 transition-colors duration-200">
                {{ __('messages.back_to_profile') }}
            </a>
            <a href="{{ route('ai-service.history') }}" class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors duration-200">
                {{ __('messages.view_history') }}
            </a>
        </div>
    </div>
</section>
@endsection
