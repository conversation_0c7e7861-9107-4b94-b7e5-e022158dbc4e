@extends('layouts.app')

@section('title', __('messages.interview_simulation') . ' - MonOri AI')
@section('description', __('messages.interview_simulation_description'))

@section('content')
<!-- Hero Section -->
<section class="py-20 bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-gray-900 dark:via-gray-800 dark:to-pink-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                <span class="gradient-text">{{ __('messages.interview_simulation') }}</span>
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                {{ __('messages.interview_simulation_hero_description') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="startInterview()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <span>{{ __('messages.start_trial_interview') }}</span>
                </button>
                <button onclick="showInterviewTypes()" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-400 transition-all duration-300">
                    {{ __('messages.interview_types') }}
                </button>
                <form action="{{ route('services.demo.interview-simulation') }}" method="POST" class="inline">
                    @csrf
                    <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white px-8 py-4 rounded-xl text-lg font-semibold transition-colors">
                        🎯 {{ __('messages.try_demo') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Interview Types -->
<section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.available_interview_types') }}</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {{ __('messages.choose_interview_type_to_practice') }}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- Technical Interview -->
            <div class="card-hover bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">مقابلة تقنية</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    أسئلة تقنية متخصصة في البرمجة وتطوير البرمجيات
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 30-45 دقيقة</span>
                    <button onclick="startTechnicalInterview()" class="text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300">
                        ابدأ الآن →
                    </button>
                </div>
            </div>

            <!-- HR Interview -->
            <div class="card-hover bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">مقابلة الموارد البشرية</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    أسئلة عامة حول الشخصية والخبرة والدافعية
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 20-30 دقيقة</span>
                    <button onclick="startHRInterview()" class="text-green-600 dark:text-green-400 font-semibold hover:text-green-700 dark:hover:text-green-300">
                        ابدأ الآن →
                    </button>
                </div>
            </div>

            <!-- Behavioral Interview -->
            <div class="card-hover bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">مقابلة سلوكية</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    أسئلة حول المواقف والتجارب السابقة والتعامل مع التحديات
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 25-35 دقيقة</span>
                    <button onclick="startBehavioralInterview()" class="text-purple-600 dark:text-purple-400 font-semibold hover:text-purple-700 dark:hover:text-purple-300">
                        ابدأ الآن →
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Interview Setup -->
<section id="interview-setup" class="hidden py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">إعداد المقابلة</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">اختر إعدادات المقابلة التي تناسبك</p>
        </div>

        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
            <form id="interview-setup-form" class="space-y-8">
                <!-- Job Position -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المنصب المستهدف</label>
                    <input type="text" placeholder="مثال: مطور ويب، مدير مشاريع، مصمم جرافيك" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <!-- Experience Level -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مستوى الخبرة</label>
                    <select class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر مستوى خبرتك</option>
                        <option value="entry">مبتدئ (0-2 سنة)</option>
                        <option value="mid">متوسط (2-5 سنوات)</option>
                        <option value="senior">خبير (5+ سنوات)</option>
                        <option value="lead">قيادي (8+ سنوات)</option>
                    </select>
                </div>

                <!-- Interview Duration -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مدة المقابلة</label>
                    <div class="grid grid-cols-3 gap-4">
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="duration" value="15" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">15 دقيقة</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="duration" value="30" class="mr-3 rtl:ml-3 rtl:mr-0" checked>
                            <span class="text-gray-700 dark:text-gray-300">30 دقيقة</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="duration" value="45" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">45 دقيقة</span>
                        </label>
                    </div>
                </div>

                <!-- Interview Language -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">لغة المقابلة</label>
                    <div class="grid grid-cols-3 gap-4">
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="language" value="ar" class="mr-3 rtl:ml-3 rtl:mr-0" checked>
                            <span class="text-gray-700 dark:text-gray-300">العربية</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="language" value="en" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">English</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="language" value="fr" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">Français</span>
                        </label>
                    </div>
                </div>

                <!-- Start Interview Button -->
                <div class="text-center pt-8">
                    <button type="submit" class="btn-primary text-white px-12 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>ابدأ المقابلة الآن</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Interview Interface -->
<section id="interview-interface" class="hidden py-20 bg-white dark:bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Interview Header -->
        <div class="bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl p-6 mb-8 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold mb-2">مقابلة عمل تفاعلية</h2>
                    <p class="opacity-90" id="interview-position">مطور ويب - مستوى متوسط</p>
                </div>
                <div class="text-right">
                    <div class="text-3xl font-bold" id="timer">30:00</div>
                    <div class="text-sm opacity-90">الوقت المتبقي</div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- AI Interviewer -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-6">
                    <!-- AI Avatar -->
                    <div class="flex items-start space-x-4 rtl:space-x-reverse mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">المحاور الذكي</h3>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                                <p class="text-gray-700 dark:text-gray-300" id="ai-question">
                                    مرحباً بك! أنا سعيد لإجراء هذه المقابلة معك اليوم. دعنا نبدأ بسؤال بسيط: حدثني عن نفسك وخبرتك في مجال تطوير الويب.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Response Area -->
                    <div class="space-y-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">إجابتك:</label>
                        <textarea id="user-response" rows="6" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none" placeholder="اكتب إجابتك هنا..."></textarea>
                        
                        <!-- Voice Recording -->
                        <div class="flex items-center space-x-4 rtl:space-x-reverse">
                            <button id="record-btn" onclick="toggleRecording()" class="flex items-center space-x-2 rtl:space-x-reverse bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                                </svg>
                                <span>تسجيل صوتي</span>
                            </button>
                            <button onclick="submitAnswer()" class="btn-primary text-white px-6 py-2 rounded-lg">
                                إرسال الإجابة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interview Progress -->
            <div class="space-y-6">
                <!-- Progress Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">تقدم المقابلة</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>الأسئلة المجابة</span>
                                <span id="progress-text">1 من 8</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-to-r from-purple-500 to-pink-600 h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 12.5%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tips Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">نصائح للمقابلة</h3>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            كن واضحاً ومحدداً في إجاباتك
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            استخدم أمثلة من تجاربك السابقة
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            اظهر حماسك للمنصب
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            لا تخف من طرح الأسئلة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Interview Results -->
<section id="interview-results" class="hidden py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">نتائج المقابلة</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">تحليل شامل لأدائك في المقابلة</p>
        </div>

        <!-- Overall Score -->
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 mb-8">
            <div class="text-center mb-8">
                <div class="relative w-32 h-32 mx-auto mb-4">
                    <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-300 dark:text-gray-600" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        <path class="text-purple-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="82, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-3xl font-bold text-gray-900 dark:text-white">82%</span>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">أداء ممتاز!</h3>
                <p class="text-gray-600 dark:text-gray-300">لقد أظهرت مهارات قوية في المقابلة</p>
            </div>
        </div>

        <!-- Detailed Feedback -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Strengths -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    نقاط القوة
                </h3>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">إجابات واضحة ومنظمة</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">استخدام أمثلة محددة</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">ثقة في التعبير</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">معرفة جيدة بالمجال</span>
                    </li>
                </ul>
            </div>

            <!-- Areas for Improvement -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    مجالات التحسين
                </h3>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">تحسين لغة الجسد</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">طرح أسئلة أكثر</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">تقليل الكلمات الحشو</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">إظهار المزيد من الحماس</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="downloadInterviewReport()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>تحميل تقرير المقابلة</span>
                </button>
                <button onclick="retakeInterview()" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-400 transition-all duration-300">
                    إعادة المقابلة
                </button>
                <button onclick="tryDifferentType()" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200">
                    جرب نوع مقابلة آخر
                </button>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
// Interview Simulation Functions
let interviewType = '';
let currentQuestion = 0;
let interviewQuestions = [];
let userAnswers = [];
let interviewTimer = null;
let timeRemaining = 1800; // 30 minutes default
let isRecording = false;

// Interview Questions Database
const questionDatabase = {
    technical: [
        "حدثني عن نفسك وخبرتك في البرمجة",
        "ما هي لغات البرمجة التي تجيدها؟",
        "كيف تتعامل مع debugging في الكود؟",
        "اشرح لي مفهوم Object-Oriented Programming",
        "ما هو الفرق بين SQL و NoSQL؟",
        "كيف تضمن أمان التطبيق الذي تطوره؟",
        "اشرح لي عملية Git workflow",
        "ما هي أفضل الممارسات في كتابة الكود؟"
    ],
    hr: [
        "حدثني عن نفسك",
        "لماذا تريد العمل في شركتنا؟",
        "ما هي نقاط قوتك وضعفك؟",
        "أين ترى نفسك خلال 5 سنوات؟",
        "لماذا تريد ترك وظيفتك الحالية؟",
        "كيف تتعامل مع ضغط العمل؟",
        "ما هو راتبك المتوقع؟",
        "هل لديك أي أسئلة لنا؟"
    ],
    behavioral: [
        "حدثني عن موقف واجهت فيه تحدياً كبيراً في العمل",
        "اذكر مثالاً على وقت عملت فيه ضمن فريق",
        "كيف تتعامل مع الصراعات في مكان العمل؟",
        "حدثني عن مشروع فشل وكيف تعاملت معه",
        "اذكر موقفاً أظهرت فيه مهارات قيادية",
        "كيف تتعامل مع المواعيد النهائية الضيقة؟",
        "حدثني عن وقت اتخذت فيه قراراً صعباً",
        "كيف تتعامل مع التغيير في بيئة العمل؟"
    ]
};

function startInterview() {
    document.getElementById('interview-setup').classList.remove('hidden');
    document.getElementById('interview-setup').scrollIntoView({ behavior: 'smooth' });
}

function showInterviewTypes() {
    // Scroll to interview types section
    document.querySelector('.py-20.bg-white.dark\\:bg-gray-900').scrollIntoView({ behavior: 'smooth' });
}

function startTechnicalInterview() {
    interviewType = 'technical';
    startInterview();
}

function startHRInterview() {
    interviewType = 'hr';
    startInterview();
}

function startBehavioralInterview() {
    interviewType = 'behavioral';
    startInterview();
}

// Setup form submission
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('interview-setup-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this);
        const duration = parseInt(formData.get('duration')) || 30;
        timeRemaining = duration * 60;

        // Initialize interview
        initializeInterview();
    });
});

function initializeInterview() {
    // Get questions for the interview type
    interviewQuestions = questionDatabase[interviewType] || questionDatabase.hr;
    currentQuestion = 0;
    userAnswers = [];

    // Show interview interface
    document.getElementById('interview-setup').classList.add('hidden');
    document.getElementById('interview-interface').classList.remove('hidden');
    document.getElementById('interview-interface').scrollIntoView({ behavior: 'smooth' });

    // Start timer
    startTimer();

    // Load first question
    loadQuestion();
}

function startTimer() {
    interviewTimer = setInterval(() => {
        timeRemaining--;
        updateTimerDisplay();

        if (timeRemaining <= 0) {
            endInterview();
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    document.getElementById('timer').textContent =
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function loadQuestion() {
    if (currentQuestion < interviewQuestions.length) {
        document.getElementById('ai-question').textContent = interviewQuestions[currentQuestion];
        document.getElementById('user-response').value = '';

        // Update progress
        const progress = ((currentQuestion + 1) / interviewQuestions.length) * 100;
        document.getElementById('progress-bar').style.width = progress + '%';
        document.getElementById('progress-text').textContent =
            `${currentQuestion + 1} من ${interviewQuestions.length}`;
    } else {
        endInterview();
    }
}

function submitAnswer() {
    const answer = document.getElementById('user-response').value.trim();

    if (!answer) {
        alert('يرجى كتابة إجابتك قبل المتابعة');
        return;
    }

    // Save answer
    userAnswers[currentQuestion] = {
        question: interviewQuestions[currentQuestion],
        answer: answer,
        timestamp: new Date()
    };

    // Move to next question
    currentQuestion++;

    // Add delay for realistic feel
    setTimeout(() => {
        loadQuestion();
    }, 1000);
}

function toggleRecording() {
    const recordBtn = document.getElementById('record-btn');

    if (!isRecording) {
        // Start recording
        isRecording = true;
        recordBtn.innerHTML = `
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path>
            </svg>
            <span>إيقاف التسجيل</span>
        `;
        recordBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
        recordBtn.classList.add('bg-gray-500', 'hover:bg-gray-600');

        // Simulate recording (in real app, would use Web Audio API)
        alert('بدأ التسجيل الصوتي...');
    } else {
        // Stop recording
        isRecording = false;
        recordBtn.innerHTML = `
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
            </svg>
            <span>تسجيل صوتي</span>
        `;
        recordBtn.classList.remove('bg-gray-500', 'hover:bg-gray-600');
        recordBtn.classList.add('bg-red-500', 'hover:bg-red-600');

        alert('تم إيقاف التسجيل. يمكنك الآن كتابة إجابتك أو إرسالها.');
    }
}

function endInterview() {
    // Stop timer
    if (interviewTimer) {
        clearInterval(interviewTimer);
    }

    // Show loading for analysis
    showInterviewAnalysisLoading();

    // Simulate AI analysis
    setTimeout(() => {
        hideInterviewAnalysisLoading();
        showInterviewResults();
    }, 3000);
}

function showInterviewAnalysisLoading() {
    const loadingHTML = `
        <div id="interview-analysis-loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">جاري تحليل أدائك</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">الذكاء الاصطناعي يحلل إجاباتك ويقيم أداءك...</p>
                <div class="text-sm text-gray-500 dark:text-gray-400" id="interview-analysis-step">تحليل محتوى الإجابات...</div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loadingHTML);

    // Simulate analysis steps
    const steps = [
        "تحليل محتوى الإجابات...",
        "تقييم وضوح التعبير...",
        "تحليل المهارات التقنية...",
        "تقييم الثقة والحماس...",
        "إنشاء التوصيات والملاحظات..."
    ];

    let stepIndex = 0;
    const stepInterval = setInterval(() => {
        stepIndex++;
        if (stepIndex < steps.length) {
            document.getElementById('interview-analysis-step').textContent = steps[stepIndex];
        } else {
            clearInterval(stepInterval);
        }
    }, 600);
}

function hideInterviewAnalysisLoading() {
    const loadingElement = document.getElementById('interview-analysis-loading');
    if (loadingElement) {
        loadingElement.remove();
    }
}

function showInterviewResults() {
    document.getElementById('interview-interface').classList.add('hidden');
    document.getElementById('interview-results').classList.remove('hidden');
    document.getElementById('interview-results').scrollIntoView({ behavior: 'smooth' });
}

function downloadInterviewReport() {
    alert('سيتم تحميل تقرير المقابلة الكامل قريباً!');
}

function retakeInterview() {
    // Reset interview
    currentQuestion = 0;
    userAnswers = [];
    document.getElementById('interview-results').classList.add('hidden');
    initializeInterview();
}

function tryDifferentType() {
    // Reset and go back to setup
    currentQuestion = 0;
    userAnswers = [];
    document.getElementById('interview-results').classList.add('hidden');
    document.getElementById('interview-setup').classList.remove('hidden');
    document.getElementById('interview-setup').scrollIntoView({ behavior: 'smooth' });
}
</script>
@endpush
@endsection
