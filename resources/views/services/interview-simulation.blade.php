@extends('layouts.app')

@section('title', __('messages.interview_simulation') . ' - MonOri AI')
@section('description', __('messages.interview_simulation_description'))

@section('content')
<style>
    .interview-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        position: relative;
    }
    
    .interview-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .ai-avatar {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        position: relative;
        animation: pulse 2s infinite;
    }
    
    .ai-avatar.speaking {
        animation: speaking 0.5s infinite alternate;
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }
    
    @keyframes speaking {
        0% { transform: scale(1); }
        100% { transform: scale(1.05); }
    }
    
    .voice-visualizer {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        height: 40px;
        margin: 1rem 0;
    }
    
    .voice-bar {
        width: 4px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 2px;
        transition: height 0.1s ease;
    }
    
    .recording-indicator {
        display: none;
        align-items: center;
        gap: 0.5rem;
        color: #ef4444;
        font-weight: 600;
        margin: 1rem 0;
    }
    
    .recording-indicator.active {
        display: flex;
    }
    
    .recording-dot {
        width: 12px;
        height: 12px;
        background: #ef4444;
        border-radius: 50%;
        animation: blink 1s infinite;
    }
    
    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }
    
    .question-card {
        background: #f8fafc;
        border-radius: 16px;
        padding: 2rem;
        margin: 2rem 0;
        border-left: 4px solid #667eea;
    }
    
    .answer-area {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        border: 2px solid #e5e7eb;
        transition: border-color 0.3s ease;
        min-height: 120px;
    }
    
    .answer-area:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    .control-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }
    
    .control-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .control-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }
    
    .control-button.recording {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }
    
    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin: 1rem 0;
    }
    
    .progress-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        transition: width 0.3s ease;
    }
    
    .feedback-card {
        background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
        border-radius: 16px;
        padding: 2rem;
        margin: 1rem 0;
        border-left: 4px solid #0ea5e9;
    }
    
    .score-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: conic-gradient(#667eea 0deg, #667eea var(--score-deg), #e5e7eb var(--score-deg), #e5e7eb 360deg);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }
    
    .score-circle::before {
        content: '';
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 50%;
        position: absolute;
    }
    
    .score-text {
        position: relative;
        z-index: 1;
        font-weight: bold;
        color: #667eea;
    }
</style>

<!-- Interview Training Container -->
<div class="interview-container">
    <div class="container mx-auto px-4 py-12">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl font-bold text-white mb-6">
                @if(app()->getLocale() === 'ar')
                    تدريب مقابلة العمل بالذكاء الاصطناعي
                @elseif(app()->getLocale() === 'fr')
                    Entraînement d'entretien d'embauche avec IA
                @else
                    AI-Powered Interview Training
                @endif
            </h1>
            <p class="text-xl text-white/80 max-w-3xl mx-auto">
                @if(app()->getLocale() === 'ar')
                    تدرب على مقابلات العمل مع مدرب ذكي يستمع لك ويقيم أداءك ويقدم لك نصائح مخصصة
                @elseif(app()->getLocale() === 'fr')
                    Entraînez-vous aux entretiens d'embauche avec un coach intelligent qui vous écoute, évalue votre performance et vous donne des conseils personnalisés
                @else
                    Practice job interviews with an intelligent coach that listens to you, evaluates your performance, and gives you personalized advice
                @endif
            </p>
        </div>

        <!-- Interview Setup -->
        <div class="interview-card max-w-4xl mx-auto p-8" id="interviewSetup">
            <div class="text-center mb-8">
                <div class="ai-avatar" id="aiAvatar">
                    <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">
                    @if(app()->getLocale() === 'ar')
                        مرحباً! أنا مدربك الذكي للمقابلات
                    @elseif(app()->getLocale() === 'fr')
                        Bonjour! Je suis votre coach intelligent pour les entretiens
                    @else
                        Hello! I'm your intelligent interview coach
                    @endif
                </h2>
                <p class="text-gray-600">
                    @if(app()->getLocale() === 'ar')
                        سأطرح عليك أسئلة مقابلة حقيقية وأستمع لإجاباتك وأقيم أداءك
                    @elseif(app()->getLocale() === 'fr')
                        Je vais vous poser de vraies questions d'entretien, écouter vos réponses et évaluer votre performance
                    @else
                        I'll ask you real interview questions, listen to your answers, and evaluate your performance
                    @endif
                </p>
            </div>

            <!-- Interview Configuration -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        @if(app()->getLocale() === 'ar')
                            المنصب المطلوب
                        @elseif(app()->getLocale() === 'fr')
                            Poste souhaité
                        @else
                            Job Position
                        @endif
                    </label>
                    <input type="text" id="jobPosition" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                           placeholder="مطور ويب، مهندس برمجيات، مصمم...">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        @if(app()->getLocale() === 'ar')
                            نوع المقابلة
                        @elseif(app()->getLocale() === 'fr')
                            Type d'entretien
                        @else
                            Interview Type
                        @endif
                    </label>
                    <select id="interviewType" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="technical">
                            @if(app()->getLocale() === 'ar')
                                مقابلة تقنية
                            @elseif(app()->getLocale() === 'fr')
                                Entretien technique
                            @else
                                Technical Interview
                            @endif
                        </option>
                        <option value="hr">
                            @if(app()->getLocale() === 'ar')
                                مقابلة الموارد البشرية
                            @elseif(app()->getLocale() === 'fr')
                                Entretien RH
                            @else
                                HR Interview
                            @endif
                        </option>
                        <option value="behavioral">
                            @if(app()->getLocale() === 'ar')
                                مقابلة سلوكية
                            @elseif(app()->getLocale() === 'fr')
                                Entretien comportemental
                            @else
                                Behavioral Interview
                            @endif
                        </option>
                    </select>
                </div>
            </div>

            <!-- Language Selection -->
            <div class="mb-8">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    @if(app()->getLocale() === 'ar')
                        لغة المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Langue de l'entretien
                    @else
                        Interview Language
                    @endif
                </label>
                <div class="grid grid-cols-3 gap-4">
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="interviewLanguage" value="ar" class="mr-3" checked>
                        <span>العربية</span>
                    </label>
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="interviewLanguage" value="en" class="mr-3">
                        <span>English</span>
                    </label>
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="interviewLanguage" value="fr" class="mr-3">
                        <span>Français</span>
                    </label>
                </div>
            </div>

            <!-- Start Button -->
            <div class="text-center">
                <button onclick="startInterview()" class="control-button text-lg px-8 py-4">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        ابدأ المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Commencer l'entretien
                    @else
                        Start Interview
                    @endif
                </button>
            </div>
        </div>

        <!-- Interview Interface -->
        <div class="interview-card max-w-4xl mx-auto p-8 hidden" id="interviewInterface">
            <!-- Progress Bar -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">
                        @if(app()->getLocale() === 'ar')
                            التقدم
                        @elseif(app()->getLocale() === 'fr')
                            Progrès
                        @else
                            Progress
                        @endif
                    </span>
                    <span class="text-sm text-gray-500" id="questionCounter">1 / 5</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 20%"></div>
                </div>
            </div>

            <!-- AI Avatar -->
            <div class="text-center mb-6">
                <div class="ai-avatar" id="interviewAvatar">
                    <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>

                <!-- Voice Visualizer -->
                <div class="voice-visualizer" id="voiceVisualizer">
                    <div class="voice-bar" style="height: 20px;"></div>
                    <div class="voice-bar" style="height: 30px;"></div>
                    <div class="voice-bar" style="height: 25px;"></div>
                    <div class="voice-bar" style="height: 35px;"></div>
                    <div class="voice-bar" style="height: 20px;"></div>
                    <div class="voice-bar" style="height: 40px;"></div>
                    <div class="voice-bar" style="height: 25px;"></div>
                </div>
            </div>

            <!-- Question Card -->
            <div class="question-card">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    @if(app()->getLocale() === 'ar')
                        السؤال:
                    @elseif(app()->getLocale() === 'fr')
                        Question:
                    @else
                        Question:
                    @endif
                </h3>
                <p class="text-gray-700 text-lg leading-relaxed" id="currentQuestion">
                    حدثني عن نفسك وخبراتك المهنية
                </p>
            </div>

            <!-- Recording Indicator -->
            <div class="recording-indicator" id="recordingIndicator">
                <div class="recording-dot"></div>
                <span>
                    @if(app()->getLocale() === 'ar')
                        جاري التسجيل...
                    @elseif(app()->getLocale() === 'fr')
                        Enregistrement en cours...
                    @else
                        Recording...
                    @endif
                </span>
            </div>

            <!-- Answer Area -->
            <div class="answer-area mb-6">
                <textarea id="answerText" class="w-full h-32 border-none outline-none resize-none"
                          placeholder="اكتب إجابتك هنا أو استخدم التسجيل الصوتي..."></textarea>
            </div>

            <!-- Controls -->
            <div class="flex flex-wrap gap-4 justify-center">
                <button onclick="startRecording()" id="recordButton" class="control-button">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        تسجيل صوتي
                    @elseif(app()->getLocale() === 'fr')
                        Enregistrer
                    @else
                        Record Audio
                    @endif
                </button>

                <button onclick="stopRecording()" id="stopButton" class="control-button recording hidden">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 6h12v12H6z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        إيقاف التسجيل
                    @elseif(app()->getLocale() === 'fr')
                        Arrêter
                    @else
                        Stop Recording
                    @endif
                </button>

                <button onclick="submitAnswer()" id="submitButton" class="control-button">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        إرسال الإجابة
                    @elseif(app()->getLocale() === 'fr')
                        Envoyer la réponse
                    @else
                        Submit Answer
                    @endif
                </button>

                <button onclick="skipQuestion()" class="control-button" style="background: #6b7280;">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        تخطي السؤال
                    @elseif(app()->getLocale() === 'fr')
                        Passer la question
                    @else
                        Skip Question
                    @endif
                </button>
            </div>
        </div>

        <!-- Results Interface -->
        <div class="interview-card max-w-4xl mx-auto p-8 hidden" id="resultsInterface">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-800 mb-4">
                    @if(app()->getLocale() === 'ar')
                        نتائج المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Résultats de l'entretien
                    @else
                        Interview Results
                    @endif
                </h2>
                <p class="text-gray-600">
                    @if(app()->getLocale() === 'ar')
                        تحليل شامل لأدائك في المقابلة مع نصائح للتحسين
                    @elseif(app()->getLocale() === 'fr')
                        Analyse complète de votre performance avec des conseils d'amélioration
                    @else
                        Comprehensive analysis of your interview performance with improvement tips
                    @endif
                </p>
            </div>

            <!-- Overall Score -->
            <div class="text-center mb-8">
                <div class="score-circle mx-auto mb-4" style="--score-deg: 288deg;">
                    <div class="score-text text-2xl">85%</div>
                </div>
                <h3 class="text-xl font-semibold text-gray-800">
                    @if(app()->getLocale() === 'ar')
                        أداء ممتاز!
                    @elseif(app()->getLocale() === 'fr')
                        Excellente performance!
                    @else
                        Excellent Performance!
                    @endif
                </h3>
            </div>

            <!-- Feedback Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div class="feedback-card">
                    <h4 class="font-semibold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            نقاط القوة
                        @elseif(app()->getLocale() === 'fr')
                            Points forts
                        @else
                            Strengths
                        @endif
                    </h4>
                    <ul class="space-y-2 text-gray-700">
                        <li>• إجابات واضحة ومنظمة</li>
                        <li>• ثقة في التعبير</li>
                        <li>• استخدام أمثلة محددة</li>
                    </ul>
                </div>

                <div class="feedback-card" style="background: linear-gradient(135deg, #fef3c7, #fde68a); border-left-color: #f59e0b;">
                    <h4 class="font-semibold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            مجالات التحسين
                        @elseif(app()->getLocale() === 'fr')
                            Domaines d'amélioration
                        @else
                            Areas for Improvement
                        @endif
                    </h4>
                    <ul class="space-y-2 text-gray-700">
                        <li>• تحسين لغة الجسد</li>
                        <li>• طرح أسئلة أكثر</li>
                        <li>• تقليل الكلمات الحشو</li>
                    </ul>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-4 justify-center">
                <button onclick="restartInterview()" class="control-button">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        إعادة المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Refaire l'entretien
                    @else
                        Retry Interview
                    @endif
                </button>

                <button onclick="downloadReport()" class="control-button" style="background: #059669;">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        تحميل التقرير
                    @elseif(app()->getLocale() === 'fr')
                        Télécharger le rapport
                    @else
                        Download Report
                    @endif
                </button>

                <button onclick="goHome()" class="control-button" style="background: #6b7280;">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        العودة للرئيسية
                    @elseif(app()->getLocale() === 'fr')
                        Retour à l'accueil
                    @else
                        Back to Home
                    @endif
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Interview Training System
class InterviewTrainer {
    constructor() {
        this.currentQuestion = 0;
        this.questions = [];
        this.answers = [];
        this.isRecording = false;
        this.recognition = null;
        this.currentLanguage = 'ar';
        this.interviewType = 'technical';
        this.voiceInterval = null;

        this.questionSets = {
            ar: {
                technical: [
                    "حدثني عن نفسك وخبراتك في البرمجة",
                    "ما هي لغات البرمجة التي تجيدها؟",
                    "كيف تتعامل مع debugging في الكود؟",
                    "اشرح لي مفهوم Object-Oriented Programming",
                    "ما هو الفرق بين SQL و NoSQL؟"
                ],
                hr: [
                    "حدثني عن نفسك",
                    "لماذا تريد العمل في شركتنا؟",
                    "ما هي نقاط قوتك وضعفك؟",
                    "أين ترى نفسك خلال 5 سنوات؟",
                    "كيف تتعامل مع ضغط العمل؟"
                ],
                behavioral: [
                    "حدثني عن موقف واجهت فيه تحدياً كبيراً",
                    "اذكر مثالاً على وقت عملت فيه ضمن فريق",
                    "كيف تتعامل مع الصراعات في العمل؟",
                    "حدثني عن مشروع فشل وكيف تعاملت معه",
                    "اذكر موقفاً أظهرت فيه مهارات قيادية"
                ]
            },
            en: {
                technical: [
                    "Tell me about yourself and your programming experience",
                    "What programming languages do you know?",
                    "How do you handle debugging in code?",
                    "Explain Object-Oriented Programming to me",
                    "What's the difference between SQL and NoSQL?"
                ],
                hr: [
                    "Tell me about yourself",
                    "Why do you want to work for our company?",
                    "What are your strengths and weaknesses?",
                    "Where do you see yourself in 5 years?",
                    "How do you handle work pressure?"
                ],
                behavioral: [
                    "Tell me about a time you faced a major challenge",
                    "Give me an example of when you worked in a team",
                    "How do you handle conflicts at work?",
                    "Tell me about a project that failed and how you handled it",
                    "Describe a situation where you showed leadership skills"
                ]
            },
            fr: {
                technical: [
                    "Parlez-moi de vous et de votre expérience en programmation",
                    "Quels langages de programmation connaissez-vous?",
                    "Comment gérez-vous le debugging dans le code?",
                    "Expliquez-moi la Programmation Orientée Objet",
                    "Quelle est la différence entre SQL et NoSQL?"
                ],
                hr: [
                    "Parlez-moi de vous",
                    "Pourquoi voulez-vous travailler dans notre entreprise?",
                    "Quels sont vos points forts et vos faiblesses?",
                    "Où vous voyez-vous dans 5 ans?",
                    "Comment gérez-vous la pression au travail?"
                ],
                behavioral: [
                    "Parlez-moi d'un moment où vous avez fait face à un défi majeur",
                    "Donnez-moi un exemple de travail en équipe",
                    "Comment gérez-vous les conflits au travail?",
                    "Parlez-moi d'un projet qui a échoué et comment vous l'avez géré",
                    "Décrivez une situation où vous avez montré des compétences de leadership"
                ]
            }
        };

        this.initializeSpeechRecognition();
    }

    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            this.recognition.continuous = true;
            this.recognition.interimResults = true;

            this.recognition.onresult = (event) => {
                let finalTranscript = '';
                for (let i = event.resultIndex; i < event.results.length; i++) {
                    if (event.results[i].isFinal) {
                        finalTranscript += event.results[i][0].transcript;
                    }
                }
                if (finalTranscript) {
                    document.getElementById('answerText').value += finalTranscript;
                }
            };

            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.stopRecording();
            };
        }
    }

    start() {
        // Get selected options
        this.currentLanguage = document.querySelector('input[name="interviewLanguage"]:checked').value;
        this.interviewType = document.getElementById('interviewType').value;

        // Set language for speech recognition
        if (this.recognition) {
            const langMap = { ar: 'ar-SA', en: 'en-US', fr: 'fr-FR' };
            this.recognition.lang = langMap[this.currentLanguage];
        }

        // Get questions for selected language and type
        this.questions = this.questionSets[this.currentLanguage][this.interviewType];
        this.currentQuestion = 0;
        this.answers = [];

        // Show interview interface
        document.getElementById('interviewSetup').classList.add('hidden');
        document.getElementById('interviewInterface').classList.remove('hidden');

        // Start first question with welcome message
        this.showQuestion();

        // Welcome message first, then question
        this.speakWelcomeMessage(() => {
            setTimeout(() => {
                this.speakQuestion();
            }, 1000);
        });
    }

    speakWelcomeMessage(callback) {
        if ('speechSynthesis' in window) {
            speechSynthesis.cancel();

            setTimeout(() => {
                let welcomeMessage = '';
                if (this.currentLanguage === 'ar') {
                    welcomeMessage = 'مرحباً بك في مقابلة العمل. سأطرح عليك بعض الأسئلة. خذ وقتك في الإجابة.';
                } else if (this.currentLanguage === 'fr') {
                    welcomeMessage = 'Bienvenue à votre entretien d\'embauche. Je vais vous poser quelques questions. Prenez votre temps pour répondre.';
                } else {
                    welcomeMessage = 'Welcome to your job interview. I will ask you some questions. Take your time to answer.';
                }

                const utterance = new SpeechSynthesisUtterance(welcomeMessage);
                const langMap = { ar: 'ar-SA', en: 'en-US', fr: 'fr-FR' };
                utterance.lang = langMap[this.currentLanguage];
                utterance.rate = 0.8;
                utterance.pitch = 1.0;
                utterance.volume = 1.0;

                document.getElementById('interviewAvatar').classList.add('speaking');
                document.getElementById('voiceVisualizer').style.display = 'flex';
                this.animateVoiceVisualizer();

                utterance.onend = () => {
                    document.getElementById('interviewAvatar').classList.remove('speaking');
                    document.getElementById('voiceVisualizer').style.display = 'none';
                    this.stopVoiceVisualizer();
                    if (callback) callback();
                };

                utterance.onerror = () => {
                    document.getElementById('interviewAvatar').classList.remove('speaking');
                    document.getElementById('voiceVisualizer').style.display = 'none';
                    this.stopVoiceVisualizer();
                    if (callback) callback();
                };

                speechSynthesis.speak(utterance);
            }, 300);
        } else {
            if (callback) callback();
        }
    }

    showQuestion() {
        const questionText = this.questions[this.currentQuestion];
        document.getElementById('currentQuestion').textContent = questionText;
        document.getElementById('questionCounter').textContent = `${this.currentQuestion + 1} / ${this.questions.length}`;

        const progress = ((this.currentQuestion + 1) / this.questions.length) * 100;
        document.getElementById('progressFill').style.width = progress + '%';

        // Clear previous answer
        document.getElementById('answerText').value = '';
    }

    speakQuestion() {
        if ('speechSynthesis' in window) {
            // Stop any current speech
            speechSynthesis.cancel();

            // Wait a moment before starting new speech
            setTimeout(() => {
                const utterance = new SpeechSynthesisUtterance(this.questions[this.currentQuestion]);
                const langMap = { ar: 'ar-SA', en: 'en-US', fr: 'fr-FR' };
                utterance.lang = langMap[this.currentLanguage];

                // Set speech properties for clarity
                utterance.rate = 0.8; // Slower speech for clarity
                utterance.pitch = 1.0; // Normal pitch
                utterance.volume = 1.0; // Full volume

                // Show speaking animation
                document.getElementById('interviewAvatar').classList.add('speaking');
                document.getElementById('voiceVisualizer').style.display = 'flex';
                this.animateVoiceVisualizer();

                utterance.onstart = () => {
                    console.log('Speech started');
                };

                utterance.onend = () => {
                    document.getElementById('interviewAvatar').classList.remove('speaking');
                    document.getElementById('voiceVisualizer').style.display = 'none';
                    this.stopVoiceVisualizer();
                    console.log('Speech ended');
                };

                utterance.onerror = (event) => {
                    console.error('Speech error:', event.error);
                    document.getElementById('interviewAvatar').classList.remove('speaking');
                    document.getElementById('voiceVisualizer').style.display = 'none';
                    this.stopVoiceVisualizer();
                };

                speechSynthesis.speak(utterance);
            }, 500);
        }
    }

    animateVoiceVisualizer() {
        const bars = document.querySelectorAll('.voice-bar');
        this.voiceInterval = setInterval(() => {
            bars.forEach(bar => {
                const height = Math.random() * 40 + 10;
                bar.style.height = height + 'px';
            });
        }, 150);
    }

    stopVoiceVisualizer() {
        if (this.voiceInterval) {
            clearInterval(this.voiceInterval);
            this.voiceInterval = null;
        }
        // Reset bars to default height
        const bars = document.querySelectorAll('.voice-bar');
        bars.forEach(bar => {
            bar.style.height = '20px';
        });
    }

    startRecording() {
        if (!this.recognition) {
            alert('Speech recognition not supported in this browser');
            return;
        }

        this.isRecording = true;
        document.getElementById('recordButton').classList.add('hidden');
        document.getElementById('stopButton').classList.remove('hidden');
        document.getElementById('recordingIndicator').classList.add('active');

        this.recognition.start();
    }

    stopRecording() {
        if (this.recognition && this.isRecording) {
            this.recognition.stop();
        }

        this.isRecording = false;
        document.getElementById('recordButton').classList.remove('hidden');
        document.getElementById('stopButton').classList.add('hidden');
        document.getElementById('recordingIndicator').classList.remove('active');
    }

    submitAnswer() {
        const answer = document.getElementById('answerText').value.trim();

        if (!answer) {
            const alertMessage = this.currentLanguage === 'ar' ? 'يرجى كتابة إجابة قبل الإرسال' :
                                this.currentLanguage === 'fr' ? 'Veuillez fournir une réponse avant de soumettre' :
                                'Please provide an answer before submitting';
            alert(alertMessage);
            return;
        }

        // Stop any current speech
        speechSynthesis.cancel();

        // Save answer
        this.answers.push({
            question: this.questions[this.currentQuestion],
            answer: answer,
            timestamp: new Date()
        });

        // Move to next question or finish
        this.currentQuestion++;

        if (this.currentQuestion < this.questions.length) {
            this.showQuestion();

            // Give feedback and move to next question
            this.speakTransition(() => {
                setTimeout(() => this.speakQuestion(), 1500);
            });
        } else {
            this.finishInterview();
        }
    }

    speakTransition(callback) {
        if ('speechSynthesis' in window) {
            let transitionMessage = '';
            if (this.currentLanguage === 'ar') {
                transitionMessage = 'شكراً لك. السؤال التالي:';
            } else if (this.currentLanguage === 'fr') {
                transitionMessage = 'Merci. Question suivante:';
            } else {
                transitionMessage = 'Thank you. Next question:';
            }

            const utterance = new SpeechSynthesisUtterance(transitionMessage);
            const langMap = { ar: 'ar-SA', en: 'en-US', fr: 'fr-FR' };
            utterance.lang = langMap[this.currentLanguage];
            utterance.rate = 0.8;
            utterance.pitch = 1.0;
            utterance.volume = 1.0;

            document.getElementById('interviewAvatar').classList.add('speaking');
            document.getElementById('voiceVisualizer').style.display = 'flex';
            this.animateVoiceVisualizer();

            utterance.onend = () => {
                document.getElementById('interviewAvatar').classList.remove('speaking');
                document.getElementById('voiceVisualizer').style.display = 'none';
                this.stopVoiceVisualizer();
                if (callback) callback();
            };

            utterance.onerror = () => {
                document.getElementById('interviewAvatar').classList.remove('speaking');
                document.getElementById('voiceVisualizer').style.display = 'none';
                this.stopVoiceVisualizer();
                if (callback) callback();
            };

            speechSynthesis.speak(utterance);
        } else {
            if (callback) callback();
        }
    }

    skipQuestion() {
        this.answers.push({
            question: this.questions[this.currentQuestion],
            answer: '[Skipped]',
            timestamp: new Date()
        });

        this.currentQuestion++;

        if (this.currentQuestion < this.questions.length) {
            this.showQuestion();
            setTimeout(() => this.speakQuestion(), 1000);
        } else {
            this.finishInterview();
        }
    }

    finishInterview() {
        // Stop any current speech
        speechSynthesis.cancel();

        // Speak completion message
        this.speakCompletionMessage(() => {
            // Hide interview interface
            document.getElementById('interviewInterface').classList.add('hidden');

            // Show results with delay for processing effect
            setTimeout(() => {
                document.getElementById('resultsInterface').classList.remove('hidden');
            }, 2000);
        });
    }

    speakCompletionMessage(callback) {
        if ('speechSynthesis' in window) {
            let completionMessage = '';
            if (this.currentLanguage === 'ar') {
                completionMessage = 'ممتاز! انتهت المقابلة. سأقوم الآن بتحليل إجاباتك وإعداد التقرير.';
            } else if (this.currentLanguage === 'fr') {
                completionMessage = 'Excellent! L\'entretien est terminé. Je vais maintenant analyser vos réponses et préparer le rapport.';
            } else {
                completionMessage = 'Excellent! The interview is complete. I will now analyze your answers and prepare the report.';
            }

            const utterance = new SpeechSynthesisUtterance(completionMessage);
            const langMap = { ar: 'ar-SA', en: 'en-US', fr: 'fr-FR' };
            utterance.lang = langMap[this.currentLanguage];
            utterance.rate = 0.8;
            utterance.pitch = 1.0;
            utterance.volume = 1.0;

            document.getElementById('interviewAvatar').classList.add('speaking');
            document.getElementById('voiceVisualizer').style.display = 'flex';
            this.animateVoiceVisualizer();

            utterance.onend = () => {
                document.getElementById('interviewAvatar').classList.remove('speaking');
                document.getElementById('voiceVisualizer').style.display = 'none';
                this.stopVoiceVisualizer();
                if (callback) callback();
            };

            utterance.onerror = () => {
                document.getElementById('interviewAvatar').classList.remove('speaking');
                document.getElementById('voiceVisualizer').style.display = 'none';
                this.stopVoiceVisualizer();
                if (callback) callback();
            };

            speechSynthesis.speak(utterance);
        } else {
            if (callback) callback();
        }
    }

    restart() {
        // Reset everything
        this.currentQuestion = 0;
        this.answers = [];

        // Show setup again
        document.getElementById('resultsInterface').classList.add('hidden');
        document.getElementById('interviewSetup').classList.remove('hidden');
    }
}

// Initialize the trainer
const trainer = new InterviewTrainer();

// Global functions for buttons
function startInterview() {
    trainer.start();
}

function startRecording() {
    trainer.startRecording();
}

function stopRecording() {
    trainer.stopRecording();
}

function submitAnswer() {
    trainer.submitAnswer();
}

function skipQuestion() {
    trainer.skipQuestion();
}

function restartInterview() {
    trainer.restart();
}

function downloadReport() {
    alert('تقرير المقابلة سيتم تحميله قريباً!');
}

function goHome() {
    window.location.href = '/';
}

// Set default language based on current locale
document.addEventListener('DOMContentLoaded', function() {
    const currentLocale = '{{ app()->getLocale() }}';
    const languageRadio = document.querySelector(`input[name="interviewLanguage"][value="${currentLocale}"]`);
    if (languageRadio) {
        languageRadio.checked = true;
    }
});
</script>
@endsection
