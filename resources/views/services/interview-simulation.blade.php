@extends('layouts.app')

@section('title', __('messages.interview_simulation') . ' - MonOri AI')
@section('description', __('messages.interview_simulation_description'))

@section('content')
<style>
    .interview-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        position: relative;
    }

    .interview-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .ai-avatar {
        width: 120px;
        height: 120px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2rem;
        position: relative;
        animation: pulse 2s infinite;
    }

    .ai-avatar.speaking {
        animation: speaking 0.5s infinite alternate;
        box-shadow: 0 0 30px rgba(102, 126, 234, 0.6);
    }

    @keyframes speaking {
        0% { transform: scale(1); }
        100% { transform: scale(1.05); }
    }

    .voice-visualizer {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 4px;
        height: 40px;
        margin: 1rem 0;
    }

    .voice-bar {
        width: 4px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 2px;
        transition: height 0.1s ease;
    }

    .recording-indicator {
        display: none;
        align-items: center;
        gap: 0.5rem;
        color: #ef4444;
        font-weight: 600;
        margin: 1rem 0;
    }

    .recording-indicator.active {
        display: flex;
    }

    .recording-dot {
        width: 12px;
        height: 12px;
        background: #ef4444;
        border-radius: 50%;
        animation: blink 1s infinite;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }

    .question-card {
        background: #f8fafc;
        border-radius: 16px;
        padding: 2rem;
        margin: 2rem 0;
        border-left: 4px solid #667eea;
    }

    .answer-area {
        background: white;
        border-radius: 16px;
        padding: 1.5rem;
        border: 2px solid #e5e7eb;
        transition: border-color 0.3s ease;
        min-height: 120px;
    }

    .answer-area:focus-within {
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .control-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 12px;
        padding: 1rem 2rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .control-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }

    .control-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .control-button.recording {
        background: linear-gradient(135deg, #ef4444, #dc2626);
    }

    .progress-bar {
        width: 100%;
        height: 8px;
        background: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin: 1rem 0;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(135deg, #667eea, #764ba2);
        transition: width 0.3s ease;
    }

    .feedback-card {
        background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
        border-radius: 16px;
        padding: 2rem;
        margin: 1rem 0;
        border-left: 4px solid #0ea5e9;
    }

    .score-circle {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: conic-gradient(#667eea 0deg, #667eea var(--score-deg), #e5e7eb var(--score-deg), #e5e7eb 360deg);
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
    }

    .score-circle::before {
        content: '';
        width: 60px;
        height: 60px;
        background: white;
        border-radius: 50%;
        position: absolute;
    }

    .score-text {
        position: relative;
        z-index: 1;
        font-weight: bold;
        color: #667eea;
    }
</style>
<!-- Interview Training Container -->
<div class="interview-container">
    <div class="container mx-auto px-4 py-12">
        <!-- Header -->
        <div class="text-center mb-8">
            <h1 class="text-5xl font-bold text-white mb-6">
                @if(app()->getLocale() === 'ar')
                    تدريب مقابلة العمل بالذكاء الاصطناعي
                @elseif(app()->getLocale() === 'fr')
                    Entraînement d'entretien d'embauche avec IA
                @else
                    AI-Powered Interview Training
                @endif
            </h1>
            <p class="text-xl text-white/80 max-w-3xl mx-auto">
                @if(app()->getLocale() === 'ar')
                    تدرب على مقابلات العمل مع مدرب ذكي يستمع لك ويقيم أداءك ويقدم لك نصائح مخصصة
                @elseif(app()->getLocale() === 'fr')
                    Entraînez-vous aux entretiens d'embauche avec un coach intelligent qui vous écoute, évalue votre performance et vous donne des conseils personnalisés
                @else
                    Practice job interviews with an intelligent coach that listens to you, evaluates your performance, and gives you personalized advice
                @endif
            </p>
        </div>

        <!-- Interview Setup -->
        <div class="interview-card max-w-4xl mx-auto p-8" id="interviewSetup">
            <div class="text-center mb-8">
                <div class="ai-avatar" id="aiAvatar">
                    <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">
                    @if(app()->getLocale() === 'ar')
                        مرحباً! أنا مدربك الذكي للمقابلات
                    @elseif(app()->getLocale() === 'fr')
                        Bonjour! Je suis votre coach intelligent pour les entretiens
                    @else
                        Hello! I'm your intelligent interview coach
                    @endif
                </h2>
                <p class="text-gray-600">
                    @if(app()->getLocale() === 'ar')
                        سأطرح عليك أسئلة مقابلة حقيقية وأستمع لإجاباتك وأقيم أداءك
                    @elseif(app()->getLocale() === 'fr')
                        Je vais vous poser de vraies questions d'entretien, écouter vos réponses et évaluer votre performance
                    @else
                        I'll ask you real interview questions, listen to your answers, and evaluate your performance
                    @endif
                </p>
            </div>

            <!-- Interview Configuration -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        @if(app()->getLocale() === 'ar')
                            المنصب المطلوب
                        @elseif(app()->getLocale() === 'fr')
                            Poste souhaité
                        @else
                            Job Position
                        @endif
                    </label>
                    <input type="text" id="jobPosition" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                           placeholder="مطور ويب، مهندس برمجيات، مصمم...">
                </div>

                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        @if(app()->getLocale() === 'ar')
                            نوع المقابلة
                        @elseif(app()->getLocale() === 'fr')
                            Type d'entretien
                        @else
                            Interview Type
                        @endif
                    </label>
                    <select id="interviewType" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option value="technical">
                            @if(app()->getLocale() === 'ar')
                                مقابلة تقنية
                            @elseif(app()->getLocale() === 'fr')
                                Entretien technique
                            @else
                                Technical Interview
                            @endif
                        </option>
                        <option value="hr">
                            @if(app()->getLocale() === 'ar')
                                مقابلة الموارد البشرية
                            @elseif(app()->getLocale() === 'fr')
                                Entretien RH
                            @else
                                HR Interview
                            @endif
                        </option>
                        <option value="behavioral">
                            @if(app()->getLocale() === 'ar')
                                مقابلة سلوكية
                            @elseif(app()->getLocale() === 'fr')
                                Entretien comportemental
                            @else
                                Behavioral Interview
                            @endif
                        </option>
                    </select>
                </div>
            </div>

            <!-- Language Selection -->
            <div class="mb-8">
                <label class="block text-sm font-medium text-gray-700 mb-2">
                    @if(app()->getLocale() === 'ar')
                        لغة المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Langue de l'entretien
                    @else
                        Interview Language
                    @endif
                </label>
                <div class="grid grid-cols-3 gap-4">
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="interviewLanguage" value="ar" class="mr-3" checked>
                        <span>العربية</span>
                    </label>
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="interviewLanguage" value="en" class="mr-3">
                        <span>English</span>
                    </label>
                    <label class="flex items-center p-4 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                        <input type="radio" name="interviewLanguage" value="fr" class="mr-3">
                        <span>Français</span>
                    </label>
                </div>
            </div>

            <!-- Start Button -->
            <div class="text-center">
                <button onclick="startInterview()" class="control-button text-lg px-8 py-4">
                    <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        ابدأ المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Commencer l'entretien
                    @else
                        Start Interview
                    @endif
                </button>
            </div>
        </div>

        <!-- Interview Interface -->
        <div class="interview-card max-w-4xl mx-auto p-8 hidden" id="interviewInterface">
            <!-- Progress Bar -->
            <div class="mb-6">
                <div class="flex justify-between items-center mb-2">
                    <span class="text-sm font-medium text-gray-700">
                        @if(app()->getLocale() === 'ar')
                            التقدم
                        @elseif(app()->getLocale() === 'fr')
                            Progrès
                        @else
                            Progress
                        @endif
                    </span>
                    <span class="text-sm text-gray-500" id="questionCounter">1 / 5</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="progressFill" style="width: 20%"></div>
                </div>
            </div>

            <!-- AI Avatar -->
            <div class="text-center mb-6">
                <div class="ai-avatar" id="interviewAvatar">
                    <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                    </svg>
                </div>

                <!-- Voice Visualizer -->
                <div class="voice-visualizer" id="voiceVisualizer">
                    <div class="voice-bar" style="height: 20px;"></div>
                    <div class="voice-bar" style="height: 30px;"></div>
                    <div class="voice-bar" style="height: 25px;"></div>
                    <div class="voice-bar" style="height: 35px;"></div>
                    <div class="voice-bar" style="height: 20px;"></div>
                    <div class="voice-bar" style="height: 40px;"></div>
                    <div class="voice-bar" style="height: 25px;"></div>
                </div>
            </div>

            <!-- Question Card -->
            <div class="question-card">
                <h3 class="text-lg font-semibold text-gray-800 mb-4">
                    @if(app()->getLocale() === 'ar')
                        السؤال:
                    @elseif(app()->getLocale() === 'fr')
                        Question:
                    @else
                        Question:
                    @endif
                </h3>
                <p class="text-gray-700 text-lg leading-relaxed" id="currentQuestion">
                    حدثني عن نفسك وخبراتك المهنية
                </p>
            </div>

            <!-- Recording Indicator -->
            <div class="recording-indicator" id="recordingIndicator">
                <div class="recording-dot"></div>
                <span>
                    @if(app()->getLocale() === 'ar')
                        جاري التسجيل...
                    @elseif(app()->getLocale() === 'fr')
                        Enregistrement en cours...
                    @else
                        Recording...
                    @endif
                </span>
            </div>

            <!-- Answer Area -->
            <div class="answer-area mb-6">
                <textarea id="answerText" class="w-full h-32 border-none outline-none resize-none"
                          placeholder="اكتب إجابتك هنا أو استخدم التسجيل الصوتي..."></textarea>
            </div>

            <!-- Controls -->
            <div class="flex flex-wrap gap-4 justify-center">
                <button onclick="startRecording()" id="recordButton" class="control-button">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        تسجيل صوتي
                    @elseif(app()->getLocale() === 'fr')
                        Enregistrer
                    @else
                        Record Audio
                    @endif
                </button>

                <button onclick="stopRecording()" id="stopButton" class="control-button recording hidden">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 6h12v12H6z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        إيقاف التسجيل
                    @elseif(app()->getLocale() === 'fr')
                        Arrêter
                    @else
                        Stop Recording
                    @endif
                </button>

                <button onclick="submitAnswer()" id="submitButton" class="control-button">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        إرسال الإجابة
                    @elseif(app()->getLocale() === 'fr')
                        Envoyer la réponse
                    @else
                        Submit Answer
                    @endif
                </button>

                <button onclick="skipQuestion()" class="control-button" style="background: #6b7280;">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        تخطي السؤال
                    @elseif(app()->getLocale() === 'fr')
                        Passer la question
                    @else
                        Skip Question
                    @endif
                </button>
            </div>
        </div>

        <!-- Results Interface -->
        <div class="interview-card max-w-4xl mx-auto p-8 hidden" id="resultsInterface">
            <div class="text-center mb-8">
                <div class="ai-avatar">
                    <svg class="w-16 h-16 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">
                    @if(app()->getLocale() === 'ar')
                        تحليل أداء المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Analyse de performance d'entretien
                    @else
                        Interview Performance Analysis
                    @endif
                </h2>
            </div>

            <!-- Overall Score -->
            <div class="text-center mb-8">
                <div class="score-circle mx-auto mb-4" style="--score-deg: 252deg;">
                    <div class="score-text text-2xl">85%</div>
                </div>
                <p class="text-gray-600">
                    @if(app()->getLocale() === 'ar')
                        النتيجة الإجمالية
                    @elseif(app()->getLocale() === 'fr')
                        Score global
                    @else
                        Overall Score
                    @endif
                </p>
            </div>

            <!-- Feedback Cards -->
            <div class="space-y-4 mb-8">
                <div class="feedback-card">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        @if(app()->getLocale() === 'ar')
                            نقاط القوة
                        @elseif(app()->getLocale() === 'fr')
                            Points forts
                        @else
                            Strengths
                        @endif
                    </h3>
                    <ul class="text-gray-700 space-y-1" id="strengthsList">
                        <li>• وضوح في التعبير والتواصل</li>
                        <li>• ثقة جيدة في الإجابات</li>
                        <li>• أمثلة عملية مناسبة</li>
                    </ul>
                </div>

                <div class="feedback-card" style="background: linear-gradient(135deg, #fef3c7, #fde68a); border-left-color: #f59e0b;">
                    <h3 class="font-semibold text-gray-800 mb-2">
                        @if(app()->getLocale() === 'ar')
                            نقاط للتحسين
                        @elseif(app()->getLocale() === 'fr')
                            Points à améliorer
                        @else
                            Areas for Improvement
                        @endif
                    </h3>
                    <ul class="text-gray-700 space-y-1" id="improvementsList">
                        <li>• تطوير الإجابات بتفاصيل أكثر</li>
                        <li>• استخدام طريقة STAR في الإجابات</li>
                        <li>• التحضير لأسئلة تقنية أكثر تعمقاً</li>
                    </ul>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex flex-wrap gap-4 justify-center">
                <button onclick="retryInterview()" class="control-button">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        إعادة المقابلة
                    @elseif(app()->getLocale() === 'fr')
                        Refaire l'entretien
                    @else
                        Retry Interview
                    @endif
                </button>

                <button onclick="downloadReport()" class="control-button" style="background: linear-gradient(135deg, #059669, #047857);">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        تحميل التقرير
                    @elseif(app()->getLocale() === 'fr')
                        Télécharger le rapport
                    @else
                        Download Report
                    @endif
                </button>

                <a href="{{ route('home') }}" class="control-button" style="background: linear-gradient(135deg, #6b7280, #4b5563);">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z"/>
                    </svg>
                    @if(app()->getLocale() === 'ar')
                        العودة للرئيسية
                    @elseif(app()->getLocale() === 'fr')
                        Retour à l'accueil
                    @else
                        Back to Home
                    @endif
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Interview Training JavaScript
let currentQuestionIndex = 0;
let totalQuestions = 5;
let answers = [];
let isRecording = false;
let recognition;
let interviewConfig = {};

// Interview questions database
const questionDatabase = {
    ar: {
        technical: [
            "حدثني عن خبرتك في البرمجة والتقنيات التي تستخدمها",
            "كيف تتعامل مع تحدي تقني صعب في العمل؟",
            "اشرح لي مشروع تقني عملت عليه مؤخراً",
            "ما هي أفضل الممارسات في كتابة الكود؟",
            "كيف تتعامل مع اختبار وتصحيح الأخطاء في البرمجيات؟"
        ],
        hr: [
            "حدثني عن نفسك وخبراتك المهنية",
            "لماذا تريد العمل في شركتنا؟",
            "ما هي نقاط قوتك ونقاط ضعفك؟",
            "أين ترى نفسك خلال 5 سنوات؟",
            "لماذا تريد ترك وظيفتك الحالية؟"
        ],
        behavioral: [
            "حدثني عن موقف واجهت فيه تحدياً كبيراً في العمل",
            "اذكر مثالاً على وقت عملت فيه ضمن فريق",
            "كيف تتعامل مع الصراعات في مكان العمل؟",
            "حدثني عن مشروع فشل وكيف تعاملت معه",
            "اذكر موقفاً أظهرت فيه مهارات قيادية"
        ]
    },
    en: {
        technical: [
            "Tell me about your programming experience and technologies you use",
            "How do you handle a difficult technical challenge at work?",
            "Explain a technical project you worked on recently",
            "What are the best practices in writing code?",
            "How do you handle testing and debugging in software?"
        ],
        hr: [
            "Tell me about yourself and your professional experience",
            "Why do you want to work at our company?",
            "What are your strengths and weaknesses?",
            "Where do you see yourself in 5 years?",
            "Why do you want to leave your current job?"
        ],
        behavioral: [
            "Tell me about a time you faced a major challenge at work",
            "Give me an example of when you worked in a team",
            "How do you handle conflicts in the workplace?",
            "Tell me about a project that failed and how you dealt with it",
            "Describe a situation where you showed leadership skills"
        ]
    },
    fr: {
        technical: [
            "Parlez-moi de votre expérience en programmation et des technologies que vous utilisez",
            "Comment gérez-vous un défi technique difficile au travail?",
            "Expliquez-moi un projet technique sur lequel vous avez travaillé récemment",
            "Quelles sont les meilleures pratiques pour écrire du code?",
            "Comment gérez-vous les tests et le débogage dans les logiciels?"
        ],
        hr: [
            "Parlez-moi de vous et de votre expérience professionnelle",
            "Pourquoi voulez-vous travailler dans notre entreprise?",
            "Quels sont vos points forts et vos points faibles?",
            "Où vous voyez-vous dans 5 ans?",
            "Pourquoi voulez-vous quitter votre emploi actuel?"
        ],
        behavioral: [
            "Parlez-moi d'une fois où vous avez fait face à un défi majeur au travail",
            "Donnez-moi un exemple de quand vous avez travaillé en équipe",
            "Comment gérez-vous les conflits sur le lieu de travail?",
            "Parlez-moi d'un projet qui a échoué et comment vous l'avez géré",
            "Décrivez une situation où vous avez montré des compétences de leadership"
        ]
    }
};

// Initialize Speech Recognition
function initializeSpeechRecognition() {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
        const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
        recognition = new SpeechRecognition();

        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.lang = interviewConfig.language === 'ar' ? 'ar-SA' :
                          interviewConfig.language === 'fr' ? 'fr-FR' : 'en-US';

        recognition.onstart = function() {
            isRecording = true;
            document.getElementById('recordingIndicator').classList.add('active');
            document.getElementById('recordButton').classList.add('hidden');
            document.getElementById('stopButton').classList.remove('hidden');
            animateVoiceVisualizer();
        };

        recognition.onresult = function(event) {
            let finalTranscript = '';
            let interimTranscript = '';

            for (let i = event.resultIndex; i < event.results.length; i++) {
                const transcript = event.results[i][0].transcript;
                if (event.results[i].isFinal) {
                    finalTranscript += transcript;
                } else {
                    interimTranscript += transcript;
                }
            }

            const answerText = document.getElementById('answerText');
            answerText.value = finalTranscript + interimTranscript;
        };

        recognition.onerror = function(event) {
            console.error('Speech recognition error:', event.error);
            stopRecording();
        };

        recognition.onend = function() {
            if (isRecording) {
                recognition.start(); // Restart if still recording
            }
        };
    } else {
        console.warn('Speech recognition not supported');
        document.getElementById('recordButton').style.display = 'none';
    }
}

// Start Interview
function startInterview() {
    // Get configuration
    interviewConfig = {
        jobPosition: document.getElementById('jobPosition').value || 'مطور ويب',
        interviewType: document.getElementById('interviewType').value,
        language: document.querySelector('input[name="interviewLanguage"]:checked').value
    };

    // Initialize speech recognition
    initializeSpeechRecognition();

    // Reset variables
    currentQuestionIndex = 0;
    answers = [];

    // Hide setup, show interview
    document.getElementById('interviewSetup').classList.add('hidden');
    document.getElementById('interviewInterface').classList.remove('hidden');

    // Load first question
    loadQuestion();

    // Speak welcome message
    speakText(getWelcomeMessage());
}

// Load Question
function loadQuestion() {
    const questions = questionDatabase[interviewConfig.language][interviewConfig.interviewType];
    const question = questions[currentQuestionIndex];

    document.getElementById('currentQuestion').textContent = question;
    document.getElementById('questionCounter').textContent = `${currentQuestionIndex + 1} / ${totalQuestions}`;

    // Update progress
    const progress = ((currentQuestionIndex + 1) / totalQuestions) * 100;
    document.getElementById('progressFill').style.width = progress + '%';

    // Clear previous answer
    document.getElementById('answerText').value = '';

    // Speak question
    setTimeout(() => {
        speakText(question);
        animateAISpeaking();
    }, 1000);
}

// Start Recording
function startRecording() {
    if (recognition) {
        recognition.start();
    }
}

// Stop Recording
function stopRecording() {
    isRecording = false;
    if (recognition) {
        recognition.stop();
    }

    document.getElementById('recordingIndicator').classList.remove('active');
    document.getElementById('recordButton').classList.remove('hidden');
    document.getElementById('stopButton').classList.add('hidden');
    stopVoiceVisualizer();
}

// Submit Answer
function submitAnswer() {
    const answerText = document.getElementById('answerText').value.trim();

    if (!answerText) {
        alert('يرجى كتابة إجابة أو استخدام التسجيل الصوتي');
        return;
    }

    // Save answer
    answers.push({
        question: document.getElementById('currentQuestion').textContent,
        answer: answerText,
        timestamp: new Date()
    });

    // Move to next question or finish
    if (currentQuestionIndex < totalQuestions - 1) {
        currentQuestionIndex++;
        loadQuestion();
    } else {
        finishInterview();
    }
}

// Skip Question
function skipQuestion() {
    answers.push({
        question: document.getElementById('currentQuestion').textContent,
        answer: '[تم تخطي السؤال]',
        timestamp: new Date()
    });

    if (currentQuestionIndex < totalQuestions - 1) {
        currentQuestionIndex++;
        loadQuestion();
    } else {
        finishInterview();
    }
}

// Finish Interview
function finishInterview() {
    // Hide interview interface
    document.getElementById('interviewInterface').classList.add('hidden');

    // Show loading
    showAnalysisLoading();

    // Simulate AI analysis
    setTimeout(() => {
        hideAnalysisLoading();
        showResults();
    }, 3000);
}

// Show Analysis Loading
function showAnalysisLoading() {
    const loadingHTML = `
        <div class="interview-card max-w-4xl mx-auto p-8" id="analysisLoading">
            <div class="text-center">
                <div class="ai-avatar speaking">
                    <svg class="w-16 h-16 text-white animate-spin" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 4V2A10 10 0 0 0 2 12h2a8 8 0 0 1 8-8z"/>
                    </svg>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">جاري تحليل أداءك...</h2>
                <p class="text-gray-600 mb-6">الذكاء الاصطناعي يحلل إجاباتك ويقيم أداءك</p>
                <div class="progress-bar">
                    <div class="progress-fill animate-pulse" style="width: 70%"></div>
                </div>
            </div>
        </div>
    `;

    document.querySelector('.interview-container .container').insertAdjacentHTML('beforeend', loadingHTML);
}

// Hide Analysis Loading
function hideAnalysisLoading() {
    const loading = document.getElementById('analysisLoading');
    if (loading) {
        loading.remove();
    }
}

// Show Results
function showResults() {
    // Generate AI feedback (simulate)
    generateAIFeedback();

    // Show results interface
    document.getElementById('resultsInterface').classList.remove('hidden');
}

// Generate AI Feedback
function generateAIFeedback() {
    // Simulate AI analysis based on answers
    const score = Math.floor(Math.random() * 30) + 70; // 70-100

    // Update score circle
    const scoreCircle = document.querySelector('.score-circle');
    const scoreDeg = (score / 100) * 360;
    scoreCircle.style.setProperty('--score-deg', scoreDeg + 'deg');
    scoreCircle.querySelector('.score-text').textContent = score + '%';

    // Generate feedback based on language
    const feedback = generateFeedbackByLanguage(interviewConfig.language);

    // Update strengths
    const strengthsList = document.getElementById('strengthsList');
    strengthsList.innerHTML = feedback.strengths.map(s => `<li>• ${s}</li>`).join('');

    // Update improvements
    const improvementsList = document.getElementById('improvementsList');
    improvementsList.innerHTML = feedback.improvements.map(i => `<li>• ${i}</li>`).join('');
}

// Generate Feedback by Language
function generateFeedbackByLanguage(language) {
    const feedbacks = {
        ar: {
            strengths: [
                'وضوح في التعبير والتواصل',
                'ثقة جيدة في الإجابات',
                'أمثلة عملية مناسبة',
                'تنظيم جيد للأفكار'
            ],
            improvements: [
                'تطوير الإجابات بتفاصيل أكثر',
                'استخدام طريقة STAR في الإجابات',
                'التحضير لأسئلة تقنية أكثر تعمقاً',
                'تحسين لغة الجسد والثقة'
            ]
        },
        en: {
            strengths: [
                'Clear communication and expression',
                'Good confidence in answers',
                'Appropriate practical examples',
                'Good organization of ideas'
            ],
            improvements: [
                'Develop answers with more details',
                'Use STAR method in responses',
                'Prepare for more in-depth technical questions',
                'Improve body language and confidence'
            ]
        },
        fr: {
            strengths: [
                'Communication claire et expression',
                'Bonne confiance dans les réponses',
                'Exemples pratiques appropriés',
                'Bonne organisation des idées'
            ],
            improvements: [
                'Développer les réponses avec plus de détails',
                'Utiliser la méthode STAR dans les réponses',
                'Se préparer à des questions techniques plus approfondies',
                'Améliorer le langage corporel et la confiance'
            ]
        }
    };

    return feedbacks[language] || feedbacks.ar;
}

// Utility Functions
function speakText(text) {
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = interviewConfig.language === 'ar' ? 'ar-SA' :
                        interviewConfig.language === 'fr' ? 'fr-FR' : 'en-US';
        utterance.rate = 0.9;
        speechSynthesis.speak(utterance);
    }
}

function animateAISpeaking() {
    const avatar = document.getElementById('interviewAvatar') || document.getElementById('aiAvatar');
    avatar.classList.add('speaking');
    setTimeout(() => {
        avatar.classList.remove('speaking');
    }, 2000);
}

function animateVoiceVisualizer() {
    const bars = document.querySelectorAll('.voice-bar');
    const interval = setInterval(() => {
        if (!isRecording) {
            clearInterval(interval);
            return;
        }

        bars.forEach(bar => {
            const height = Math.random() * 40 + 10;
            bar.style.height = height + 'px';
        });
    }, 100);
}

function stopVoiceVisualizer() {
    const bars = document.querySelectorAll('.voice-bar');
    bars.forEach(bar => {
        bar.style.height = '20px';
    });
}

function getWelcomeMessage() {
    const messages = {
        ar: `مرحباً! سنبدأ مقابلة ${interviewConfig.interviewType} لمنصب ${interviewConfig.jobPosition}. استعد للسؤال الأول.`,
        en: `Hello! We'll start a ${interviewConfig.interviewType} interview for ${interviewConfig.jobPosition} position. Get ready for the first question.`,
        fr: `Bonjour! Nous allons commencer un entretien ${interviewConfig.interviewType} pour le poste de ${interviewConfig.jobPosition}. Préparez-vous pour la première question.`
    };

    return messages[interviewConfig.language] || messages.ar;
}

// Action Functions
function retryInterview() {
    // Reset everything
    document.getElementById('resultsInterface').classList.add('hidden');
    document.getElementById('interviewSetup').classList.remove('hidden');
}

function downloadReport() {
    // Simulate report download
    alert('سيتم تحميل تقرير المقابلة الكامل قريباً!');
}

// Initialize on page load
document.addEventListener('DOMContentLoaded', function() {
    // Set default language based on current locale
    const currentLocale = '{{ app()->getLocale() }}';
    const languageRadio = document.querySelector(`input[name="interviewLanguage"][value="${currentLocale}"]`);
    if (languageRadio) {
        languageRadio.checked = true;
    }
});
</script>
@endsection
            <!-- Technical Interview -->
            <div class="card-hover bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">مقابلة تقنية</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    أسئلة تقنية متخصصة في البرمجة وتطوير البرمجيات
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 30-45 دقيقة</span>
                    <button onclick="startTechnicalInterview()" class="text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300">
                        ابدأ الآن →
                    </button>
                </div>
            </div>

            <!-- HR Interview -->
            <div class="card-hover bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">مقابلة الموارد البشرية</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    أسئلة عامة حول الشخصية والخبرة والدافعية
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 20-30 دقيقة</span>
                    <button onclick="startHRInterview()" class="text-green-600 dark:text-green-400 font-semibold hover:text-green-700 dark:hover:text-green-300">
                        ابدأ الآن →
                    </button>
                </div>
            </div>

            <!-- Behavioral Interview -->
            <div class="card-hover bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">مقابلة سلوكية</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    أسئلة حول المواقف والتجارب السابقة والتعامل مع التحديات
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 25-35 دقيقة</span>
                    <button onclick="startBehavioralInterview()" class="text-purple-600 dark:text-purple-400 font-semibold hover:text-purple-700 dark:hover:text-purple-300">
                        ابدأ الآن →
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Interview Setup -->
<section id="interview-setup" class="hidden py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">إعداد المقابلة</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">اختر إعدادات المقابلة التي تناسبك</p>
        </div>

        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
            <form id="interview-setup-form" class="space-y-8">
                <!-- Job Position -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">المنصب المستهدف</label>
                    <input type="text" placeholder="مثال: مطور ويب، مدير مشاريع، مصمم جرافيك" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                </div>

                <!-- Experience Level -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مستوى الخبرة</label>
                    <select class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white">
                        <option value="">اختر مستوى خبرتك</option>
                        <option value="entry">مبتدئ (0-2 سنة)</option>
                        <option value="mid">متوسط (2-5 سنوات)</option>
                        <option value="senior">خبير (5+ سنوات)</option>
                        <option value="lead">قيادي (8+ سنوات)</option>
                    </select>
                </div>

                <!-- Interview Duration -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">مدة المقابلة</label>
                    <div class="grid grid-cols-3 gap-4">
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="duration" value="15" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">15 دقيقة</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="duration" value="30" class="mr-3 rtl:ml-3 rtl:mr-0" checked>
                            <span class="text-gray-700 dark:text-gray-300">30 دقيقة</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="duration" value="45" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">45 دقيقة</span>
                        </label>
                    </div>
                </div>

                <!-- Interview Language -->
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">لغة المقابلة</label>
                    <div class="grid grid-cols-3 gap-4">
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="language" value="ar" class="mr-3 rtl:ml-3 rtl:mr-0" checked>
                            <span class="text-gray-700 dark:text-gray-300">العربية</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="language" value="en" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">English</span>
                        </label>
                        <label class="flex items-center p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer">
                            <input type="radio" name="language" value="fr" class="mr-3 rtl:ml-3 rtl:mr-0">
                            <span class="text-gray-700 dark:text-gray-300">Français</span>
                        </label>
                    </div>
                </div>

                <!-- Start Interview Button -->
                <div class="text-center pt-8">
                    <button type="submit" class="btn-primary text-white px-12 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <span>ابدأ المقابلة الآن</span>
                    </button>
                </div>
            </form>
        </div>
    </div>
</section>

<!-- Interview Interface -->
<section id="interview-interface" class="hidden py-20 bg-white dark:bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Interview Header -->
        <div class="bg-gradient-to-r from-purple-500 to-pink-600 rounded-2xl p-6 mb-8 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold mb-2">مقابلة عمل تفاعلية</h2>
                    <p class="opacity-90" id="interview-position">مطور ويب - مستوى متوسط</p>
                </div>
                <div class="text-right">
                    <div class="text-3xl font-bold" id="timer">30:00</div>
                    <div class="text-sm opacity-90">الوقت المتبقي</div>
                </div>
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- AI Interviewer -->
            <div class="lg:col-span-2">
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-6">
                    <!-- AI Avatar -->
                    <div class="flex items-start space-x-4 rtl:space-x-reverse mb-6">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                            </svg>
                        </div>
                        <div class="flex-1">
                            <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-2">المحاور الذكي</h3>
                            <div class="bg-gray-50 dark:bg-gray-700 rounded-xl p-4">
                                <p class="text-gray-700 dark:text-gray-300" id="ai-question">
                                    مرحباً بك! أنا سعيد لإجراء هذه المقابلة معك اليوم. دعنا نبدأ بسؤال بسيط: حدثني عن نفسك وخبرتك في مجال تطوير الويب.
                                </p>
                            </div>
                        </div>
                    </div>

                    <!-- Response Area -->
                    <div class="space-y-4">
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">إجابتك:</label>
                        <textarea id="user-response" rows="6" class="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent dark:bg-gray-700 dark:text-white resize-none" placeholder="اكتب إجابتك هنا..."></textarea>
                        
                        <!-- Voice Recording -->
                        <div class="flex items-center space-x-4 rtl:space-x-reverse">
                            <button id="record-btn" onclick="toggleRecording()" class="flex items-center space-x-2 rtl:space-x-reverse bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors duration-200">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                                </svg>
                                <span>تسجيل صوتي</span>
                            </button>
                            <button onclick="submitAnswer()" class="btn-primary text-white px-6 py-2 rounded-lg">
                                إرسال الإجابة
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Interview Progress -->
            <div class="space-y-6">
                <!-- Progress Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">تقدم المقابلة</h3>
                    <div class="space-y-4">
                        <div>
                            <div class="flex justify-between text-sm text-gray-600 dark:text-gray-400 mb-1">
                                <span>الأسئلة المجابة</span>
                                <span id="progress-text">1 من 8</span>
                            </div>
                            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                                <div class="bg-gradient-to-r from-purple-500 to-pink-600 h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 12.5%"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tips Card -->
                <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6">
                    <h3 class="text-lg font-bold text-gray-900 dark:text-white mb-4">نصائح للمقابلة</h3>
                    <ul class="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            كن واضحاً ومحدداً في إجاباتك
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            استخدم أمثلة من تجاربك السابقة
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            اظهر حماسك للمنصب
                        </li>
                        <li class="flex items-start">
                            <svg class="w-4 h-4 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            لا تخف من طرح الأسئلة
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Interview Results -->
<section id="interview-results" class="hidden py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">نتائج المقابلة</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">تحليل شامل لأدائك في المقابلة</p>
        </div>

        <!-- Overall Score -->
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 mb-8">
            <div class="text-center mb-8">
                <div class="relative w-32 h-32 mx-auto mb-4">
                    <svg class="w-32 h-32 transform -rotate-90" viewBox="0 0 36 36">
                        <path class="text-gray-300 dark:text-gray-600" stroke="currentColor" stroke-width="3" fill="none" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                        <path class="text-purple-500" stroke="currentColor" stroke-width="3" fill="none" stroke-dasharray="82, 100" d="M18 2.0845 a 15.9155 15.9155 0 0 1 0 31.831 a 15.9155 15.9155 0 0 1 0 -31.831"></path>
                    </svg>
                    <div class="absolute inset-0 flex items-center justify-center">
                        <span class="text-3xl font-bold text-gray-900 dark:text-white">82%</span>
                    </div>
                </div>
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">أداء ممتاز!</h3>
                <p class="text-gray-600 dark:text-gray-300">لقد أظهرت مهارات قوية في المقابلة</p>
            </div>
        </div>

        <!-- Detailed Feedback -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            <!-- Strengths -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                    </svg>
                    نقاط القوة
                </h3>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">إجابات واضحة ومنظمة</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">استخدام أمثلة محددة</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">ثقة في التعبير</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">معرفة جيدة بالمجال</span>
                    </li>
                </ul>
            </div>

            <!-- Areas for Improvement -->
            <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
                    <svg class="w-6 h-6 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                    مجالات التحسين
                </h3>
                <ul class="space-y-3">
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">تحسين لغة الجسد</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">طرح أسئلة أكثر</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">تقليل الكلمات الحشو</span>
                    </li>
                    <li class="flex items-start">
                        <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                        </svg>
                        <span class="text-gray-700 dark:text-gray-300">إظهار المزيد من الحماس</span>
                    </li>
                </ul>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center">
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="downloadInterviewReport()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    <span>تحميل تقرير المقابلة</span>
                </button>
                <button onclick="retakeInterview()" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-400 transition-all duration-300">
                    إعادة المقابلة
                </button>
                <button onclick="tryDifferentType()" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200">
                    جرب نوع مقابلة آخر
                </button>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
// Interview Simulation Functions
let interviewType = '';
let currentQuestion = 0;
let interviewQuestions = [];
let userAnswers = [];
let interviewTimer = null;
let timeRemaining = 1800; // 30 minutes default
let isRecording = false;

// Interview Questions Database
const questionDatabase = {
    technical: [
        "حدثني عن نفسك وخبرتك في البرمجة",
        "ما هي لغات البرمجة التي تجيدها؟",
        "كيف تتعامل مع debugging في الكود؟",
        "اشرح لي مفهوم Object-Oriented Programming",
        "ما هو الفرق بين SQL و NoSQL؟",
        "كيف تضمن أمان التطبيق الذي تطوره؟",
        "اشرح لي عملية Git workflow",
        "ما هي أفضل الممارسات في كتابة الكود؟"
    ],
    hr: [
        "حدثني عن نفسك",
        "لماذا تريد العمل في شركتنا؟",
        "ما هي نقاط قوتك وضعفك؟",
        "أين ترى نفسك خلال 5 سنوات؟",
        "لماذا تريد ترك وظيفتك الحالية؟",
        "كيف تتعامل مع ضغط العمل؟",
        "ما هو راتبك المتوقع؟",
        "هل لديك أي أسئلة لنا؟"
    ],
    behavioral: [
        "حدثني عن موقف واجهت فيه تحدياً كبيراً في العمل",
        "اذكر مثالاً على وقت عملت فيه ضمن فريق",
        "كيف تتعامل مع الصراعات في مكان العمل؟",
        "حدثني عن مشروع فشل وكيف تعاملت معه",
        "اذكر موقفاً أظهرت فيه مهارات قيادية",
        "كيف تتعامل مع المواعيد النهائية الضيقة؟",
        "حدثني عن وقت اتخذت فيه قراراً صعباً",
        "كيف تتعامل مع التغيير في بيئة العمل؟"
    ]
};

function startInterview() {
    document.getElementById('interview-setup').classList.remove('hidden');
    document.getElementById('interview-setup').scrollIntoView({ behavior: 'smooth' });
}

function showInterviewTypes() {
    // Scroll to interview types section
    document.querySelector('.py-20.bg-white.dark\\:bg-gray-900').scrollIntoView({ behavior: 'smooth' });
}

function startTechnicalInterview() {
    interviewType = 'technical';
    startInterview();
}

function startHRInterview() {
    interviewType = 'hr';
    startInterview();
}

function startBehavioralInterview() {
    interviewType = 'behavioral';
    startInterview();
}

// Setup form submission
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('interview-setup-form').addEventListener('submit', function(e) {
        e.preventDefault();

        // Get form data
        const formData = new FormData(this);
        const duration = parseInt(formData.get('duration')) || 30;
        timeRemaining = duration * 60;

        // Initialize interview
        initializeInterview();
    });
});

function initializeInterview() {
    // Get questions for the interview type
    interviewQuestions = questionDatabase[interviewType] || questionDatabase.hr;
    currentQuestion = 0;
    userAnswers = [];

    // Show interview interface
    document.getElementById('interview-setup').classList.add('hidden');
    document.getElementById('interview-interface').classList.remove('hidden');
    document.getElementById('interview-interface').scrollIntoView({ behavior: 'smooth' });

    // Start timer
    startTimer();

    // Load first question
    loadQuestion();
}

function startTimer() {
    interviewTimer = setInterval(() => {
        timeRemaining--;
        updateTimerDisplay();

        if (timeRemaining <= 0) {
            endInterview();
        }
    }, 1000);
}

function updateTimerDisplay() {
    const minutes = Math.floor(timeRemaining / 60);
    const seconds = timeRemaining % 60;
    document.getElementById('timer').textContent =
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
}

function loadQuestion() {
    if (currentQuestion < interviewQuestions.length) {
        document.getElementById('ai-question').textContent = interviewQuestions[currentQuestion];
        document.getElementById('user-response').value = '';

        // Update progress
        const progress = ((currentQuestion + 1) / interviewQuestions.length) * 100;
        document.getElementById('progress-bar').style.width = progress + '%';
        document.getElementById('progress-text').textContent =
            `${currentQuestion + 1} من ${interviewQuestions.length}`;
    } else {
        endInterview();
    }
}

function submitAnswer() {
    const answer = document.getElementById('user-response').value.trim();

    if (!answer) {
        alert('يرجى كتابة إجابتك قبل المتابعة');
        return;
    }

    // Save answer
    userAnswers[currentQuestion] = {
        question: interviewQuestions[currentQuestion],
        answer: answer,
        timestamp: new Date()
    };

    // Move to next question
    currentQuestion++;

    // Add delay for realistic feel
    setTimeout(() => {
        loadQuestion();
    }, 1000);
}

function toggleRecording() {
    const recordBtn = document.getElementById('record-btn');

    if (!isRecording) {
        // Start recording
        isRecording = true;
        recordBtn.innerHTML = `
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 10h6v4H9z"></path>
            </svg>
            <span>إيقاف التسجيل</span>
        `;
        recordBtn.classList.remove('bg-red-500', 'hover:bg-red-600');
        recordBtn.classList.add('bg-gray-500', 'hover:bg-gray-600');

        // Simulate recording (in real app, would use Web Audio API)
        alert('بدأ التسجيل الصوتي...');
    } else {
        // Stop recording
        isRecording = false;
        recordBtn.innerHTML = `
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
            </svg>
            <span>تسجيل صوتي</span>
        `;
        recordBtn.classList.remove('bg-gray-500', 'hover:bg-gray-600');
        recordBtn.classList.add('bg-red-500', 'hover:bg-red-600');

        alert('تم إيقاف التسجيل. يمكنك الآن كتابة إجابتك أو إرسالها.');
    }
}

function endInterview() {
    // Stop timer
    if (interviewTimer) {
        clearInterval(interviewTimer);
    }

    // Show loading for analysis
    showInterviewAnalysisLoading();

    // Simulate AI analysis
    setTimeout(() => {
        hideInterviewAnalysisLoading();
        showInterviewResults();
    }, 3000);
}

function showInterviewAnalysisLoading() {
    const loadingHTML = `
        <div id="interview-analysis-loading" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="animate-spin w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">جاري تحليل أدائك</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">الذكاء الاصطناعي يحلل إجاباتك ويقيم أداءك...</p>
                <div class="text-sm text-gray-500 dark:text-gray-400" id="interview-analysis-step">تحليل محتوى الإجابات...</div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loadingHTML);

    // Simulate analysis steps
    const steps = [
        "تحليل محتوى الإجابات...",
        "تقييم وضوح التعبير...",
        "تحليل المهارات التقنية...",
        "تقييم الثقة والحماس...",
        "إنشاء التوصيات والملاحظات..."
    ];

    let stepIndex = 0;
    const stepInterval = setInterval(() => {
        stepIndex++;
        if (stepIndex < steps.length) {
            document.getElementById('interview-analysis-step').textContent = steps[stepIndex];
        } else {
            clearInterval(stepInterval);
        }
    }, 600);
}

function hideInterviewAnalysisLoading() {
    const loadingElement = document.getElementById('interview-analysis-loading');
    if (loadingElement) {
        loadingElement.remove();
    }
}

function showInterviewResults() {
    document.getElementById('interview-interface').classList.add('hidden');
    document.getElementById('interview-results').classList.remove('hidden');
    document.getElementById('interview-results').scrollIntoView({ behavior: 'smooth' });
}

function downloadInterviewReport() {
    alert('سيتم تحميل تقرير المقابلة الكامل قريباً!');
}

function retakeInterview() {
    // Reset interview
    currentQuestion = 0;
    userAnswers = [];
    document.getElementById('interview-results').classList.add('hidden');
    initializeInterview();
}

function tryDifferentType() {
    // Reset and go back to setup
    currentQuestion = 0;
    userAnswers = [];
    document.getElementById('interview-results').classList.add('hidden');
    document.getElementById('interview-setup').classList.remove('hidden');
    document.getElementById('interview-setup').scrollIntoView({ behavior: 'smooth' });
}
</script>
@endpush
@endsection
