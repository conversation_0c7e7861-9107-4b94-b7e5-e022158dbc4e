@extends('layouts.app')

@section('title', __('messages.service_history') . ' - MonOri AI')
@section('description', __('messages.service_history_description'))

@section('content')
<!-- Header -->
<section class="py-12 bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-500">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center">
            <h1 class="text-4xl font-bold text-white mb-4">{{ __('messages.service_history') }}</h1>
            <p class="text-xl text-white/90">{{ __('messages.service_history_description') }}</p>
        </div>
    </div>
</section>

<!-- History Section -->
<section class="py-12 bg-gray-50 dark:bg-gray-900">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        
        @if($results->count() > 0)
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($results as $result)
                    <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-6 hover:shadow-xl transition-shadow duration-300">
                        <!-- Service Type Icon -->
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-gradient-to-br 
                                @if($result->service_type === 'personality_analysis') from-blue-500 to-purple-500
                                @elseif($result->service_type === 'cv_improvement') from-green-500 to-blue-500
                                @else from-orange-500 to-red-500
                                @endif
                                rounded-xl flex items-center justify-center mr-3">
                                @if($result->service_type === 'personality_analysis')
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                @elseif($result->service_type === 'cv_improvement')
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                @else
                                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                    </svg>
                                @endif
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 dark:text-white">{{ $result->title }}</h3>
                                <span class="text-sm text-gray-500 dark:text-gray-400">{{ $result->created_at->format('M d, Y') }}</span>
                            </div>
                        </div>

                        <!-- Description -->
                        <p class="text-gray-600 dark:text-gray-300 text-sm mb-4">
                            {{ Str::limit($result->description, 100) }}
                        </p>

                        <!-- Score and Status -->
                        <div class="flex items-center justify-between mb-4">
                            @if($result->score)
                                <div class="flex items-center">
                                    <span class="text-sm text-gray-500 dark:text-gray-400 mr-2">{{ __('messages.score') }}:</span>
                                    <span class="font-semibold 
                                        @if($result->score >= 80) text-green-600 dark:text-green-400
                                        @elseif($result->score >= 60) text-yellow-600 dark:text-yellow-400
                                        @else text-red-600 dark:text-red-400
                                        @endif
                                    ">{{ $result->score }}%</span>
                                </div>
                            @endif
                            
                            <span class="px-3 py-1 rounded-full text-xs font-medium
                                @if($result->status === 'completed') bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200
                                @elseif($result->status === 'in_progress') bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-200
                                @else bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200
                                @endif
                            ">
                                @if($result->status === 'completed') {{ __('messages.completed') }}
                                @elseif($result->status === 'in_progress') {{ __('messages.in_progress') }}
                                @else {{ __('messages.failed') }}
                                @endif
                            </span>
                        </div>

                        <!-- Action Button -->
                        <div class="flex justify-end">
                            <a href="{{ route('ai-service.result', $result->id) }}" 
                               class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
                                {{ __('messages.view_details') }}
                            </a>
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Pagination -->
            <div class="mt-8">
                {{ $results->links() }}
            </div>

        @else
            <!-- Empty State -->
            <div class="text-center py-16">
                <svg class="w-24 h-24 text-gray-400 mx-auto mb-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.no_service_history') }}</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-8">{{ __('messages.no_service_history_desc') }}</p>
                
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('ai-service.personality-analysis') }}" 
                       class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200">
                        {{ __('messages.personality_analysis_title') }}
                    </a>
                    <a href="{{ route('ai-service.cv-improvement') }}" 
                       class="bg-gradient-to-r from-green-600 to-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:from-green-700 hover:to-blue-700 transition-all duration-200">
                        {{ __('messages.cv_improvement_title') }}
                    </a>
                    <a href="{{ route('ai-service.interview-simulation') }}" 
                       class="bg-gradient-to-r from-orange-600 to-red-600 text-white px-6 py-3 rounded-lg font-medium hover:from-orange-700 hover:to-red-700 transition-all duration-200">
                        {{ __('messages.interview_simulation_title') }}
                    </a>
                </div>
            </div>
        @endif

        <!-- Back to Profile -->
        <div class="text-center mt-8">
            <a href="{{ route('profile') }}" 
               class="text-indigo-600 dark:text-indigo-400 hover:text-indigo-700 dark:hover:text-indigo-300 font-medium inline-flex items-center">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                </svg>
                {{ __('messages.back_to_profile') }}
            </a>
        </div>
    </div>
</section>
@endsection
