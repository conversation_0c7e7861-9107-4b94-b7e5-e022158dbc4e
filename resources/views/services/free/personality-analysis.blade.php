@extends('layouts.app')

@section('title', __('messages.personality_analysis') . ' - ' . __('messages.basic_level') . ' - MonOri AI')
@section('description', __('messages.basic_personality_desc'))

@section('content')
<!-- Hero Section -->
<section class="py-12 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-10">
            <div class="inline-flex items-center bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-1 rounded-full text-sm font-medium mb-4">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {{ __('messages.free') }} - {{ __('messages.basic_level') }}
            </div>
            <h1 class="text-compact-title lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                <span class="gradient-text">{{ __('messages.personality_analysis') }}</span>
            </h1>
            <p class="text-compact-body text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-6">
                {{ __('messages.basic_personality_desc') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-compact justify-center">
                <button onclick="startBasicTest()" class="btn-primary-compact hover-lift inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="icon-compact" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span>{{ __('messages.start_test_now') }}</span>
                </button>
                <a href="{{ route('services.personality-analysis') }}" class="btn-secondary-compact hover-lift">
                    {{ __('messages.upgrade_to_professional') }}
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Basic Test Interface -->
<section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div id="test-interface" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div class="mb-6">
                    <div class="flex justify-between items-center mb-4">
                        <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ __('messages.personality_test') }}</h3>
                        <span id="question-counter" class="text-sm text-gray-500 dark:text-gray-400">1 / 10</span>
                    </div>
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div id="progress-bar" class="bg-blue-500 h-2 rounded-full transition-all duration-300" style="width: 10%"></div>
                    </div>
                </div>

                <div id="question-container">
                    <h4 id="question-text" class="text-lg font-medium text-gray-900 dark:text-white mb-6">
                        {{ __('messages.loading_question') }}
                    </h4>
                    
                    <div id="answers-container" class="space-y-3">
                        <!-- Answers will be loaded here -->
                    </div>
                </div>

                <div class="flex justify-between mt-8">
                    <button id="prev-btn" onclick="previousQuestion()" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors disabled:opacity-50" disabled>
                        {{ __('messages.previous') }}
                    </button>
                    <button id="next-btn" onclick="nextQuestion()" class="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors disabled:opacity-50" disabled>
                        {{ __('messages.next') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Results Section -->
        <div id="results-section" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.test_completed') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('messages.basic_results_ready') }}</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.personality_type') }}</h4>
                        <p id="personality-type" class="text-blue-600 dark:text-blue-400 font-medium">{{ __('messages.analyzing') }}...</p>
                    </div>
                    <div class="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.main_traits') }}</h4>
                        <ul id="main-traits" class="text-green-600 dark:text-green-400 space-y-1">
                            <li>{{ __('messages.analyzing') }}...</li>
                        </ul>
                    </div>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6 mb-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-500 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.upgrade_for_more') }}</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">{{ __('messages.professional_features_include') }}</p>
                            <a href="{{ route('services.personality-analysis') }}" class="inline-flex items-center bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                {{ __('messages.upgrade_now') }}
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="flex justify-center space-x-4">
                    <button onclick="retakeTest()" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        {{ __('messages.retake_test') }}
                    </button>
                    <a href="{{ route('services') }}" class="px-6 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded-lg transition-colors">
                        {{ __('messages.explore_other_services') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
let currentQuestion = 0;
let answers = [];
const totalQuestions = 10;

// Basic personality questions
const questions = [
    {
        text: "{{ __('messages.question_1') }}",
        options: [
            "{{ __('messages.strongly_disagree') }}",
            "{{ __('messages.disagree') }}",
            "{{ __('messages.neutral') }}",
            "{{ __('messages.agree') }}",
            "{{ __('messages.strongly_agree') }}"
        ]
    },
    // Add more basic questions here...
];

function startBasicTest() {
    document.getElementById('test-interface').classList.remove('hidden');
    loadQuestion();
}

function loadQuestion() {
    if (currentQuestion < questions.length) {
        document.getElementById('question-text').textContent = questions[currentQuestion].text;
        document.getElementById('question-counter').textContent = `${currentQuestion + 1} / ${totalQuestions}`;
        
        const progress = ((currentQuestion + 1) / totalQuestions) * 100;
        document.getElementById('progress-bar').style.width = progress + '%';
        
        const answersContainer = document.getElementById('answers-container');
        answersContainer.innerHTML = '';
        
        questions[currentQuestion].options.forEach((option, index) => {
            const answerDiv = document.createElement('div');
            answerDiv.innerHTML = `
                <label class="flex items-center p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer transition-colors">
                    <input type="radio" name="answer" value="${index}" class="mr-3" onchange="selectAnswer(this)">
                    <span class="text-gray-900 dark:text-white">${option}</span>
                </label>
            `;
            answersContainer.appendChild(answerDiv);
        });
        
        updateButtons();
    }
}

function selectAnswer(input) {
    answers[currentQuestion] = parseInt(input.value);
    document.getElementById('next-btn').disabled = false;
}

function nextQuestion() {
    if (currentQuestion < totalQuestions - 1) {
        currentQuestion++;
        loadQuestion();
    } else {
        showResults();
    }
}

function previousQuestion() {
    if (currentQuestion > 0) {
        currentQuestion--;
        loadQuestion();
    }
}

function updateButtons() {
    document.getElementById('prev-btn').disabled = currentQuestion === 0;
    document.getElementById('next-btn').disabled = !answers[currentQuestion];
    
    if (currentQuestion === totalQuestions - 1) {
        document.getElementById('next-btn').textContent = "{{ __('messages.finish') }}";
    } else {
        document.getElementById('next-btn').textContent = "{{ __('messages.next') }}";
    }
}

function showResults() {
    document.getElementById('test-interface').classList.add('hidden');
    document.getElementById('results-section').classList.remove('hidden');
    
    // Simulate basic analysis
    setTimeout(() => {
        document.getElementById('personality-type').textContent = "{{ __('messages.sample_personality_type') }}";
        document.getElementById('main-traits').innerHTML = `
            <li>{{ __('messages.trait_1') }}</li>
            <li>{{ __('messages.trait_2') }}</li>
            <li>{{ __('messages.trait_3') }}</li>
        `;
    }, 1000);
}

function retakeTest() {
    currentQuestion = 0;
    answers = [];
    document.getElementById('results-section').classList.add('hidden');
    startBasicTest();
}
</script>
@endsection
