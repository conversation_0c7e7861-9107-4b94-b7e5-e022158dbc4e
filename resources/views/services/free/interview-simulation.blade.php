@extends('layouts.app')

@section('title', __('messages.interview_simulation') . ' - ' . __('messages.basic_level') . ' - MonOri AI')
@section('description', __('messages.basic_interview_desc'))

@section('content')
<!-- Hero Section -->
<section class="py-12 bg-gradient-to-br from-purple-50 via-white to-pink-50 dark:from-gray-900 dark:via-gray-800 dark:to-pink-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-10">
            <div class="inline-flex items-center bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-1 rounded-full text-sm font-medium mb-4">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {{ __('messages.free') }} - {{ __('messages.basic_level') }}
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                <span class="gradient-text">{{ __('messages.interview_simulation') }}</span>
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                {{ __('messages.basic_interview_desc') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="startBasicInterview()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                    <span>{{ __('messages.start_interview') }}</span>
                </button>
                <a href="{{ route('services.interview-simulation') }}" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-purple-500 dark:hover:border-purple-400 transition-all duration-300">
                    {{ __('messages.upgrade_to_professional') }}
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Interview Setup -->
<section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div id="interview-setup" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.interview_setup') }}</h3>
                
                <div class="space-y-6">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.interview_type') }}</label>
                        <select id="interview-type" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-800 dark:text-white">
                            <option value="general">{{ __('messages.general_interview') }}</option>
                            <option value="behavioral">{{ __('messages.behavioral_interview') }}</option>
                            <option value="technical">{{ __('messages.technical_interview') }}</option>
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.job_position') }}</label>
                        <input type="text" id="job-position" placeholder="{{ __('messages.job_position_placeholder') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-800 dark:text-white">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.experience_level') }}</label>
                        <select id="experience-level" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-800 dark:text-white">
                            <option value="entry">{{ __('messages.entry_level') }}</option>
                            <option value="mid">{{ __('messages.mid_level') }}</option>
                            <option value="senior">{{ __('messages.senior_level') }}</option>
                        </select>
                    </div>
                </div>
                
                <div class="flex justify-between mt-8">
                    <button onclick="hideSetup()" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        {{ __('messages.cancel') }}
                    </button>
                    <button onclick="startInterview()" class="px-8 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors">
                        {{ __('messages.start_interview') }}
                    </button>
                </div>
            </div>
        </div>

        <!-- Interview Interface -->
        <div id="interview-interface" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-xl font-bold text-gray-900 dark:text-white">{{ __('messages.interview_in_progress') }}</h3>
                    <div class="flex items-center space-x-4">
                        <span id="question-counter" class="text-sm text-gray-500 dark:text-gray-400">1 / 5</span>
                        <div id="timer" class="text-sm font-medium text-purple-600 dark:text-purple-400">05:00</div>
                    </div>
                </div>

                <div class="mb-8">
                    <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mb-4">
                        <div id="progress-bar" class="bg-purple-500 h-2 rounded-full transition-all duration-300" style="width: 20%"></div>
                    </div>
                </div>

                <div class="mb-8">
                    <div class="bg-purple-50 dark:bg-purple-900/20 p-6 rounded-lg mb-6">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.interviewer') }}</h4>
                        <p id="question-text" class="text-gray-700 dark:text-gray-300">{{ __('messages.loading_question') }}</p>
                    </div>

                    <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.your_answer') }}</h4>
                        <textarea id="answer-input" rows="6" placeholder="{{ __('messages.type_your_answer') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-purple-500 dark:bg-gray-800 dark:text-white resize-none"></textarea>
                        
                        <div class="flex justify-between items-center mt-4">
                            <span id="word-count" class="text-sm text-gray-500 dark:text-gray-400">0 {{ __('messages.words') }}</span>
                            <div class="space-x-2">
                                <button onclick="skipQuestion()" class="px-4 py-2 text-gray-600 dark:text-gray-400 hover:text-gray-800 dark:hover:text-gray-200">
                                    {{ __('messages.skip') }}
                                </button>
                                <button onclick="nextQuestion()" class="px-6 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors">
                                    {{ __('messages.next_question') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Interview Results -->
        <div id="interview-results" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div class="text-center mb-8">
                    <div class="w-20 h-20 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg class="w-10 h-10 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2">{{ __('messages.interview_completed') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('messages.basic_feedback_ready') }}</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                    <div class="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.overall_performance') }}</h4>
                        <div class="flex items-center">
                            <div class="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-3 mr-3">
                                <div id="performance-bar" class="bg-blue-500 h-3 rounded-full" style="width: 75%"></div>
                            </div>
                            <span class="text-blue-600 dark:text-blue-400 font-medium">75%</span>
                        </div>
                    </div>
                    
                    <div class="bg-green-50 dark:bg-green-900/20 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">{{ __('messages.completion_time') }}</h4>
                        <p class="text-green-600 dark:text-green-400 font-medium" id="completion-time">12:34</p>
                    </div>
                </div>

                <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg mb-6">
                    <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.basic_feedback') }}</h4>
                    <ul class="space-y-2 text-gray-700 dark:text-gray-300">
                        <li class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ __('messages.feedback_1') }}
                        </li>
                        <li class="flex items-start">
                            <svg class="w-5 h-5 text-green-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ __('messages.feedback_2') }}
                        </li>
                        <li class="flex items-start">
                            <svg class="w-5 h-5 text-yellow-500 mt-0.5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            {{ __('messages.feedback_3') }}
                        </li>
                    </ul>
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6 mb-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-500 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.upgrade_for_detailed_feedback') }}</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">{{ __('messages.professional_interview_features') }}</p>
                            <a href="{{ route('services.interview-simulation') }}" class="inline-flex items-center bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                {{ __('messages.upgrade_now') }}
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="flex justify-center space-x-4">
                    <button onclick="retakeInterview()" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                        {{ __('messages.retake_interview') }}
                    </button>
                    <a href="{{ route('services') }}" class="px-6 py-2 bg-purple-500 hover:bg-purple-600 text-white rounded-lg transition-colors">
                        {{ __('messages.explore_other_services') }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
let currentQuestion = 0;
let answers = [];
let startTime;
let timerInterval;
const totalQuestions = 5;

// Basic interview questions
const questions = [
    "{{ __('messages.interview_question_1') }}",
    "{{ __('messages.interview_question_2') }}",
    "{{ __('messages.interview_question_3') }}",
    "{{ __('messages.interview_question_4') }}",
    "{{ __('messages.interview_question_5') }}"
];

function startBasicInterview() {
    document.getElementById('interview-setup').classList.remove('hidden');
}

function hideSetup() {
    document.getElementById('interview-setup').classList.add('hidden');
}

function startInterview() {
    document.getElementById('interview-setup').classList.add('hidden');
    document.getElementById('interview-interface').classList.remove('hidden');
    startTime = new Date();
    startTimer();
    loadQuestion();
}

function startTimer() {
    let timeLeft = 300; // 5 minutes
    timerInterval = setInterval(() => {
        const minutes = Math.floor(timeLeft / 60);
        const seconds = timeLeft % 60;
        document.getElementById('timer').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        
        if (timeLeft <= 0) {
            clearInterval(timerInterval);
            finishInterview();
        }
        timeLeft--;
    }, 1000);
}

function loadQuestion() {
    if (currentQuestion < questions.length) {
        document.getElementById('question-text').textContent = questions[currentQuestion];
        document.getElementById('question-counter').textContent = `${currentQuestion + 1} / ${totalQuestions}`;
        
        const progress = ((currentQuestion + 1) / totalQuestions) * 100;
        document.getElementById('progress-bar').style.width = progress + '%';
        
        document.getElementById('answer-input').value = '';
        updateWordCount();
    }
}

function nextQuestion() {
    const answer = document.getElementById('answer-input').value.trim();
    answers[currentQuestion] = answer;
    
    if (currentQuestion < totalQuestions - 1) {
        currentQuestion++;
        loadQuestion();
    } else {
        finishInterview();
    }
}

function skipQuestion() {
    answers[currentQuestion] = '';
    nextQuestion();
}

function finishInterview() {
    clearInterval(timerInterval);
    const endTime = new Date();
    const duration = Math.floor((endTime - startTime) / 1000);
    const minutes = Math.floor(duration / 60);
    const seconds = duration % 60;
    
    document.getElementById('completion-time').textContent = 
        `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    
    document.getElementById('interview-interface').classList.add('hidden');
    document.getElementById('interview-results').classList.remove('hidden');
}

function retakeInterview() {
    currentQuestion = 0;
    answers = [];
    document.getElementById('interview-results').classList.add('hidden');
    startBasicInterview();
}

// Word count functionality
document.getElementById('answer-input').addEventListener('input', updateWordCount);

function updateWordCount() {
    const text = document.getElementById('answer-input').value.trim();
    const wordCount = text === '' ? 0 : text.split(/\s+/).length;
    document.getElementById('word-count').textContent = `${wordCount} {{ __('messages.words') }}`;
}
</script>
@endsection
