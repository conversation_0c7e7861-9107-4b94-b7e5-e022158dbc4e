@extends('layouts.app')

@section('title', __('messages.cv_improvement') . ' - ' . __('messages.basic_level') . ' - MonOri AI')
@section('description', __('messages.basic_cv_desc'))

@section('content')
<!-- Hero Section -->
<section class="py-12 bg-gradient-to-br from-green-50 via-white to-blue-50 dark:from-gray-900 dark:via-gray-800 dark:to-blue-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-10">
            <div class="inline-flex items-center bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 px-3 py-1 rounded-full text-sm font-medium mb-4">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                {{ __('messages.free') }} - {{ __('messages.basic_level') }}
            </div>
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                <span class="gradient-text">{{ __('messages.cv_improvement') }}</span>
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                {{ __('messages.basic_cv_desc') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button onclick="startBasicCVBuilder()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    <span>{{ __('messages.create_cv_now') }}</span>
                </button>
                <a href="{{ route('services.cv-improvement') }}" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-green-500 dark:hover:border-green-400 transition-all duration-300">
                    {{ __('messages.upgrade_to_professional') }}
                </a>
            </div>
        </div>
    </div>
</section>

<!-- Basic CV Builder -->
<section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div id="cv-builder" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div class="mb-8">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4">{{ __('messages.basic_cv_builder') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('messages.fill_basic_info') }}</p>
                </div>

                <form id="basic-cv-form" class="space-y-6">
                    <!-- Personal Information -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.personal_information') }}</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.full_name') }}</label>
                                <input type="text" name="full_name" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.email') }}</label>
                                <input type="email" name="email" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.phone') }}</label>
                                <input type="tel" name="phone" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.location') }}</label>
                                <input type="text" name="location" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                            </div>
                        </div>
                    </div>

                    <!-- Professional Summary -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.professional_summary') }}</h4>
                        <textarea name="summary" rows="4" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" placeholder="{{ __('messages.summary_placeholder') }}" required></textarea>
                    </div>

                    <!-- Work Experience -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.work_experience') }}</h4>
                        <div id="experience-container">
                            <div class="experience-item border border-gray-200 dark:border-gray-600 p-4 rounded-lg mb-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <input type="text" name="job_title[]" placeholder="{{ __('messages.job_title') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                                    <input type="text" name="company[]" placeholder="{{ __('messages.company') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                    <input type="text" name="start_date[]" placeholder="{{ __('messages.start_date') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                                    <input type="text" name="end_date[]" placeholder="{{ __('messages.end_date') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white">
                                </div>
                                <textarea name="job_description[]" rows="3" placeholder="{{ __('messages.job_description') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required></textarea>
                            </div>
                        </div>
                        <button type="button" onclick="addExperience()" class="text-green-600 hover:text-green-700 font-medium">+ {{ __('messages.add_experience') }}</button>
                    </div>

                    <!-- Education -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.education') }}</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <input type="text" name="degree" placeholder="{{ __('messages.degree') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                            <input type="text" name="institution" placeholder="{{ __('messages.institution') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                            <input type="text" name="graduation_year" placeholder="{{ __('messages.graduation_year') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
                        </div>
                    </div>

                    <!-- Skills -->
                    <div class="bg-gray-50 dark:bg-gray-700 p-6 rounded-lg">
                        <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-4">{{ __('messages.skills') }}</h4>
                        <textarea name="skills" rows="3" placeholder="{{ __('messages.skills_placeholder') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required></textarea>
                    </div>

                    <div class="flex justify-between">
                        <button type="button" onclick="hideBuilder()" class="px-6 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            {{ __('messages.cancel') }}
                        </button>
                        <button type="submit" class="px-8 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors">
                            {{ __('messages.generate_cv') }}
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- CV Preview -->
        <div id="cv-preview" class="hidden">
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white">{{ __('messages.cv_preview') }}</h3>
                    <div class="space-x-2">
                        <button onclick="editCV()" class="px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                            {{ __('messages.edit') }}
                        </button>
                        <button onclick="downloadCV()" class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded-lg transition-colors">
                            {{ __('messages.download') }}
                        </button>
                    </div>
                </div>

                <div id="cv-content" class="bg-gray-50 dark:bg-gray-700 p-8 rounded-lg">
                    <!-- CV content will be generated here -->
                </div>

                <div class="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-700 rounded-lg p-6 mt-6">
                    <div class="flex items-start">
                        <svg class="w-6 h-6 text-yellow-500 mt-1 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <div>
                            <h4 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.upgrade_for_better_cv') }}</h4>
                            <p class="text-gray-600 dark:text-gray-400 mb-4">{{ __('messages.professional_cv_features') }}</p>
                            <a href="{{ route('services.cv-improvement') }}" class="inline-flex items-center bg-yellow-500 hover:bg-yellow-600 text-white px-4 py-2 rounded-lg font-medium transition-colors">
                                {{ __('messages.upgrade_now') }}
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
function startBasicCVBuilder() {
    document.getElementById('cv-builder').classList.remove('hidden');
}

function hideBuilder() {
    document.getElementById('cv-builder').classList.add('hidden');
}

function addExperience() {
    const container = document.getElementById('experience-container');
    const newExperience = document.createElement('div');
    newExperience.className = 'experience-item border border-gray-200 dark:border-gray-600 p-4 rounded-lg mb-4';
    newExperience.innerHTML = `
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input type="text" name="job_title[]" placeholder="{{ __('messages.job_title') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
            <input type="text" name="company[]" placeholder="{{ __('messages.company') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input type="text" name="start_date[]" placeholder="{{ __('messages.start_date') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required>
            <input type="text" name="end_date[]" placeholder="{{ __('messages.end_date') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white">
        </div>
        <textarea name="job_description[]" rows="3" placeholder="{{ __('messages.job_description') }}" class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 dark:bg-gray-800 dark:text-white" required></textarea>
        <button type="button" onclick="removeExperience(this)" class="text-red-600 hover:text-red-700 font-medium mt-2">{{ __('messages.remove') }}</button>
    `;
    container.appendChild(newExperience);
}

function removeExperience(button) {
    button.closest('.experience-item').remove();
}

document.getElementById('basic-cv-form').addEventListener('submit', function(e) {
    e.preventDefault();
    generateBasicCV();
});

function generateBasicCV() {
    const formData = new FormData(document.getElementById('basic-cv-form'));
    
    // Generate basic CV content
    const cvContent = `
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">${formData.get('full_name')}</h1>
            <p class="text-gray-600 dark:text-gray-400">${formData.get('email')} | ${formData.get('phone')} | ${formData.get('location')}</p>
        </div>
        
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-3">{{ __('messages.professional_summary') }}</h2>
            <p class="text-gray-700 dark:text-gray-300">${formData.get('summary')}</p>
        </div>
        
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-3">{{ __('messages.work_experience') }}</h2>
            <div class="space-y-4">
                ${generateExperienceHTML(formData)}
            </div>
        </div>
        
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-3">{{ __('messages.education') }}</h2>
            <p class="text-gray-700 dark:text-gray-300">${formData.get('degree')} - ${formData.get('institution')} (${formData.get('graduation_year')})</p>
        </div>
        
        <div class="mb-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-3">{{ __('messages.skills') }}</h2>
            <p class="text-gray-700 dark:text-gray-300">${formData.get('skills')}</p>
        </div>
    `;
    
    document.getElementById('cv-content').innerHTML = cvContent;
    document.getElementById('cv-builder').classList.add('hidden');
    document.getElementById('cv-preview').classList.remove('hidden');
}

function generateExperienceHTML(formData) {
    const jobTitles = formData.getAll('job_title[]');
    const companies = formData.getAll('company[]');
    const startDates = formData.getAll('start_date[]');
    const endDates = formData.getAll('end_date[]');
    const descriptions = formData.getAll('job_description[]');
    
    let html = '';
    for (let i = 0; i < jobTitles.length; i++) {
        html += `
            <div class="border-l-4 border-green-500 pl-4">
                <h3 class="font-semibold text-gray-900 dark:text-white">${jobTitles[i]} - ${companies[i]}</h3>
                <p class="text-sm text-gray-600 dark:text-gray-400">${startDates[i]} - ${endDates[i] || 'Present'}</p>
                <p class="text-gray-700 dark:text-gray-300 mt-2">${descriptions[i]}</p>
            </div>
        `;
    }
    return html;
}

function editCV() {
    document.getElementById('cv-preview').classList.add('hidden');
    document.getElementById('cv-builder').classList.remove('hidden');
}

function downloadCV() {
    alert('{{ __('messages.download_feature_coming_soon') }}');
}
</script>
@endsection
