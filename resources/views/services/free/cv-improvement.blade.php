@extends('layouts.app')

@section('title', 'Basic CV Builder - MonOri AI')
@section('description', 'Create your professional CV with our free AI-powered basic builder')

@push('styles')
<style>
    .cv-builder-container {
        background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
        min-height: 100vh;
        position: relative;
    }

    .builder-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-section {
        padding: 2rem;
        border-bottom: 1px solid #e5e7eb;
    }

    .form-section:last-child {
        border-bottom: none;
    }

    .section-title {
        font-size: 1.5rem;
        font-weight: 700;
        color: #1f2937;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
        gap: 0.5rem;
    }

    .form-input {
        width: 100%;
        padding: 0.75rem 1rem;
        border: 2px solid #e5e7eb;
        border-radius: 12px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-input:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }

    .form-label {
        display: block;
        font-weight: 600;
        color: #374151;
        margin-bottom: 0.5rem;
        font-size: 0.9rem;
    }

    .add-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 600;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        font-size: 0.9rem;
    }

    .add-button:hover {
        transform: translateY(-1px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }

    .remove-button {
        background: #ef4444;
        color: white;
        padding: 0.25rem 0.5rem;
        border-radius: 6px;
        border: none;
        cursor: pointer;
        font-size: 0.8rem;
    }

    .dynamic-item {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        position: relative;
    }

    .generate-button {
        background: linear-gradient(135deg, #10b981, #059669);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 700;
        font-size: 1.1rem;
        border: none;
        cursor: pointer;
        transition: all 0.3s ease;
        width: 100%;
        margin-top: 2rem;
    }

    .generate-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(16, 185, 129, 0.4);
    }

    .generate-button:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
    }

    .upgrade-banner {
        background: linear-gradient(135deg, #f59e0b, #ef4444);
        color: white;
        padding: 1rem;
        border-radius: 12px;
        margin-bottom: 2rem;
        text-align: center;
    }

    .upgrade-banner a {
        color: white;
        text-decoration: underline;
        font-weight: 600;
    }

    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        display: none;
    }

    .loading-content {
        background: white;
        padding: 2rem;
        border-radius: 20px;
        text-align: center;
        max-width: 400px;
    }

    .spinner {
        border: 4px solid #f3f4f6;
        border-top: 4px solid #667eea;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        animation: spin 1s linear infinite;
        margin: 0 auto 1rem;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    @media (max-width: 768px) {
        .cv-builder-container {
            padding: 1rem;
        }
        
        .form-section {
            padding: 1.5rem;
        }
    }
</style>
@endpush

@section('content')
<div class="cv-builder-container">
    <div class="max-w-4xl mx-auto px-4 py-8">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="inline-flex items-center bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium mb-4">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
                FREE - Basic Level
            </div>
            <h1 class="text-4xl font-bold text-white mb-4">AI-Powered CV Builder</h1>
            <p class="text-blue-100 text-lg">Create a professional CV with intelligent AI optimization</p>
            <div class="bg-blue-800/30 border border-blue-600/50 rounded-lg p-3 max-w-2xl mx-auto mt-4">
                <div class="flex items-center justify-center text-blue-100">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <span class="text-sm">
                        ✨ AI Enhancement Active - Professional content optimization included!
                    </span>
                </div>
            </div>
        </div>

        <!-- Features Banner -->
        <div class="upgrade-banner">
            <p class="mb-2">🎯 This FREE version includes AI content enhancement and professional PDF generation!</p>
            <a href="{{ route('services.cv-improvement') }}">Want more features? Check Professional (50 DH) or Premium (100 DH)</a>
        </div>

        <!-- CV Builder Form -->
        <div class="builder-card">
            <form id="cvBuilderForm">
                @csrf
                <input type="hidden" name="level" value="basic">
                
                <!-- Personal Information -->
                <div class="form-section">
                    <h2 class="section-title">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Personal Information
                    </h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="form-label">Full Name *</label>
                            <input type="text" name="full_name" class="form-input" required>
                        </div>
                        <div>
                            <label class="form-label">Email *</label>
                            <input type="email" name="email" class="form-input" required>
                        </div>
                        <div>
                            <label class="form-label">Phone *</label>
                            <input type="tel" name="phone" class="form-input" required>
                        </div>
                        <div>
                            <label class="form-label">Location</label>
                            <input type="text" name="location" class="form-input">
                        </div>
                    </div>
                </div>

                <!-- Professional Summary -->
                <div class="form-section">
                    <h2 class="section-title">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Professional Summary
                    </h2>
                    
                    <textarea name="summary" rows="4" class="form-input" placeholder="Brief description about yourself and your career goals..."></textarea>
                </div>

                <!-- Experience -->
                <div class="form-section">
                    <h2 class="section-title">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 13.255A23.931 23.931 0 0112 15c-3.183 0-6.22-.62-9-1.745M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2-2v2m8 0V6a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V8a2 2 0 012-2V6"></path>
                        </svg>
                        Work Experience
                    </h2>
                    
                    <div id="experienceContainer">
                        <div class="dynamic-item">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <input type="text" name="experience[0][job_title]" placeholder="Job Title" class="form-input">
                                <input type="text" name="experience[0][company]" placeholder="Company" class="form-input">
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <input type="text" name="experience[0][start_date]" placeholder="Start Date (e.g., Jan 2020)" class="form-input">
                                <input type="text" name="experience[0][end_date]" placeholder="End Date (or Present)" class="form-input">
                            </div>
                            <textarea name="experience[0][description]" rows="3" placeholder="Job description and achievements..." class="form-input"></textarea>
                        </div>
                    </div>
                    
                    <button type="button" onclick="addExperience()" class="add-button">+ Add Experience</button>
                </div>

                <!-- Education -->
                <div class="form-section">
                    <h2 class="section-title">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z"></path>
                        </svg>
                        Education
                    </h2>

                    <div id="educationContainer">
                        <div class="dynamic-item">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <input type="text" name="education[0][degree]" placeholder="Degree" class="form-input">
                                <input type="text" name="education[0][institution]" placeholder="Institution" class="form-input">
                            </div>
                            <input type="text" name="education[0][year]" placeholder="Graduation Year" class="form-input">
                        </div>
                    </div>

                    <button type="button" onclick="addEducation()" class="add-button">+ Add Education</button>
                </div>

                <!-- Skills -->
                <div class="form-section">
                    <h2 class="section-title">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                        Skills
                    </h2>

                    <div id="skillsContainer">
                        <div class="dynamic-item">
                            <input type="text" name="skills[]" placeholder="Skill (e.g., JavaScript, Project Management)" class="form-input">
                        </div>
                    </div>

                    <button type="button" onclick="addSkill()" class="add-button">+ Add Skill</button>
                </div>



                <!-- Languages You Speak -->
                <div class="form-section">
                    <h2 class="section-title">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"></path>
                        </svg>
                        Languages You Speak
                    </h2>

                    <div id="languagesContainer">
                        <div class="dynamic-item">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <input type="text" name="languages[0][language]" placeholder="Language (e.g., Arabic, English, French)" class="form-input">
                                <select name="languages[0][level]" class="form-input">
                                    <option value="">Select Level</option>
                                    <option value="Beginner">Beginner</option>
                                    <option value="Intermediate">Intermediate</option>
                                    <option value="Advanced">Advanced</option>
                                    <option value="Native">Native</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <button type="button" onclick="addLanguage()" class="add-button">+ Add Language</button>
                </div>

                <!-- CV Language Selection -->
                <div class="form-section">
                    <h2 class="section-title">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        CV Language
                    </h2>

                    <div>
                        <label class="form-label">Choose the language for your CV *</label>
                        <select name="cv_language" class="form-input" required>
                            <option value="">Select CV Language</option>
                            <option value="en">English</option>
                            <option value="ar">العربية (Arabic)</option>
                            <option value="fr">Français (French)</option>
                        </select>
                        <p class="text-sm text-gray-500 mt-2">This will determine the language of section titles and layout direction in your CV</p>
                    </div>
                </div>

                <!-- Generate Button -->
                <div class="form-section">
                    <button type="submit" class="generate-button" id="generateBtn">
                        <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Generate CV with AI
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="loading-overlay">
    <div class="loading-content">
        <div class="spinner"></div>
        <h3 class="text-lg font-semibold mb-2">Generating Your CV</h3>
        <p class="text-gray-600">AI is optimizing your content...</p>
    </div>
</div>
@endsection

@push('scripts')
<script>
let experienceCount = 1;
let educationCount = 1;
let skillCount = 1;
let languageCount = 1;

// Add Experience
function addExperience() {
    const container = document.getElementById('experienceContainer');
    const newItem = document.createElement('div');
    newItem.className = 'dynamic-item';
    newItem.innerHTML = `
        <div class="flex justify-end mb-2">
            <button type="button" onclick="removeItem(this)" class="remove-button">Remove</button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input type="text" name="experience[${experienceCount}][job_title]" placeholder="Job Title" class="form-input">
            <input type="text" name="experience[${experienceCount}][company]" placeholder="Company" class="form-input">
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input type="text" name="experience[${experienceCount}][start_date]" placeholder="Start Date (e.g., Jan 2020)" class="form-input">
            <input type="text" name="experience[${experienceCount}][end_date]" placeholder="End Date (or Present)" class="form-input">
        </div>
        <textarea name="experience[${experienceCount}][description]" rows="3" placeholder="Job description and achievements..." class="form-input"></textarea>
    `;
    container.appendChild(newItem);
    experienceCount++;
}

// Add Education
function addEducation() {
    const container = document.getElementById('educationContainer');
    const newItem = document.createElement('div');
    newItem.className = 'dynamic-item';
    newItem.innerHTML = `
        <div class="flex justify-end mb-2">
            <button type="button" onclick="removeItem(this)" class="remove-button">Remove</button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <input type="text" name="education[${educationCount}][degree]" placeholder="Degree" class="form-input">
            <input type="text" name="education[${educationCount}][institution]" placeholder="Institution" class="form-input">
        </div>
        <input type="text" name="education[${educationCount}][year]" placeholder="Graduation Year" class="form-input">
    `;
    container.appendChild(newItem);
    educationCount++;
}

// Add Skill
function addSkill() {
    const container = document.getElementById('skillsContainer');
    const newItem = document.createElement('div');
    newItem.className = 'dynamic-item';
    newItem.innerHTML = `
        <div class="flex justify-end mb-2">
            <button type="button" onclick="removeItem(this)" class="remove-button">Remove</button>
        </div>
        <input type="text" name="skills[]" placeholder="Skill (e.g., JavaScript, Project Management)" class="form-input">
    `;
    container.appendChild(newItem);
    skillCount++;
}

// Add Language
function addLanguage() {
    const container = document.getElementById('languagesContainer');
    const newItem = document.createElement('div');
    newItem.className = 'dynamic-item';
    newItem.innerHTML = `
        <div class="flex justify-end mb-2">
            <button type="button" onclick="removeItem(this)" class="remove-button">Remove</button>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <input type="text" name="languages[${languageCount}][language]" placeholder="Language" class="form-input">
            <select name="languages[${languageCount}][level]" class="form-input">
                <option value="">Select Level</option>
                <option value="Beginner">Beginner</option>
                <option value="Intermediate">Intermediate</option>
                <option value="Advanced">Advanced</option>
                <option value="Native">Native</option>
            </select>
        </div>
    `;
    container.appendChild(newItem);
    languageCount++;
}

// Remove Item
function removeItem(button) {
    button.closest('.dynamic-item').remove();
}

// Form Submission
document.getElementById('cvBuilderForm').addEventListener('submit', function(e) {
    e.preventDefault();

    const formData = new FormData(this);
    const generateBtn = document.getElementById('generateBtn');
    const loadingOverlay = document.getElementById('loadingOverlay');

    // Show loading
    generateBtn.disabled = true;
    generateBtn.innerHTML = 'Generating...';
    loadingOverlay.style.display = 'flex';

    // Submit to backend
    fetch('{{ route("cv.generate") }}', {
        method: 'POST',
        body: formData,
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        console.log('Response received:', data);
        loadingOverlay.style.display = 'none';

        if (data.success) {
            console.log('Success! Download URL:', data.download_url);
            // Show success message with download link
            showSuccessWithDownload(data.download_url, data.message || 'CV generated successfully!');
        } else {
            console.log('Error:', data.message);
            showNotification(data.message || 'Failed to generate CV', 'error');
        }
    })
    .catch(error => {
        loadingOverlay.style.display = 'none';
        showNotification('An error occurred. Please try again.', 'error');
        console.error('Error:', error);
    })
    .finally(() => {
        generateBtn.disabled = false;
        generateBtn.innerHTML = `
            <svg class="w-5 h-5 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
            </svg>
            Generate CV with AI
        `;
    });
});

// Notification function
function showNotification(message, type) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg text-white z-50 ${type === 'success' ? 'bg-green-500' : 'bg-red-500'}`;
    notification.textContent = message;

    document.body.appendChild(notification);

    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Success with download function
function showSuccessWithDownload(downloadUrl, message) {
    // Create modal overlay
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-lg p-8 max-w-md mx-4 text-center shadow-2xl">
            <div class="text-green-500 text-6xl mb-4">✓</div>
            <h3 class="text-xl font-semibold mb-4 text-gray-800 dark:text-white">CV Generated Successfully!</h3>
            <p class="text-gray-600 dark:text-gray-300 mb-6">${message}</p>
            <div class="flex flex-col gap-3">
                <a href="${downloadUrl}" class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                    Download CV PDF
                </a>
                <button onclick="this.closest('.fixed').remove()" class="bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-2 rounded-lg font-medium transition-colors">
                    Close
                </button>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Auto-close after 30 seconds
    setTimeout(() => {
        if (document.body.contains(modal)) {
            modal.remove();
        }
    }, 30000);
}
</script>
@endpush
