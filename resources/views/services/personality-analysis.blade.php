@extends('layouts.app')

@section('title', __('messages.personality_analysis') . ' - MonOri AI')
@section('description', __('messages.personality_analysis_description'))

@section('content')
<!-- Hero Section -->
<section class="py-12 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-10">
            <h1 class="text-compact-title lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                <span class="gradient-text">{{ __('messages.personality_analysis') }}</span>
            </h1>
            <p class="text-compact-body text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-6">
                {{ __('messages.discover_personality') }}
            </p>
            <div class="flex flex-col sm:flex-row gap-compact justify-center">
                <button onclick="startPersonalityTest()" class="btn-primary-compact hover-lift inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                    <svg class="icon-compact" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                    <span>{{ __('messages.start_test_now') }}</span>
                </button>
                <button onclick="showSampleResults()" class="btn-secondary-compact hover-lift">
                    {{ __('messages.show_sample_results') }}
                </button>
                <form action="{{ route('services.demo.personality-analysis') }}" method="POST" class="inline">
                    @csrf
                    <input type="hidden" name="answers[]" value="demo1">
                    <input type="hidden" name="answers[]" value="demo2">
                    <input type="hidden" name="answers[]" value="demo3">
                    <button type="submit" class="bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
                        🎯 {{ __('messages.try_demo') }}
                    </button>
                </form>
            </div>
        </div>
    </div>
</section>

<!-- Test Types Section -->
<section class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">{{ __('messages.available_test_types') }}</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
                {{ __('messages.comprehensive_scientific_tests') }}
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- MBTI Test -->
            <div class="card-hover bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">اختبار MBTI</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    مؤشر مايرز-بريجز لأنماط الشخصية - يحدد نمطك من بين 16 نمط شخصية مختلف
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 15-20 دقيقة</span>
                    <button onclick="startMBTITest()" class="text-blue-600 dark:text-blue-400 font-semibold hover:text-blue-700 dark:hover:text-blue-300">
                        ابدأ الاختبار →
                    </button>
                </div>
            </div>

            <!-- Big Five Test -->
            <div class="card-hover bg-gradient-to-br from-green-50 to-teal-50 dark:from-green-900/20 dark:to-teal-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-teal-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">اختبار Big Five</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    نموذج العوامل الخمسة الكبرى للشخصية - يقيس خمسة أبعاد أساسية للشخصية
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 10-15 دقيقة</span>
                    <button onclick="startBigFiveTest()" class="text-green-600 dark:text-green-400 font-semibold hover:text-green-700 dark:hover:text-green-300">
                        ابدأ الاختبار →
                    </button>
                </div>
            </div>

            <!-- AI Enhanced Analysis -->
            <div class="card-hover bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 p-8 rounded-2xl border border-gray-200 dark:border-gray-700">
                <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-red-600 rounded-2xl flex items-center justify-center mb-6">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-4">التحليل المعزز بالذكاء الاصطناعي</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-6">
                    تحليل شامل يجمع بين النتائج التقليدية والذكاء الاصطناعي لفهم أعمق
                </p>
                <div class="flex items-center justify-between">
                    <span class="text-sm text-gray-500 dark:text-gray-400">⏱️ 25-30 دقيقة</span>
                    <button onclick="startAIAnalysis()" class="text-orange-600 dark:text-orange-400 font-semibold hover:text-orange-700 dark:hover:text-orange-300">
                        ابدأ التحليل →
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Test Interface -->
<section id="test-interface" class="hidden py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Test Progress -->
        <div class="mb-8">
            <div class="flex items-center justify-between mb-2">
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300">التقدم</span>
                <span class="text-sm font-medium text-gray-700 dark:text-gray-300" id="progress-text">1 من 60</span>
            </div>
            <div class="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300" id="progress-bar" style="width: 1.67%"></div>
            </div>
        </div>

        <!-- Question Card -->
        <div class="bg-white dark:bg-gray-900 rounded-2xl shadow-lg p-8 mb-8">
            <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6" id="question-text">
                أنا شخص يحب التفاعل مع الآخرين في المناسبات الاجتماعية
            </h3>

            <!-- Answer Options -->
            <div class="space-y-3" id="answer-options">
                <label class="flex items-center p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-all duration-200">
                    <input type="radio" name="answer" value="5" class="sr-only">
                    <div class="w-5 h-5 border-2 border-gray-300 dark:border-gray-600 rounded-full mr-3 rtl:ml-3 rtl:mr-0 flex items-center justify-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full hidden"></div>
                    </div>
                    <span class="text-gray-700 dark:text-gray-300">أوافق بشدة</span>
                </label>

                <label class="flex items-center p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-all duration-200">
                    <input type="radio" name="answer" value="4" class="sr-only">
                    <div class="w-5 h-5 border-2 border-gray-300 dark:border-gray-600 rounded-full mr-3 rtl:ml-3 rtl:mr-0 flex items-center justify-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full hidden"></div>
                    </div>
                    <span class="text-gray-700 dark:text-gray-300">أوافق</span>
                </label>

                <label class="flex items-center p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-all duration-200">
                    <input type="radio" name="answer" value="3" class="sr-only">
                    <div class="w-5 h-5 border-2 border-gray-300 dark:border-gray-600 rounded-full mr-3 rtl:ml-3 rtl:mr-0 flex items-center justify-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full hidden"></div>
                    </div>
                    <span class="text-gray-700 dark:text-gray-300">محايد</span>
                </label>

                <label class="flex items-center p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-all duration-200">
                    <input type="radio" name="answer" value="2" class="sr-only">
                    <div class="w-5 h-5 border-2 border-gray-300 dark:border-gray-600 rounded-full mr-3 rtl:ml-3 rtl:mr-0 flex items-center justify-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full hidden"></div>
                    </div>
                    <span class="text-gray-700 dark:text-gray-300">لا أوافق</span>
                </label>

                <label class="flex items-center p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 cursor-pointer transition-all duration-200">
                    <input type="radio" name="answer" value="1" class="sr-only">
                    <div class="w-5 h-5 border-2 border-gray-300 dark:border-gray-600 rounded-full mr-3 rtl:ml-3 rtl:mr-0 flex items-center justify-center">
                        <div class="w-3 h-3 bg-blue-500 rounded-full hidden"></div>
                    </div>
                    <span class="text-gray-700 dark:text-gray-300">لا أوافق بشدة</span>
                </label>
            </div>
        </div>

        <!-- Navigation Buttons -->
        <div class="flex justify-between">
            <button id="prev-btn" onclick="previousQuestion()" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-6 py-3 rounded-xl font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                السؤال السابق
            </button>
            <button id="next-btn" onclick="nextQuestion()" class="btn-primary text-white px-6 py-3 rounded-xl font-semibold disabled:opacity-50 disabled:cursor-not-allowed" disabled>
                السؤال التالي
            </button>
        </div>
    </div>
</section>

<!-- Results Section -->
<section id="results-section" class="hidden py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-6">نتائج تحليل شخصيتك</h2>
            <p class="text-xl text-gray-600 dark:text-gray-300">تم تحليل إجاباتك باستخدام الذكاء الاصطناعي المتقدم</p>
        </div>

        <!-- Personality Type Card -->
        <div class="max-w-4xl mx-auto">
            <div class="bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-2xl p-8 mb-8">
                <div class="text-center mb-8">
                    <div class="w-24 h-24 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                        <span class="text-2xl font-bold text-white" id="personality-type">ENFP</span>
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-2" id="personality-title">المحفز المتحمس</h3>
                    <p class="text-gray-600 dark:text-gray-300" id="personality-subtitle">شخصية إبداعية ومتحمسة تحب الإلهام والتأثير على الآخرين</p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <!-- Strengths -->
                    <div>
                        <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-4">نقاط القوة</h4>
                        <ul class="space-y-2" id="strengths-list">
                            <li class="flex items-center text-gray-700 dark:text-gray-300">
                                <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                إبداعي ومبتكر
                            </li>
                            <li class="flex items-center text-gray-700 dark:text-gray-300">
                                <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                متحمس وملهم للآخرين
                            </li>
                            <li class="flex items-center text-gray-700 dark:text-gray-300">
                                <svg class="w-5 h-5 text-green-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                مرن ومتكيف
                            </li>
                        </ul>
                    </div>

                    <!-- Areas for Development -->
                    <div>
                        <h4 class="text-lg font-bold text-gray-900 dark:text-white mb-4">مجالات التطوير</h4>
                        <ul class="space-y-2" id="development-list">
                            <li class="flex items-center text-gray-700 dark:text-gray-300">
                                <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                تحسين التركيز على التفاصيل
                            </li>
                            <li class="flex items-center text-gray-700 dark:text-gray-300">
                                <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                إدارة الوقت بشكل أفضل
                            </li>
                            <li class="flex items-center text-gray-700 dark:text-gray-300">
                                <svg class="w-5 h-5 text-orange-500 mr-2 rtl:ml-2 rtl:mr-0" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                تطوير الصبر والمثابرة
                            </li>
                        </ul>
                    </div>
                </div>
            </div>

            <!-- Career Recommendations -->
            <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-lg p-8 mb-8">
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-6">التوصيات المهنية</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="career-recommendations">
                    <div class="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-xl">
                        <div class="w-12 h-12 bg-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold text-gray-900 dark:text-white">التسويق والإعلان</h4>
                    </div>
                    <div class="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-xl">
                        <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold text-gray-900 dark:text-white">التعليم والتدريب</h4>
                    </div>
                    <div class="text-center p-4 bg-purple-50 dark:bg-purple-900/20 rounded-xl">
                        <div class="w-12 h-12 bg-purple-500 rounded-xl flex items-center justify-center mx-auto mb-3">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                            </svg>
                        </div>
                        <h4 class="font-semibold text-gray-900 dark:text-white">الإبداع والفنون</h4>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="text-center">
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button onclick="downloadReport()" class="btn-primary text-white px-8 py-4 rounded-xl text-lg font-semibold inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        <span>تحميل التقرير الكامل</span>
                    </button>
                    <button onclick="shareResults()" class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-8 py-4 rounded-xl text-lg font-semibold border-2 border-gray-200 dark:border-gray-700 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300 inline-flex items-center justify-center space-x-2 rtl:space-x-reverse">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                        </svg>
                        <span>مشاركة النتائج</span>
                    </button>
                    <button onclick="retakeTest()" class="bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-8 py-4 rounded-xl text-lg font-semibold hover:bg-gray-300 dark:hover:bg-gray-600 transition-all duration-200">
                        إعادة الاختبار
                    </button>
                </div>
            </div>
        </div>
    </div>
</section>

@push('scripts')
<script>
// Personality Test Data
const personalityQuestions = [
    {
        text: "أنا شخص يحب التفاعل مع الآخرين في المناسبات الاجتماعية",
        category: "extraversion"
    },
    {
        text: "أفضل التخطيط المسبق للأنشطة بدلاً من الارتجال",
        category: "judging"
    },
    {
        text: "أركز على الحقائق والتفاصيل أكثر من الأفكار المجردة",
        category: "sensing"
    },
    {
        text: "أتخذ قراراتي بناءً على المنطق أكثر من المشاعر",
        category: "thinking"
    },
    {
        text: "أشعر بالراحة عند التحدث أمام مجموعة كبيرة من الناس",
        category: "extraversion"
    },
    {
        text: "أحب استكشاف الأفكار الجديدة والإمكانيات المختلفة",
        category: "intuition"
    },
    {
        text: "أعتبر مشاعر الآخرين عند اتخاذ القرارات",
        category: "feeling"
    },
    {
        text: "أفضل المرونة والتكيف على الالتزام بخطة محددة",
        category: "perceiving"
    },
    {
        text: "أحتاج إلى وقت للتفكير قبل التحدث في المناقشات",
        category: "introversion"
    },
    {
        text: "أهتم بالتطبيق العملي للأفكار أكثر من النظريات",
        category: "sensing"
    }
];

let currentQuestion = 0;
let answers = [];
let testType = '';

// Test Functions
function startPersonalityTest() {
    testType = 'comprehensive';
    showTestInterface();
}

function startMBTITest() {
    testType = 'mbti';
    showTestInterface();
}

function startBigFiveTest() {
    testType = 'bigfive';
    showTestInterface();
}

function startAIAnalysis() {
    testType = 'ai-enhanced';
    showTestInterface();
}

function showTestInterface() {
    document.getElementById('test-interface').classList.remove('hidden');
    document.getElementById('test-interface').scrollIntoView({ behavior: 'smooth' });
    loadQuestion();
}

function loadQuestion() {
    if (currentQuestion < personalityQuestions.length) {
        const question = personalityQuestions[currentQuestion];
        document.getElementById('question-text').textContent = question.text;
        document.getElementById('progress-text').textContent = `${currentQuestion + 1} من ${personalityQuestions.length}`;

        const progressPercent = ((currentQuestion + 1) / personalityQuestions.length) * 100;
        document.getElementById('progress-bar').style.width = progressPercent + '%';

        // Reset radio buttons
        const radioButtons = document.querySelectorAll('input[name="answer"]');
        radioButtons.forEach(radio => {
            radio.checked = false;
            radio.parentElement.classList.remove('border-blue-500');
            radio.parentElement.querySelector('.w-3').classList.add('hidden');
        });

        // Update navigation buttons
        document.getElementById('prev-btn').disabled = currentQuestion === 0;
        document.getElementById('next-btn').disabled = true;
    } else {
        // Test completed
        analyzeResults();
    }
}

function nextQuestion() {
    const selectedAnswer = document.querySelector('input[name="answer"]:checked');
    if (selectedAnswer) {
        answers[currentQuestion] = {
            question: personalityQuestions[currentQuestion],
            answer: parseInt(selectedAnswer.value)
        };

        currentQuestion++;
        loadQuestion();
    }
}

function previousQuestion() {
    if (currentQuestion > 0) {
        currentQuestion--;
        loadQuestion();

        // Restore previous answer if exists
        if (answers[currentQuestion]) {
            const previousAnswer = answers[currentQuestion].answer;
            const radioButton = document.querySelector(`input[name="answer"][value="${previousAnswer}"]`);
            if (radioButton) {
                radioButton.checked = true;
                selectAnswer(radioButton);
            }
        }
    }
}

function selectAnswer(radio) {
    // Remove selection from all options
    const allOptions = document.querySelectorAll('input[name="answer"]');
    allOptions.forEach(option => {
        option.parentElement.classList.remove('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
        option.parentElement.querySelector('.w-3').classList.add('hidden');
    });

    // Add selection to chosen option
    radio.parentElement.classList.add('border-blue-500', 'bg-blue-50', 'dark:bg-blue-900/20');
    radio.parentElement.querySelector('.w-3').classList.remove('hidden');

    // Enable next button
    document.getElementById('next-btn').disabled = false;
}

function analyzeResults() {
    // Simulate AI analysis
    document.getElementById('test-interface').classList.add('hidden');

    // Show loading animation
    showLoadingAnalysis();

    // Simulate processing time
    setTimeout(() => {
        hideLoadingAnalysis();
        showResults();
    }, 3000);
}

function showLoadingAnalysis() {
    const loadingHTML = `
        <div id="loading-analysis" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md mx-4 text-center">
                <div class="animate-spin w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"></div>
                <h3 class="text-xl font-bold text-gray-900 dark:text-white mb-2">جاري تحليل شخصيتك</h3>
                <p class="text-gray-600 dark:text-gray-300 mb-4">الذكاء الاصطناعي يحلل إجاباتك...</p>
                <div class="text-sm text-gray-500 dark:text-gray-400" id="analysis-step">تحليل الأنماط السلوكية...</div>
            </div>
        </div>
    `;
    document.body.insertAdjacentHTML('beforeend', loadingHTML);

    // Simulate analysis steps
    const steps = [
        "تحليل الأنماط السلوكية...",
        "تقييم نقاط القوة والضعف...",
        "إنشاء التوصيات المهنية...",
        "إعداد التقرير النهائي..."
    ];

    let stepIndex = 0;
    const stepInterval = setInterval(() => {
        stepIndex++;
        if (stepIndex < steps.length) {
            document.getElementById('analysis-step').textContent = steps[stepIndex];
        } else {
            clearInterval(stepInterval);
        }
    }, 750);
}

function hideLoadingAnalysis() {
    const loadingElement = document.getElementById('loading-analysis');
    if (loadingElement) {
        loadingElement.remove();
    }
}

function showResults() {
    document.getElementById('results-section').classList.remove('hidden');
    document.getElementById('results-section').scrollIntoView({ behavior: 'smooth' });
}

function showSampleResults() {
    document.getElementById('results-section').classList.remove('hidden');
    document.getElementById('results-section').scrollIntoView({ behavior: 'smooth' });
}

function downloadReport() {
    // Simulate PDF download
    alert('سيتم تحميل التقرير الكامل قريباً!');
}

function shareResults() {
    // Simulate sharing
    if (navigator.share) {
        navigator.share({
            title: 'نتائج تحليل شخصيتي - MonOri AI',
            text: 'اكتشفت نمط شخصيتي باستخدام MonOri AI!',
            url: window.location.href
        });
    } else {
        // Fallback for browsers that don't support Web Share API
        const url = window.location.href;
        navigator.clipboard.writeText(url).then(() => {
            alert('تم نسخ الرابط! يمكنك مشاركته الآن.');
        });
    }
}

function retakeTest() {
    currentQuestion = 0;
    answers = [];
    document.getElementById('results-section').classList.add('hidden');
    showTestInterface();
}

// Event Listeners
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for radio buttons
    const radioButtons = document.querySelectorAll('input[name="answer"]');
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            selectAnswer(this);
        });
    });
});
</script>
@endpush
@endsection