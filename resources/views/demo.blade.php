@extends('layouts.app')

@section('title', 'Demo - MonOri AI')

@section('content')
<style>
    .demo-container {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        position: relative;
        overflow: hidden;
    }
    
    .demo-container::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="10" cy="60" r="0.5" fill="rgba(255,255,255,0.1)"/><circle cx="90" cy="40" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.3;
    }
    
    .demo-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 24px;
        box-shadow: 0 25px 80px rgba(0, 0, 0, 0.3);
        border: 1px solid rgba(255, 255, 255, 0.3);
        position: relative;
        z-index: 1;
    }
    
    .step-card {
        background: white;
        border-radius: 16px;
        padding: 2rem;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
        border: 1px solid #e5e7eb;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }
    
    .step-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, #667eea, #764ba2);
        transform: scaleX(0);
        transition: transform 0.3s ease;
    }
    
    .step-card:hover::before {
        transform: scaleX(1);
    }
    
    .step-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }
    
    .step-number {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        font-weight: bold;
        margin-bottom: 1rem;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
    }
    
    .demo-video-placeholder {
        background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
        border-radius: 16px;
        height: 300px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .demo-video-placeholder:hover {
        transform: scale(1.02);
        box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    }
    
    .play-button {
        width: 80px;
        height: 80px;
        background: rgba(102, 126, 234, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.4);
    }
    
    .play-button:hover {
        background: rgba(102, 126, 234, 1);
        transform: scale(1.1);
    }
    
    .feature-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        margin-bottom: 1rem;
    }
    
    .cta-button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        padding: 1rem 2rem;
        border-radius: 50px;
        font-weight: 600;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
        gap: 0.5rem;
        transition: all 0.3s ease;
        box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    }
    
    .cta-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
        color: white;
    }
    
    .floating-element {
        position: absolute;
        background: rgba(255, 255, 255, 0.1);
        border-radius: 50%;
        animation: float 6s ease-in-out infinite;
    }
    
    .floating-element:nth-child(1) {
        width: 60px;
        height: 60px;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
    }
    
    .floating-element:nth-child(2) {
        width: 40px;
        height: 40px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
    }
    
    .floating-element:nth-child(3) {
        width: 80px;
        height: 80px;
        bottom: 20%;
        left: 20%;
        animation-delay: 4s;
    }
    
    @keyframes float {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(180deg); }
    }
    
    .demo-modal {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.8);
        display: none;
        align-items: center;
        justify-content: center;
        z-index: 1000;
        padding: 2rem;
    }
    
    .demo-modal.active {
        display: flex;
    }
    
    .modal-content {
        background: white;
        border-radius: 20px;
        padding: 2rem;
        max-width: 800px;
        width: 100%;
        max-height: 90vh;
        overflow-y: auto;
        position: relative;
    }
    
    .close-modal {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: #f3f4f6;
        border: none;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
    }
    
    .close-modal:hover {
        background: #e5e7eb;
        transform: scale(1.1);
    }
</style>

<div class="demo-container">
    <!-- Floating Elements -->
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    <div class="floating-element"></div>
    
    <div class="container mx-auto px-4 py-12 relative z-10">
        <!-- Header -->
        <div class="text-center mb-12">
            <h1 class="text-5xl font-bold text-white mb-6">
                @if(app()->getLocale() === 'ar')
                    شاهد كيف يعمل منشئ السيرة الذاتية
                @elseif(app()->getLocale() === 'fr')
                    Découvrez comment fonctionne le générateur de CV
                @else
                    See How Our CV Builder Works
                @endif
            </h1>
            <p class="text-xl text-white/80 max-w-3xl mx-auto">
                @if(app()->getLocale() === 'ar')
                    اكتشف كيف يمكن للذكاء الاصطناعي أن يحول معلوماتك البسيطة إلى سيرة ذاتية احترافية ومتميزة
                @elseif(app()->getLocale() === 'fr')
                    Découvrez comment l'IA peut transformer vos informations simples en un CV professionnel et exceptionnel
                @else
                    Discover how AI can transform your simple information into a professional and outstanding resume
                @endif
            </p>
        </div>

        <!-- Demo Video Section -->
        <div class="demo-card max-w-4xl mx-auto mb-12 p-8">
            <div class="demo-video-placeholder" onclick="openDemoModal()">
                <div class="play-button">
                    <svg fill="currentColor" viewBox="0 0 24 24" width="32" height="32">
                        <path d="M8 5v14l11-7z"/>
                    </svg>
                </div>
                <div class="absolute inset-0 bg-gradient-to-r from-blue-500/20 to-purple-500/20"></div>
                <div class="absolute bottom-4 left-4 text-gray-700 font-semibold">
                    @if(app()->getLocale() === 'ar')
                        انقر لمشاهدة العرض التوضيحي
                    @elseif(app()->getLocale() === 'fr')
                        Cliquez pour voir la démo
                    @else
                        Click to Watch Demo
                    @endif
                </div>
            </div>
        </div>

        <!-- Steps Section -->
        <div class="max-w-6xl mx-auto mb-12">
            <h2 class="text-3xl font-bold text-white text-center mb-8">
                @if(app()->getLocale() === 'ar')
                    كيف يعمل النظام؟
                @elseif(app()->getLocale() === 'fr')
                    Comment ça marche?
                @else
                    How It Works?
                @endif
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <!-- Step 1 -->
                <div class="step-card" onclick="showStepDemo(1)">
                    <div class="step-number">1</div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            اختر القالب
                        @elseif(app()->getLocale() === 'fr')
                            Choisir le modèle
                        @else
                            Choose Template
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if(app()->getLocale() === 'ar')
                            اختر من بين 6 قوالب احترافية مختلفة
                        @elseif(app()->getLocale() === 'fr')
                            Choisissez parmi 6 modèles professionnels différents
                        @else
                            Select from 6 different professional templates
                        @endif
                    </p>
                </div>

                <!-- Step 2 -->
                <div class="step-card" onclick="showStepDemo(2)">
                    <div class="step-number">2</div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            أدخل المعلومات
                        @elseif(app()->getLocale() === 'fr')
                            Saisir les informations
                        @else
                            Enter Information
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if(app()->getLocale() === 'ar')
                            املأ معلوماتك الشخصية والمهنية
                        @elseif(app()->getLocale() === 'fr')
                            Remplissez vos informations personnelles et professionnelles
                        @else
                            Fill in your personal and professional information
                        @endif
                    </p>
                </div>

                <!-- Step 3 -->
                <div class="step-card" onclick="showStepDemo(3)">
                    <div class="step-number">3</div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            تحسين بالذكاء الاصطناعي
                        @elseif(app()->getLocale() === 'fr')
                            Amélioration par IA
                        @else
                            AI Enhancement
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if(app()->getLocale() === 'ar')
                            الذكاء الاصطناعي يحسن ويثري المحتوى
                        @elseif(app()->getLocale() === 'fr')
                            L'IA améliore et enrichit le contenu
                        @else
                            AI improves and enriches the content
                        @endif
                    </p>
                </div>

                <!-- Step 4 -->
                <div class="step-card" onclick="showStepDemo(4)">
                    <div class="step-number">4</div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            حمل PDF
                        @elseif(app()->getLocale() === 'fr')
                            Télécharger PDF
                        @else
                            Download PDF
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if(app()->getLocale() === 'ar')
                            احصل على سيرة ذاتية احترافية جاهزة
                        @elseif(app()->getLocale() === 'fr')
                            Obtenez un CV professionnel prêt
                        @else
                            Get a professional resume ready
                        @endif
                    </p>
                </div>
            </div>
        </div>

        <!-- Features Section -->
        <div class="demo-card max-w-4xl mx-auto mb-12 p-8">
            <h2 class="text-3xl font-bold text-gray-800 text-center mb-8">
                @if(app()->getLocale() === 'ar')
                    المميزات الرئيسية
                @elseif(app()->getLocale() === 'fr')
                    Fonctionnalités principales
                @else
                    Key Features
                @endif
            </h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                    <div class="feature-icon mx-auto">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            تحسين بالذكاء الاصطناعي
                        @elseif(app()->getLocale() === 'fr')
                            Amélioration par IA
                        @else
                            AI Enhancement
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if(app()->getLocale() === 'ar')
                            يضيف الذكاء الاصطناعي محتوى احترافي ومفصل
                        @elseif(app()->getLocale() === 'fr')
                            L'IA ajoute du contenu professionnel et détaillé
                        @else
                            AI adds professional and detailed content
                        @endif
                    </p>
                </div>

                <div class="text-center">
                    <div class="feature-icon mx-auto">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            قوالب متعددة
                        @elseif(app()->getLocale() === 'fr')
                            Modèles multiples
                        @else
                            Multiple Templates
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if(app()->getLocale() === 'ar')
                            6 قوالب احترافية مختلفة للاختيار من بينها
                        @elseif(app()->getLocale() === 'fr')
                            6 modèles professionnels différents au choix
                        @else
                            6 different professional templates to choose from
                        @endif
                    </p>
                </div>

                <div class="text-center">
                    <div class="feature-icon mx-auto">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.01-4.65.75-6.78l-1.08-1.83c-.28-.48-.89-.64-1.37-.36-.48.28-.64.89-.36 1.37l1.08 1.83c.96 1.58.84 3.54-.36 5.07l-2.51 2.54c-.48.48-.48 1.26 0 1.74.24.24.55.36.87.36s.63-.12.87-.36l2.54-2.51.03.03c1.74 1.94 2.01 4.65.75 6.78l-1.08 1.83c-.28.48-.12 1.09.36 1.37.16.09.33.14.5.14.35 0 .69-.18.87-.5l1.08-1.83c.96-1.58.84-3.54-.36-5.07z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-800 mb-3">
                        @if(app()->getLocale() === 'ar')
                            دعم متعدد اللغات
                        @elseif(app()->getLocale() === 'fr')
                            Support multilingue
                        @else
                            Multi-language Support
                        @endif
                    </h3>
                    <p class="text-gray-600">
                        @if(app()->getLocale() === 'ar')
                            إنشاء السيرة الذاتية بالعربية والإنجليزية والفرنسية
                        @elseif(app()->getLocale() === 'fr')
                            Créer un CV en arabe, anglais et français
                        @else
                            Create CV in Arabic, English and French
                        @endif
                    </p>
                </div>
            </div>
        </div>

        <!-- CTA Section -->
        <div class="text-center">
            <h2 class="text-3xl font-bold text-white mb-6">
                @if(app()->getLocale() === 'ar')
                    جاهز لإنشاء سيرتك الذاتية؟
                @elseif(app()->getLocale() === 'fr')
                    Prêt à créer votre CV?
                @else
                    Ready to Create Your CV?
                @endif
            </h2>
            <a href="{{ route('cv.builder') }}" class="cta-button">
                @if(app()->getLocale() === 'ar')
                    ابدأ الآن مجاناً
                @elseif(app()->getLocale() === 'fr')
                    Commencer gratuitement
                @else
                    Start Now for Free
                @endif
                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 4l-1.41 1.41L16.17 11H4v2h12.17l-5.58 5.59L12 20l8-8z"/>
                </svg>
            </a>
        </div>
    </div>
</div>

<!-- Demo Modal -->
<div class="demo-modal" id="demoModal">
    <div class="modal-content">
        <button class="close-modal" onclick="closeDemoModal()">
            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
            </svg>
        </button>
        
        <div id="modalContent">
            <!-- Content will be loaded here -->
        </div>
    </div>
</div>

<script>
function openDemoModal() {
    const modal = document.getElementById('demoModal');
    const content = document.getElementById('modalContent');
    
    content.innerHTML = `
        <div class="text-center">
            <h2 class="text-2xl font-bold text-gray-800 mb-6">CV Builder Demo</h2>
            <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-8 text-white mb-6">
                <h3 class="text-xl font-bold mb-4">🎬 شاهد الفيديو التعريفي</h3>
                <p class="mb-4">فيديو تعريفي كامل بالموقع مع صوت راوي احترافي يشرح جميع المميزات</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="{{ route('video-demo') }}" class="bg-white text-blue-600 px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                        🎥 شاهد الفيديو التعريفي
                    </a>
                    <a href="{{ route('cv.builder') }}" class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors">
                        جرب منشئ السيرة الذاتية
                    </a>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-left">
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-bold text-gray-800 mb-2">✨ What You'll Get:</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• AI-enhanced content</li>
                        <li>• Professional templates</li>
                        <li>• Multi-language support</li>
                        <li>• Instant PDF download</li>
                    </ul>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg">
                    <h4 class="font-bold text-gray-800 mb-2">🚀 How It Works:</h4>
                    <ul class="text-sm text-gray-600 space-y-1">
                        <li>• Choose your template</li>
                        <li>• Fill in your information</li>
                        <li>• AI enhances your content</li>
                        <li>• Download professional PDF</li>
                    </ul>
                </div>
            </div>
        </div>
    `;
    
    modal.classList.add('active');
}

function closeDemoModal() {
    const modal = document.getElementById('demoModal');
    modal.classList.remove('active');
}

function showStepDemo(step) {
    const modal = document.getElementById('demoModal');
    const content = document.getElementById('modalContent');
    
    const stepContent = {
        1: {
            title: 'Step 1: Choose Template',
            description: 'Select from 6 professional CV templates designed for different industries and styles.',
            features: ['Modern Professional', 'Classic Elegant', 'Creative Designer', 'Minimal Clean', 'Executive Premium', 'Tech Specialist']
        },
        2: {
            title: 'Step 2: Enter Information',
            description: 'Fill in your personal details, work experience, education, and skills in our user-friendly form.',
            features: ['Personal Information', 'Work Experience', 'Education', 'Skills & Languages']
        },
        3: {
            title: 'Step 3: AI Enhancement',
            description: 'Our AI analyzes your information and enhances it with professional language and additional content.',
            features: ['Professional Summary', 'Enhanced Descriptions', 'Key Achievements', 'Core Competencies']
        },
        4: {
            title: 'Step 4: Download PDF',
            description: 'Get your professionally formatted CV as a high-quality PDF ready for job applications.',
            features: ['High-Quality PDF', 'Professional Formatting', 'Multi-language Support', 'Instant Download']
        }
    };
    
    const stepData = stepContent[step];
    
    content.innerHTML = `
        <div class="text-center">
            <div class="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-2xl font-bold mx-auto mb-4">
                ${step}
            </div>
            <h2 class="text-2xl font-bold text-gray-800 mb-4">${stepData.title}</h2>
            <p class="text-gray-600 mb-6">${stepData.description}</p>
            
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6 mb-6">
                <h3 class="text-lg font-bold text-gray-800 mb-4">Features:</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
                    ${stepData.features.map(feature => `
                        <div class="flex items-center text-gray-700">
                            <svg class="w-4 h-4 text-green-500 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                            </svg>
                            ${feature}
                        </div>
                    `).join('')}
                </div>
            </div>
            
            <a href="{{ route('cv.builder') }}" class="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:opacity-90 transition-opacity">
                Try It Now
            </a>
        </div>
    `;
    
    modal.classList.add('active');
}

// Close modal when clicking outside
document.getElementById('demoModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDemoModal();
    }
});
</script>
@endsection
