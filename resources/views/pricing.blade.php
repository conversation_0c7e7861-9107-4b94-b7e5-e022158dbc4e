@extends('layouts.app')

@section('title', __('messages.pricing_title') . ' - MonOri AI')
@section('description', __('messages.pricing_description'))

@section('content')
<section class="py-20 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-purple-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- Header -->
        <div class="text-center mb-16">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900 dark:text-white mb-6">
                {{ __('messages.pricing_title') }}
            </h1>
            <p class="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto mb-8">
                {{ __('messages.pricing_description') }}
            </p>
            
            <!-- Billing Toggle -->
            <div class="flex items-center justify-center mb-8">
                <span class="text-gray-600 dark:text-gray-400 mr-3">{{ __('messages.monthly') }}</span>
                <label class="relative inline-flex items-center cursor-pointer">
                    <input type="checkbox" id="billing-toggle" class="sr-only peer">
                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                </label>
                <span class="text-gray-600 dark:text-gray-400 ml-3">{{ __('messages.yearly') }}</span>
                <span class="bg-green-100 text-green-800 text-xs font-medium px-2.5 py-0.5 rounded-full ml-2">{{ __('messages.save_up_to') }}</span>
            </div>
        </div>

        <!-- Pricing Cards - Only show 3 plans: Basic, Gold, Premium -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16 max-w-7xl mx-auto items-stretch">
            @foreach($subscriptionPlans->take(3) as $index => $plan)
            @php
                $colors = [
                    ['primary' => 'blue', 'gradient' => 'from-blue-500 to-cyan-500', 'bg' => 'bg-blue-50', 'text' => 'text-blue-600'],
                    ['primary' => 'amber', 'gradient' => 'from-amber-500 to-orange-500', 'bg' => 'bg-amber-50', 'text' => 'text-amber-600'],
                    ['primary' => 'purple', 'gradient' => 'from-purple-500 to-pink-500', 'bg' => 'bg-purple-50', 'text' => 'text-purple-600']
                ];
                $color = $colors[$index % count($colors)];
                $isPopular = $index === 1; // Gold Plan (index 1) is popular
            @endphp

            <div class="relative group">
                <!-- Popular Badge -->
                @if($isPopular)
                <div class="absolute -top-5 left-1/2 transform -translate-x-1/2 z-10">
                    <div class="bg-gradient-to-r {{ $color['gradient'] }} text-white px-6 py-2 rounded-full text-sm font-bold shadow-lg">
                        ⭐ {{ __('messages.most_popular') }}
                    </div>
                </div>
                @endif

                <!-- Current Plan Badge -->
                @if($currentSubscription && $currentSubscription->subscription_plan_id === $plan->id)
                <div class="absolute -top-3 -right-3 z-10">
                    <div class="bg-green-500 text-white p-2 rounded-full shadow-lg">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                </div>
                @endif

                <!-- Card -->
                <div class="relative bg-white dark:bg-gray-800 rounded-3xl shadow-xl hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 {{ $isPopular ? 'ring-4 ring-amber-200 dark:ring-amber-800 scale-105' : '' }} overflow-hidden flex flex-col h-full">

                    <!-- Header with Gradient -->
                    <div class="relative bg-gradient-to-br {{ $color['gradient'] }} p-8 text-white">
                        <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
                        <div class="absolute bottom-0 left-0 w-24 h-24 bg-white/10 rounded-full translate-y-12 -translate-x-12"></div>

                        <div class="relative z-10 text-center">
                            <!-- Icon -->
                            <div class="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center mx-auto mb-4">
                                @if($index === 0)
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>
                                @elseif($index === 1)
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"></path>
                                </svg>
                                @else
                                <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                                @endif
                            </div>

                            <!-- Plan Name -->
                            <h3 class="text-2xl font-bold mb-2">{{ $plan->localized_name }}</h3>
                            <p class="text-white/80 text-sm">{{ $plan->localized_description }}</p>
                        </div>
                    </div>

                    <!-- Pricing Section -->
                    <div class="p-8 text-center border-b border-gray-100 dark:border-gray-700">
                        <div class="price-container">
                            <div class="flex items-baseline justify-center mb-2">
                                <span class="monthly-price text-5xl font-black {{ $color['text'] }} dark:text-white">{{ number_format($plan->price_monthly, 0) }}</span>
                                <span class="yearly-price text-5xl font-black {{ $color['text'] }} dark:text-white hidden">{{ number_format($plan->price_yearly, 0) }}</span>
                                <span class="text-gray-500 dark:text-gray-400 text-lg mr-2">{{ __('messages.currency') }}</span>
                            </div>
                            <div class="billing-period text-gray-600 dark:text-gray-400 font-medium">{{ __('messages.per_month_short') }}</div>
                        </div>

                        @if($plan->yearly_savings > 0)
                        <div class="yearly-savings hidden mt-3">
                            <div class="inline-flex items-center bg-green-100 dark:bg-green-900/20 text-green-700 dark:text-green-400 px-3 py-1 rounded-full text-sm font-medium">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                                </svg>
                                {{ __('messages.save_yearly', ['percentage' => $plan->yearly_savings_percentage]) }}
                            </div>
                        </div>
                        @endif
                    </div>

                    <!-- Features Section -->
                    <div class="p-8 flex-grow">
                        <h4 class="font-bold text-gray-900 dark:text-white mb-6 text-center">{{ __('messages.included_features') }}</h4>
                        <ul class="space-y-4">
                            @foreach($plan->localized_features as $feature)
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-br {{ $color['gradient'] }} rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{{ $feature }}</span>
                            </li>
                            @endforeach

                            <!-- AI Requests -->
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-br {{ $color['gradient'] }} rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                                    <span class="font-semibold {{ $color['text'] }}">{{ $plan->ai_requests_limit }}</span> {{ __('messages.ai_requests_monthly') }}
                                </span>
                            </li>

                            <!-- CV Generations -->
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-br {{ $color['gradient'] }} rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">
                                    <span class="font-semibold {{ $color['text'] }}">{{ $plan->cv_generations_limit }}</span> {{ __('messages.cv_monthly') }}
                                </span>
                            </li>

                            @if($plan->pdf_export)
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-br {{ $color['gradient'] }} rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{{ __('messages.high_quality_pdf_export') }}</span>
                            </li>
                            @endif

                            @if($plan->voice_interviews)
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-br {{ $color['gradient'] }} rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{{ __('messages.interactive_voice_interviews') }}</span>
                            </li>
                            @endif

                            @if($plan->personal_coaching)
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-br {{ $color['gradient'] }} rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{{ __('messages.personal_coaching_sessions') }}</span>
                            </li>
                            @endif

                            @if($plan->priority_support)
                            <li class="flex items-start">
                                <div class="flex-shrink-0 w-6 h-6 bg-gradient-to-br {{ $color['gradient'] }} rounded-full flex items-center justify-center mr-3 mt-0.5">
                                    <svg class="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                </div>
                                <span class="text-gray-700 dark:text-gray-300 text-sm leading-relaxed">{{ __('messages.priority_support_24_7') }}</span>
                            </li>
                            @endif
                        </ul>
                    </div>

                    <!-- CTA Button -->
                    <div class="p-8 pt-0 mt-auto">
                        @if($currentSubscription && $currentSubscription->subscription_plan_id === $plan->id)
                        <button disabled class="w-full bg-gray-100 dark:bg-gray-700 text-gray-500 dark:text-gray-400 font-bold py-4 px-6 rounded-2xl cursor-not-allowed flex items-center justify-center h-14">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            {{ __('messages.current_plan') }}
                        </button>
                        @else
                        <button onclick="subscribeToPlan({{ $plan->id }}, '{{ $plan->localized_name }}')"
                                class="w-full bg-gradient-to-r {{ $color['gradient'] }} hover:shadow-lg hover:shadow-{{ $color['primary'] }}-500/25 text-white font-bold py-4 px-6 rounded-2xl transition-all duration-300 transform hover:scale-105 flex items-center justify-center group h-14">
                            <span>{{ __('messages.choose_plan', ['plan' => $plan->localized_name]) }}</span>
                            <svg class="w-5 h-5 mr-2 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </button>
                        @endif
                    </div>
                </div>
            </div>
            @endforeach
        </div>

        <!-- Paid Services Section -->
        <div class="bg-white dark:bg-gray-800 rounded-2xl shadow-xl p-8 mb-16">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-4">{{ __('messages.paid_services_title') }}</h2>
                <p class="text-xl text-gray-600 dark:text-gray-300">{{ __('messages.paid_services_description') }}</p>
            </div>

            <!-- Show only 4 unique services: CV Creation, CV Improvement, Personal Coaching, Interview Training -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                @foreach($paidServices->take(4) as $index => $service)
                @php
                    $serviceColors = [
                        ['gradient' => 'from-blue-500 to-cyan-500', 'bg' => 'bg-blue-50', 'text' => 'text-blue-600', 'ring' => 'ring-blue-200'],
                        ['gradient' => 'from-emerald-500 to-teal-500', 'bg' => 'bg-emerald-50', 'text' => 'text-emerald-600', 'ring' => 'ring-emerald-200'],
                        ['gradient' => 'from-purple-500 to-indigo-500', 'bg' => 'bg-purple-50', 'text' => 'text-purple-600', 'ring' => 'ring-purple-200'],
                        ['gradient' => 'from-orange-500 to-red-500', 'bg' => 'bg-orange-50', 'text' => 'text-orange-600', 'ring' => 'ring-orange-200'],
                        ['gradient' => 'from-pink-500 to-rose-500', 'bg' => 'bg-pink-50', 'text' => 'text-pink-600', 'ring' => 'ring-pink-200'],
                        ['gradient' => 'from-indigo-500 to-blue-500', 'bg' => 'bg-indigo-50', 'text' => 'text-indigo-600', 'ring' => 'ring-indigo-200']
                    ];
                    $color = $serviceColors[$index % count($serviceColors)];
                @endphp

                <!-- Service Card -->
                <div class="bg-gradient-to-br {{ $color['gradient'] }} rounded-xl p-5 text-white hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden group">
                    <!-- Background Pattern -->
                    <div class="absolute top-0 right-0 w-20 h-20 bg-white/10 rounded-full -translate-y-10 translate-x-10 group-hover:scale-110 transition-transform duration-300"></div>
                    <div class="absolute bottom-0 left-0 w-16 h-16 bg-white/10 rounded-full translate-y-8 -translate-x-8 group-hover:scale-110 transition-transform duration-300"></div>

                    <div class="relative z-10">
                        <!-- Header -->
                        <div class="text-center mb-4">
                            <div class="w-12 h-12 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mx-auto mb-3 group-hover:scale-110 transition-transform duration-300">
                                @if($service->type === 'cv_creation')
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                @elseif($service->type === 'cv_improvement')
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                </svg>
                                @elseif($service->type === 'personal_coaching')
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                </svg>
                                @elseif($service->type === 'interview_training')
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                                </svg>
                                @else
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                                @endif
                            </div>

                            <h3 class="text-lg font-bold mb-1">{{ $service->{'name_' . app()->getLocale()} ?? $service->name }}</h3>
                            <p class="text-white/90 text-xs leading-relaxed">{{ $service->{'description_' . app()->getLocale()} ?? $service->description }}</p>
                        </div>

                        <!-- Price -->
                        <div class="text-center mb-5">
                            <div class="inline-flex items-baseline bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2">
                                <span class="text-2xl font-black">{{ number_format($service->price, 0) }}</span>
                                <span class="text-sm mr-1">{{ __('messages.currency') }}</span>
                            </div>
                            <div class="mt-1 text-white/80 text-xs">
                                ⚡ {{ __('messages.delivery_within') }} {{ $service->delivery_time_hours }} {{ __('messages.hours') }}
                            </div>
                        </div>

                        <!-- Features -->
                        @if($service->features && count($service->features) > 0)
                        <div class="mb-5">
                            <h4 class="font-semibold mb-2 text-center text-sm">{{ __('messages.included_features') }}</h4>
                            <ul class="space-y-1">
                                @foreach($service->localized_features as $feature)
                                <li class="flex items-start">
                                    <div class="flex-shrink-0 w-3 h-3 bg-white/30 rounded-full flex items-center justify-center mr-2 mt-1">
                                        <svg class="w-2 h-2 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="3" d="M5 13l4 4L19 7"></path>
                                        </svg>
                                    </div>
                                    <span class="text-white/95 text-xs leading-relaxed">{{ $feature }}</span>
                                </li>
                                @endforeach
                            </ul>
                        </div>
                        @endif

                        <!-- CTA Button -->
                        @auth
                        <button onclick="orderService({{ $service->id }}, '{{ $service->{'name_' . app()->getLocale()} ?? $service->name }}')"
                                class="w-full bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 flex items-center justify-center group-hover:shadow-lg text-sm">
                            <span>{{ __('messages.order_service') }}</span>
                            <svg class="w-4 h-4 mr-1 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </button>
                        @else
                        <a href="{{ route('login') }}" class="block w-full bg-white/20 hover:bg-white/30 backdrop-blur-sm text-white font-semibold py-2 px-4 rounded-lg transition-all duration-300 transform hover:scale-105 text-center group-hover:shadow-lg text-sm">
                            {{ __('messages.login_to_order') }}
                        </a>
                        @endauth
                    </div>
                </div>
                @endforeach
            </div>
        </div>

        <!-- FAQ Section -->
        <div class="text-center">
            <h2 class="text-3xl font-bold text-gray-900 dark:text-white mb-8">{{ __('messages.faq_title') }}</h2>
            <div class="max-w-3xl mx-auto space-y-4">
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-left">
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.faq_cancel_question') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('messages.faq_cancel_answer') }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-left">
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.faq_payment_question') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('messages.faq_payment_answer') }}</p>
                </div>
                <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-left">
                    <h3 class="font-semibold text-gray-900 dark:text-white mb-2">{{ __('messages.faq_refund_question') }}</h3>
                    <p class="text-gray-600 dark:text-gray-400">{{ __('messages.faq_refund_answer') }}</p>
                </div>
            </div>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const billingToggle = document.getElementById('billing-toggle');
    const monthlyPrices = document.querySelectorAll('.monthly-price');
    const yearlyPrices = document.querySelectorAll('.yearly-price');
    const billingPeriods = document.querySelectorAll('.billing-period');
    const yearlySavings = document.querySelectorAll('.yearly-savings');

    billingToggle.addEventListener('change', function() {
        if (this.checked) {
            // Show yearly prices
            monthlyPrices.forEach(price => price.classList.add('hidden'));
            yearlyPrices.forEach(price => price.classList.remove('hidden'));
            billingPeriods.forEach(period => period.textContent = '{{ __("messages.per_year_short") }}');
            yearlySavings.forEach(saving => saving.classList.remove('hidden'));
        } else {
            // Show monthly prices
            monthlyPrices.forEach(price => price.classList.remove('hidden'));
            yearlyPrices.forEach(price => price.classList.add('hidden'));
            billingPeriods.forEach(period => period.textContent = '{{ __("messages.per_month_short") }}');
            yearlySavings.forEach(saving => saving.classList.add('hidden'));
        }
    });
});

// Subscribe to plan function
async function subscribeToPlan(planId, planName) {
    @guest
        window.location.href = '{{ route("login") }}';
        return;
    @endguest

    // Show payment method selection modal
    showPaymentMethodModal('subscription', planId, planName);
}

// Order service function
async function orderService(serviceId, serviceName) {
    @guest
        window.location.href = '{{ route("login") }}';
        return;
    @endguest

    // Show payment method selection modal
    showPaymentMethodModal('service', serviceId, serviceName);
}

// Check subscription status on page load
@auth
document.addEventListener('DOMContentLoaded', async function() {
    try {
        const response = await fetch('/subscription/status');
        const status = await response.json();

        if (status.has_subscription) {
            console.log('User has active subscription:', status.subscription);
            // Update UI to show current subscription status
        }
    } catch (error) {
        console.error('Error checking subscription status:', error);
    }
});
@endauth

// Show payment method selection modal
function showPaymentMethodModal(type, id, name) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
    modal.innerHTML = `
        <div class="bg-white dark:bg-gray-800 rounded-2xl p-8 max-w-md w-full mx-4">
            <h3 class="text-2xl font-bold text-gray-900 dark:text-white mb-4 text-center">{{ __('messages.choose_payment_method') }}</h3>
            <p class="text-gray-600 dark:text-gray-400 mb-6 text-center">${name}</p>

            ${type === 'subscription' ? `
            <div class="mb-6">
                <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">{{ __('messages.billing_cycle') }}</label>
                <select id="billing-cycle" class="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white">
                    <option value="monthly">{{ __('messages.monthly') }}</option>
                    <option value="yearly">{{ __('messages.yearly_save') }}</option>
                </select>
            </div>
            ` : ''}

            <div class="space-y-4 mb-6">
                <button onclick="processPayment('${type}', ${id}, 'stripe')" class="w-full bg-blue-600 hover:bg-blue-700 text-white font-bold py-3 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                    </svg>
                    {{ __('messages.pay_with_card') }}
                </button>

                <button onclick="processPayment('${type}', ${id}, 'paypal')" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-3 px-6 rounded-xl transition-colors duration-200 flex items-center justify-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z"></path>
                    </svg>
                    {{ __('messages.pay_with_paypal') }}
                </button>
            </div>

            <button onclick="closeModal()" class="w-full bg-gray-300 hover:bg-gray-400 text-gray-700 font-bold py-3 px-6 rounded-xl transition-colors duration-200">
                {{ __('messages.cancel') }}
            </button>
        </div>
    `;

    document.body.appendChild(modal);

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeModal();
        }
    });
}

// Process payment
function processPayment(type, id, paymentMethod) {
    const billingCycle = type === 'subscription' ? document.getElementById('billing-cycle').value : 'monthly';

    // Show loading
    const button = event.target;
    const originalText = button.innerHTML;
    button.innerHTML = '<div class="animate-spin rounded-full h-5 w-5 border-b-2 border-white mx-auto"></div>';
    button.disabled = true;

    // Prepare data
    const data = {
        payment_method: paymentMethod,
        _token: document.querySelector('meta[name="csrf-token"]').getAttribute('content')
    };

    if (type === 'subscription') {
        data.plan_id = id;
        data.billing_cycle = billingCycle;
    } else {
        data.service_id = id;
    }

    // Send request
    const url = type === 'subscription' ? '/payment/subscribe' : '/payment/service/order';

    fetch(url, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': data._token
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Redirect to payment page
            window.location.href = data.payment_url;
        } else {
            alert(data.message || '{{ __("messages.payment_error") }}');
            button.innerHTML = originalText;
            button.disabled = false;
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('{{ __("messages.connection_error") }}');
        button.innerHTML = originalText;
        button.disabled = false;
    });
}

// Close modal
function closeModal() {
    const modal = document.querySelector('.fixed.inset-0');
    if (modal) {
        modal.remove();
    }
}
</script>
@endsection
