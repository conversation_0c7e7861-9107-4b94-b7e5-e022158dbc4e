@extends('layouts.app')

@section('title', 'Test Payment System')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900 py-8">
    <div class="container mx-auto px-4">
        <div class="max-w-4xl mx-auto">
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-white mb-4">Test Dynamic Payment System</h1>
                <p class="text-gray-300 text-lg">اختبار نظام الدفع الديناميكي المتصل بقاعدة البيانات</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                @foreach($services as $service)
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50 hover:border-blue-500/50 transition-all duration-300">
                    <div class="text-center mb-4">
                        <div class="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-{{ $service->icon ?? 'star' }} text-white text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-white mb-2">{{ $service->name }}</h3>
                        <p class="text-gray-400 text-sm mb-4">{{ $service->description }}</p>
                    </div>

                    <div class="space-y-2 mb-6">
                        @if($service->features)
                            @foreach(is_array($service->features) ? $service->features : json_decode($service->features, true) as $feature)
                                <div class="flex items-center text-gray-300 text-sm">
                                    <i class="fas fa-check text-green-400 mr-2"></i>
                                    <span>{{ $feature }}</span>
                                </div>
                            @endforeach
                        @endif
                    </div>

                    <div class="border-t border-gray-700 pt-4 mb-6">
                        <div class="flex justify-between items-center mb-2">
                            <span class="text-gray-400">{{ __('messages.delivery_time') }}</span>
                            <span class="text-white">{{ $service->delivery_time_hours }} {{ __('messages.hours') }}</span>
                        </div>
                        <div class="flex justify-between items-center text-2xl font-bold">
                            <span class="text-gray-400">{{ __('messages.price') }}</span>
                            <span class="text-blue-400">{{ number_format($service->price, 2) }} {{ $service->currency }}</span>
                        </div>
                    </div>

                    <a href="{{ route('payment.show', $service) }}" 
                       class="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white font-semibold py-3 px-6 rounded-xl transition-all duration-300 transform hover:scale-105 text-center block">
                        <i class="fas fa-credit-card mr-2"></i>
                        {{ __('messages.order_now') }}
                    </a>
                </div>
                @endforeach
            </div>

            <!-- Admin Panel Link -->
            @auth
                @if(auth()->user()->is_admin)
                <div class="mt-12 text-center">
                    <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
                        <h3 class="text-xl font-semibold text-white mb-4">{{ __('messages.admin_panel') }}</h3>
                        <p class="text-gray-400 mb-6">{{ __('messages.manage_payments_and_transactions') }}</p>
                        <a href="{{ route('admin.payments.index') }}" 
                           class="bg-gradient-to-r from-green-500 to-teal-600 hover:from-green-600 hover:to-teal-700 text-white font-semibold py-3 px-8 rounded-xl transition-all duration-300 transform hover:scale-105">
                            <i class="fas fa-tachometer-alt mr-2"></i>
                            {{ __('messages.admin_dashboard') }}
                        </a>
                    </div>
                </div>
                @endif
            @endauth

            <!-- Test Transactions -->
            <div class="mt-12">
                <div class="bg-gray-800/50 backdrop-blur-sm rounded-2xl p-6 border border-gray-700/50">
                    <h3 class="text-xl font-semibold text-white mb-4">{{ __('messages.recent_transactions') }}</h3>
                    @if($transactions->count() > 0)
                        <div class="space-y-4">
                            @foreach($transactions->take(5) as $transaction)
                            <div class="flex items-center justify-between p-4 bg-gray-700/30 rounded-lg">
                                <div>
                                    <div class="text-white font-medium">{{ $transaction->paidService->name }}</div>
                                    <div class="text-gray-400 text-sm">{{ $transaction->customer_name }} • {{ $transaction->created_at->format('d/m/Y H:i') }}</div>
                                </div>
                                <div class="text-right">
                                    <div class="text-white font-semibold">{{ $transaction->formatted_amount }}</div>
                                    <div class="text-sm">
                                        @if($transaction->status === 'completed')
                                            <span class="text-green-400">{{ __('messages.completed') }}</span>
                                        @elseif($transaction->status === 'pending')
                                            <span class="text-yellow-400">{{ __('messages.pending') }}</span>
                                        @else
                                            <span class="text-red-400">{{ __('messages.failed') }}</span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    @else
                        <p class="text-gray-400 text-center py-8">{{ __('messages.no_transactions_yet') }}</p>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
