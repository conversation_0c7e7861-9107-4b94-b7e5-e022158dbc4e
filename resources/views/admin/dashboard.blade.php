@extends('layouts.admin')

@section('title', 'لوحة التحكم')
@section('subtitle', 'نظرة عامة على إحصائيات المنصة')

@section('content')
<div class="space-y-6">
    <!-- Welcome Card -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <div class="flex items-center justify-between">
            <div>
                <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-1">مرحباً، {{ auth()->user()->name }}!</h2>
                <p class="text-gray-600 dark:text-gray-400">لوحة التحكم الخاصة بـ MonOri AI</p>
            </div>
            <div class="text-right">
                <p class="text-lg font-medium text-gray-900 dark:text-white">{{ \Carbon\Carbon::now()->format('Y/m/d') }}</p>
                <p class="text-sm text-gray-500 dark:text-gray-400">{{ \Carbon\Carbon::now()->locale('ar')->translatedFormat('l') }}</p>
            </div>
        </div>
    </div>

    <!-- Stats Grid -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <!-- Total Users -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-users text-blue-600 dark:text-blue-400 text-lg"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي المستخدمين</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $totalUsers ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Active Subscriptions -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-credit-card text-green-600 dark:text-green-400 text-lg"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">الاشتراكات النشطة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $activeSubscriptions ?? 0 }}</p>
                </div>
            </div>
        </div>

        <!-- Total Revenue -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-yellow-100 dark:bg-yellow-900 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-dollar-sign text-yellow-600 dark:text-yellow-400 text-lg"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">إجمالي الإيرادات</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ number_format($totalRevenue ?? 0) }} DH</p>
                </div>
            </div>
        </div>

        <!-- Services Used -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-cogs text-purple-600 dark:text-purple-400 text-lg"></i>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-600 dark:text-gray-400">الخدمات المستخدمة</p>
                    <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ $servicesUsed ?? 0 }}</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Recent Users -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">المستخدمون الجدد</h3>
            </div>
            <div class="p-6">
                @if(isset($recentUsers) && $recentUsers->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentUsers as $user)
                        <div class="flex items-center">
                            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-3">
                                <span class="text-white text-sm font-bold">{{ substr($user->name, 0, 1) }}</span>
                            </div>
                            <div class="flex-1">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $user->name }}</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $user->email }}</p>
                            </div>
                            <div class="text-xs text-gray-500 dark:text-gray-400">
                                {{ $user->created_at->diffForHumans() }}
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">لا توجد بيانات متاحة</p>
                @endif
            </div>
        </div>

        <!-- Recent Payments -->
        <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
            <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
                <h3 class="text-lg font-medium text-gray-900 dark:text-white">المدفوعات الأخيرة</h3>
            </div>
            <div class="p-6">
                @if(isset($recentPayments) && $recentPayments->count() > 0)
                    <div class="space-y-4">
                        @foreach($recentPayments as $payment)
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mr-3">
                                    <i class="fas fa-dollar-sign text-green-600 dark:text-green-400 text-xs"></i>
                                </div>
                                <div>
                                    <p class="text-sm font-medium text-gray-900 dark:text-white">{{ $payment->user->name ?? 'مستخدم' }}</p>
                                    <p class="text-xs text-gray-500 dark:text-gray-400">{{ $payment->service_name ?? 'خدمة' }}</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-sm font-medium text-gray-900 dark:text-white">{{ number_format($payment->amount) }} DH</p>
                                <p class="text-xs text-gray-500 dark:text-gray-400">{{ $payment->created_at->diffForHumans() }}</p>
                            </div>
                        </div>
                        @endforeach
                    </div>
                @else
                    <p class="text-gray-500 dark:text-gray-400 text-center py-4">لا توجد بيانات متاحة</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">إجراءات سريعة</h3>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <a href="{{ route('admin.users') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <i class="fas fa-users text-blue-600 dark:text-blue-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-gray-900 dark:text-white">إدارة المستخدمين</span>
            </a>
            <a href="{{ route('admin.subscriptions') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <i class="fas fa-credit-card text-green-600 dark:text-green-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-gray-900 dark:text-white">الاشتراكات</span>
            </a>
            <a href="{{ route('admin.payments') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <i class="fas fa-dollar-sign text-yellow-600 dark:text-yellow-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-gray-900 dark:text-white">المدفوعات</span>
            </a>
            <a href="{{ route('admin.settings') }}" class="flex flex-col items-center p-4 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors">
                <i class="fas fa-cog text-purple-600 dark:text-purple-400 text-xl mb-2"></i>
                <span class="text-sm font-medium text-gray-900 dark:text-white">الإعدادات</span>
            </a>
        </div>
    </div>
</div>
@endsection
