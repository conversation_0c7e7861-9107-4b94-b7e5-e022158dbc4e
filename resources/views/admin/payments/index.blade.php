@extends('admin.layouts.app')

@section('title', __('messages.payment_management'))

@section('content')
<div class="container-fluid">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 text-gray-800">{{ __('messages.payment_management') }}</h1>
            <p class="text-muted">{{ __('messages.manage_all_payment_transactions') }}</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="exportTransactions()">
                <i class="fas fa-download mr-2"></i>{{ __('messages.export_data') }}
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                {{ __('messages.total_revenue') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['total_revenue'], 2) }} MAD
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-dollar-sign fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                {{ __('messages.completed_transactions') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['completed_transactions']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                {{ __('messages.pending_transactions') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['pending_transactions']) }}
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                {{ __('messages.success_rate') }}
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['success_rate'] }}%
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-percentage fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Transactions Table -->
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.recent_transactions') }}</h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink"
                            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                        <div class="dropdown-menu dropdown-menu-right shadow animated--fade-in"
                            aria-labelledby="dropdownMenuLink">
                            <div class="dropdown-header">{{ __('messages.actions') }}:</div>
                            <a class="dropdown-item" href="#" onclick="refreshTransactions()">
                                <i class="fas fa-sync-alt fa-sm fa-fw mr-2 text-gray-400"></i>
                                {{ __('messages.refresh') }}
                            </a>
                            <a class="dropdown-item" href="#" onclick="exportTransactions()">
                                <i class="fas fa-download fa-sm fa-fw mr-2 text-gray-400"></i>
                                {{ __('messages.export') }}
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Filters -->
                <div class="card-body">
                    <form method="GET" class="mb-4">
                        <div class="row">
                            <div class="col-md-3">
                                <select name="status" class="form-control form-control-sm">
                                    <option value="">{{ __('messages.all_statuses') }}</option>
                                    <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>
                                        {{ __('messages.pending') }}
                                    </option>
                                    <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>
                                        {{ __('messages.completed') }}
                                    </option>
                                    <option value="failed" {{ request('status') == 'failed' ? 'selected' : '' }}>
                                        {{ __('messages.failed') }}
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="payment_method" class="form-control form-control-sm">
                                    <option value="">{{ __('messages.all_methods') }}</option>
                                    <option value="stripe" {{ request('payment_method') == 'stripe' ? 'selected' : '' }}>
                                        Stripe
                                    </option>
                                    <option value="paypal" {{ request('payment_method') == 'paypal' ? 'selected' : '' }}>
                                        PayPal
                                    </option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control form-control-sm" 
                                       placeholder="{{ __('messages.search_transactions') }}" 
                                       value="{{ request('search') }}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-primary btn-sm btn-block">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- Transactions Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th>{{ __('messages.transaction_id') }}</th>
                                    <th>{{ __('messages.customer') }}</th>
                                    <th>{{ __('messages.service') }}</th>
                                    <th>{{ __('messages.amount') }}</th>
                                    <th>{{ __('messages.method') }}</th>
                                    <th>{{ __('messages.status') }}</th>
                                    <th>{{ __('messages.date') }}</th>
                                    <th>{{ __('messages.actions') }}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @forelse($transactions as $transaction)
                                <tr>
                                    <td>
                                        <code class="text-primary">{{ $transaction->transaction_id }}</code>
                                    </td>
                                    <td>
                                        <div>
                                            <strong>{{ $transaction->customer_name }}</strong><br>
                                            <small class="text-muted">{{ $transaction->customer_email }}</small>
                                        </div>
                                    </td>
                                    <td>{{ $transaction->paidService->name }}</td>
                                    <td>
                                        <strong>{{ $transaction->formatted_amount }}</strong>
                                    </td>
                                    <td>
                                        @if($transaction->payment_method === 'stripe')
                                            <span class="badge badge-primary">
                                                <i class="fas fa-credit-card mr-1"></i>Stripe
                                            </span>
                                        @else
                                            <span class="badge badge-info">
                                                <i class="fab fa-paypal mr-1"></i>PayPal
                                            </span>
                                        @endif
                                    </td>
                                    <td>
                                        @if($transaction->status === 'completed')
                                            <span class="badge badge-success">{{ __('messages.completed') }}</span>
                                        @elseif($transaction->status === 'pending')
                                            <span class="badge badge-warning">{{ __('messages.pending') }}</span>
                                        @elseif($transaction->status === 'failed')
                                            <span class="badge badge-danger">{{ __('messages.failed') }}</span>
                                        @else
                                            <span class="badge badge-secondary">{{ $transaction->status }}</span>
                                        @endif
                                    </td>
                                    <td>
                                        <small>{{ $transaction->created_at->format('d/m/Y H:i') }}</small>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.payments.show', $transaction) }}" 
                                           class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @empty
                                <tr>
                                    <td colspan="8" class="text-center text-muted py-4">
                                        {{ __('messages.no_transactions_found') }}
                                    </td>
                                </tr>
                                @endforelse
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    <div class="d-flex justify-content-center">
                        {{ $transactions->appends(request()->query())->links() }}
                    </div>
                </div>
            </div>
        </div>

        <!-- Notifications Panel -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.recent_notifications') }}</h6>
                    <button class="btn btn-sm btn-outline-primary" onclick="markAllAsRead()">
                        {{ __('messages.mark_all_read') }}
                    </button>
                </div>
                <div class="card-body" style="max-height: 400px; overflow-y: auto;">
                    @forelse($notifications as $notification)
                    <div class="notification-item {{ $notification->is_read ? '' : 'unread' }} mb-3 p-3 border-left-primary" 
                         data-notification-id="{{ $notification->id }}">
                        <div class="d-flex justify-content-between align-items-start">
                            <div class="flex-grow-1">
                                <h6 class="mb-1">{{ $notification->title }}</h6>
                                <p class="mb-1 text-sm">{{ $notification->message }}</p>
                                <small class="text-muted">{{ $notification->created_at->diffForHumans() }}</small>
                            </div>
                            @if(!$notification->is_read)
                            <span class="badge badge-primary badge-sm">{{ __('messages.new') }}</span>
                            @endif
                        </div>
                    </div>
                    @empty
                    <div class="text-center text-muted py-4">
                        {{ __('messages.no_notifications') }}
                    </div>
                    @endforelse
                </div>
            </div>

            <!-- Quick Stats -->
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">{{ __('messages.quick_stats') }}</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-6 border-right">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['today_revenue'], 2) }}
                            </div>
                            <div class="text-xs text-muted">{{ __('messages.today_revenue') }}</div>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ number_format($stats['month_revenue'], 2) }}
                            </div>
                            <div class="text-xs text-muted">{{ __('messages.month_revenue') }}</div>
                        </div>
                    </div>
                    <hr>
                    <div class="row text-center">
                        <div class="col-6 border-right">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['stripe_transactions'] }}
                            </div>
                            <div class="text-xs text-muted">Stripe</div>
                        </div>
                        <div class="col-6">
                            <div class="h5 mb-0 font-weight-bold text-gray-800">
                                {{ $stats['paypal_transactions'] }}
                            </div>
                            <div class="text-xs text-muted">PayPal</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@endsection

@section('scripts')
<script>
function refreshTransactions() {
    location.reload();
}

function exportTransactions() {
    const params = new URLSearchParams(window.location.search);
    window.location.href = '{{ route("admin.payments.export") }}?' + params.toString();
}

function markAllAsRead() {
    const notificationIds = [];
    document.querySelectorAll('.notification-item.unread').forEach(item => {
        notificationIds.push(item.dataset.notificationId);
    });

    if (notificationIds.length === 0) {
        return;
    }

    fetch('{{ route("admin.payments.notifications.read") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': '{{ csrf_token() }}'
        },
        body: JSON.stringify({
            notification_ids: notificationIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.querySelectorAll('.notification-item.unread').forEach(item => {
                item.classList.remove('unread');
                const badge = item.querySelector('.badge');
                if (badge) badge.remove();
            });
        }
    });
}

// Auto-refresh notifications every 30 seconds
setInterval(() => {
    fetch('{{ route("admin.payments.notifications.count") }}')
        .then(response => response.json())
        .then(data => {
            if (data.count > 0) {
                // Update notification indicator if exists
                const indicator = document.querySelector('.notification-indicator');
                if (indicator) {
                    indicator.textContent = data.count;
                    indicator.style.display = 'inline';
                }
            }
        });
}, 30000);
</script>

<style>
.notification-item.unread {
    background-color: #f8f9fc;
    border-left: 4px solid #4e73df !important;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}
</style>
@endsection
