@import 'tailwindcss';
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Tajawal:wght@300;400;500;700;800&display=swap');

/* INSTANT LIGHTNING FAST TRANSITIONS */
* {
    transition: all 0.02s ease-out;
}

/* INSTANT hover effects */
button, a, .btn, .card {
    transition: all 0.01s ease-out;
}

/* INSTANT transforms */
.hover\:scale-105:hover,
.hover-lift:hover,
.hover-glow:hover {
    transition: transform 0.01s ease-out, box-shadow 0.01s ease-out;
}

/* Compact button styles */
.btn-primary-compact {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.01s ease-out;
    background: linear-gradient(to right, #2563eb, #9333ea);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    cursor: pointer;
}

.btn-primary-compact:hover {
    transform: scale(1.02);
    background: linear-gradient(to right, #1d4ed8, #7c3aed);
    box-shadow: 0 8px 12px -2px rgba(0, 0, 0, 0.15);
}

.btn-primary-compact:active {
    transform: scale(0.99);
    transition: all 0.005s ease-out;
}

.btn-secondary-compact {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.5rem;
    transition: all 0.01s ease-out;
    background: white;
    color: #111827;
    border: 1px solid #d1d5db;
    cursor: pointer;
}

.btn-secondary-compact:hover {
    transform: scale(1.02);
    border-color: #3b82f6;
    box-shadow: 0 4px 8px -2px rgba(0, 0, 0, 0.1);
}

.btn-secondary-compact:active {
    transform: scale(0.99);
    transition: all 0.005s ease-out;
}

/* Dark mode for buttons */
@media (prefers-color-scheme: dark) {
    .btn-secondary-compact {
        background: #1f2937;
        color: white;
        border-color: #4b5563;
    }

    .btn-secondary-compact:hover {
        border-color: #60a5fa;
    }
}

/* Compact text styles */
.text-compact-title {
    font-size: 1.5rem;
    font-weight: 700;
}

@media (min-width: 768px) {
    .text-compact-title {
        font-size: 1.875rem;
    }
}

.text-compact-subtitle {
    font-size: 1.125rem;
}

@media (min-width: 768px) {
    .text-compact-subtitle {
        font-size: 1.25rem;
    }
}

.text-compact-body {
    font-size: 0.875rem;
}

@media (min-width: 768px) {
    .text-compact-body {
        font-size: 1rem;
    }
}

.text-compact-small {
    font-size: 0.75rem;
}

@media (min-width: 768px) {
    .text-compact-small {
        font-size: 0.875rem;
    }
}

/* Fast hover effects */
.hover-lift:hover {
    transform: scale(1.05);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transition: all 0.2s ease-in-out;
}

.hover-glow:hover {
    box-shadow: 0 25px 50px -12px rgba(59, 130, 246, 0.25);
    transition: all 0.2s ease-in-out;
}

/* Compact spacing */
.gap-compact {
    gap: 0.75rem;
}

/* Responsive icons */
.icon-compact {
    width: 1rem;
    height: 1rem;
}

@media (min-width: 768px) {
    .icon-compact {
        width: 1.25rem;
        height: 1.25rem;
    }
}

.icon-small {
    width: 0.75rem;
    height: 0.75rem;
}

@media (min-width: 768px) {
    .icon-small {
        width: 1rem;
        height: 1rem;
    }
}

/* Existing styles */
.gradient-text {
    background: linear-gradient(to right, #2563eb, #9333ea);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.btn-primary {
    background: linear-gradient(to right, #2563eb, #9333ea);
    color: white;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease-in-out;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background: linear-gradient(to right, #1d4ed8, #7c3aed);
    transform: scale(1.05);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.card-hover {
    transition: all 0.3s ease-in-out;
}

.card-hover:hover {
    transform: scale(1.05);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Inter', 'Tajawal', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';

    /* Custom Colors for MonOri AI */
    --color-primary-50: #f0f9ff;
    --color-primary-100: #e0f2fe;
    --color-primary-200: #bae6fd;
    --color-primary-300: #7dd3fc;
    --color-primary-400: #38bdf8;
    --color-primary-500: #0ea5e9;
    --color-primary-600: #0284c7;
    --color-primary-700: #0369a1;
    --color-primary-800: #075985;
    --color-primary-900: #0c4a6e;
    --color-primary-950: #082f49;

    --color-secondary-50: #fdf4ff;
    --color-secondary-100: #fae8ff;
    --color-secondary-200: #f5d0fe;
    --color-secondary-300: #f0abfc;
    --color-secondary-400: #e879f9;
    --color-secondary-500: #d946ef;
    --color-secondary-600: #c026d3;
    --color-secondary-700: #a21caf;
    --color-secondary-800: #86198f;
    --color-secondary-900: #701a75;
    --color-secondary-950: #4a044e;

    --color-accent-50: #fff7ed;
    --color-accent-100: #ffedd5;
    --color-accent-200: #fed7aa;
    --color-accent-300: #fdba74;
    --color-accent-400: #fb923c;
    --color-accent-500: #f97316;
    --color-accent-600: #ea580c;
    --color-accent-700: #c2410c;
    --color-accent-800: #9a3412;
    --color-accent-900: #7c2d12;
    --color-accent-950: #431407;
}

/* Custom Styles */
.gradient-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    transition: all 0.3s ease;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 20px rgba(102, 126, 234, 0.4);
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.animate-pulse-slow {
    animation: pulse 4s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* RTL Support */
[dir="rtl"] {
    font-family: 'Tajawal', 'Inter', ui-sans-serif, system-ui, sans-serif;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Responsive Design Improvements */
@media (max-width: 640px) {
    .hero-title {
        font-size: 2.5rem;
        line-height: 1.2;
    }

    .hero-subtitle {
        font-size: 1.25rem;
    }

    .service-card {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 4rem;
        height: 4rem;
    }
}

@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.25rem;
    }

    .mobile-menu {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out;
    }

    .mobile-menu.active {
        max-height: 500px;
    }

    .hero-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.5rem;
    }
}

@media (max-width: 1024px) {
    .services-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .features-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
    animation: fadeInRight 0.6s ease-out;
}

/* Loading States */
.btn-loading {
    position: relative;
    color: transparent;
}

.btn-loading::after {
    content: '';
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Dark mode improvements */
@media (prefers-color-scheme: dark) {
    .gradient-bg {
        background: linear-gradient(135deg, #1e3a8a 0%, #581c87 100%);
    }
}

/* Print styles */
@media print {
    .navbar, .footer, .cta-section {
        display: none;
    }

    .hero-section {
        page-break-after: always;
    }
}

/* ========== INSTANT LIGHTNING SPEED INTERACTIONS ========== */

/* INSTANT hover effects for all interactive elements */
button, a, .btn, input, select, textarea {
    transition: all 0.005s ease-out !important;
    cursor: pointer;
}

/* INSTANT card animations */
.card, .bg-white, .bg-gray-50 {
    transition: all 0.01s ease-out !important;
}

.card:hover, .bg-white:hover, .bg-gray-50:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 16px -4px rgba(0, 0, 0, 0.1);
}

/* INSTANT scale effects */
.hover\:scale-105:hover {
    transform: scale(1.01) !important;
    transition: transform 0.005s ease-out !important;
}

/* INSTANT button responses */
.btn:active, button:active, a:active {
    transform: scale(0.99) !important;
    transition: all 0.001s ease-out !important;
}

/* INSTANT gradient animations */
.gradient-text, .bg-gradient-to-r, .bg-gradient-to-br {
    transition: all 0.005s ease-out !important;
}

/* INSTANT navigation */
nav a {
    transition: all 0.005s ease-out !important;
}

nav a:hover {
    transform: translateY(-1px);
}

/* INSTANT form interactions */
input:focus, select:focus, textarea:focus {
    transition: all 0.005s ease-out !important;
    transform: scale(1.005);
}

/* INSTANT animations */
.animate-pulse {
    animation-duration: 0.3s !important;
}

.animate-spin {
    animation-duration: 0.2s !important;
}

/* Maximum performance optimizations */
button:hover, a:hover, .btn:hover {
    will-change: transform, box-shadow;
}

/* INSTANT hover states - no delay */
button:hover, a:hover, .btn:hover, .card:hover {
    transition-delay: 0s !important;
}

/* Remove all delays */
* {
    transition-delay: 0s !important;
    animation-delay: 0s !important;
}

/* ========== MAXIMUM SPEED OVERRIDES ========== */

/* Force instant transitions on ALL Tailwind classes */
.transition, .transition-all, .transition-colors, .transition-opacity, .transition-shadow, .transition-transform {
    transition-duration: 0.005s !important;
    transition-timing-function: ease-out !important;
}

.duration-75, .duration-100, .duration-150, .duration-200, .duration-300, .duration-500, .duration-700, .duration-1000 {
    transition-duration: 0.005s !important;
}

/* Instant hover effects for Tailwind utilities */
.hover\:bg-gray-50:hover, .hover\:bg-gray-100:hover, .hover\:bg-blue-50:hover {
    transition: all 0.005s ease-out !important;
}

.hover\:text-blue-600:hover, .hover\:text-gray-900:hover {
    transition: all 0.005s ease-out !important;
}

.hover\:shadow-lg:hover, .hover\:shadow-xl:hover {
    transition: all 0.005s ease-out !important;
}

/* Force instant focus states */
.focus\:ring-2:focus, .focus\:ring-blue-500:focus, .focus\:outline-none:focus {
    transition: all 0.005s ease-out !important;
}

/* Instant group hover effects */
.group:hover .group-hover\:translate-x-1 {
    transition: transform 0.005s ease-out !important;
}

/* Maximum responsiveness */
@media (hover: hover) {
    button:hover, a:hover, .btn:hover {
        transition: all 0.001s ease-out !important;
    }
}

/* ========== LIGHT MODE STYLES (Inverted Colors Theme) ========== */

/* Light mode - Inverted colors design */
.light {
    color-scheme: light;
}

.light body {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 50%, #fdf4ff 100%);
    color: #111827;
}

/* Light mode hero section - Inverted gradients */
.light .hero-section {
    background: linear-gradient(135deg, #f0f9ff 0%, #e0e7ff 30%, #fdf4ff 70%, #f3e8ff 100%);
}

/* Light mode cards - Light backgrounds with inverted shadows */
.light .card, .light .bg-white {
    background: linear-gradient(145deg, #ffffff 0%, #f8fafc 100%);
    border: 1px solid #e2e8f0;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.light .card:hover, .light .bg-white:hover {
    background: linear-gradient(145deg, #f8fafc 0%, #f1f5f9 100%);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
}

/* Light mode gradients - Inverted but keep same style */
.light .gradient-text {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #be185d 100%);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    font-weight: 700;
}

/* Light mode buttons - Enhanced visibility */
.light .btn-primary-compact, .light .btn-primary {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #be185d 100%);
    color: #ffffff !important;
    box-shadow: 0 6px 24px rgba(30, 64, 175, 0.4);
    border: 2px solid transparent;
    font-weight: 600;
}

.light .btn-primary-compact:hover, .light .btn-primary:hover {
    background: linear-gradient(135deg, #1e3a8a 0%, #6d28d9 50%, #a21caf 100%);
    box-shadow: 0 10px 35px rgba(30, 64, 175, 0.6);
    transform: translateY(-2px);
}

.light .btn-secondary-compact, .light .btn-secondary {
    background: #ffffff;
    color: #1e40af !important;
    border: 2px solid #1e40af;
    font-weight: 600;
    box-shadow: 0 4px 16px rgba(30, 64, 175, 0.2);
}

.light .btn-secondary-compact:hover, .light .btn-secondary:hover {
    background: #1e40af;
    color: #ffffff !important;
    border-color: #1e40af;
    box-shadow: 0 6px 20px rgba(30, 64, 175, 0.4);
    transform: translateY(-1px);
}

/* Light mode button variants */
.light .btn {
    font-weight: 600;
    border-radius: 0.75rem;
    transition: all 0.01s ease-out;
}

.light .btn:hover {
    transform: translateY(-1px);
}

/* Light mode navbar - Light with subtle backdrop */
.light nav {
    background: rgba(248, 250, 252, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(226, 232, 240, 0.8);
}

/* Light mode text - Dark text on light background */
.light h1, .light h2, .light h3 {
    color: #0f172a;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.light p {
    color: #475569;
}

/* Light mode specific backgrounds */
.light .bg-gradient-to-r {
    background: linear-gradient(to right, #f0f9ff, #fdf4ff) !important;
}

.light .bg-gradient-to-br {
    background: linear-gradient(to bottom right, #f0f9ff, #e0e7ff, #fdf4ff) !important;
}

/* Light mode links - Keep readable */
.light a {
    color: #1e40af;
}

.light a:hover {
    color: #1e3a8a;
}

/* Light mode borders */
.light .border-gray-200 {
    border-color: #e2e8f0;
}

.light .border-gray-700 {
    border-color: #cbd5e1;
}

/* Override dark mode text colors */
.light .text-white {
    color: #0f172a !important;
}

.light .text-gray-300 {
    color: #475569 !important;
}

.light .text-gray-700 {
    color: #334155 !important;
}

.light .bg-gray-50 {
    background: #f8fafc !important;
}

.light .bg-gray-900 {
    background: #f8fafc !important;
}

.light .bg-gray-800 {
    background: #ffffff !important;
}

/* Keep icons colors unchanged - preserve gradients and special colors */
.light svg {
    color: inherit; /* This preserves the original icon colors */
}

/* Preserve gradient icons and special colored elements */
.light .w-8.h-8.bg-gradient-to-br,
.light .w-7.h-7.bg-gradient-to-r,
.light .w-6.h-6.rounded-full.bg-gradient-to-r,
.light .w-10.h-10.bg-gradient-to-r {
    background: linear-gradient(to bottom right, #3b82f6, #8b5cf6) !important;
}

/* Preserve logo and brand colors */
.light .gradient-text {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #be185d 100%) !important;
    -webkit-background-clip: text !important;
    background-clip: text !important;
    color: transparent !important;
}

/* Enhanced button visibility in light mode */
.light .btn-primary-compact, .light .btn-primary {
    background: linear-gradient(135deg, #1e40af 0%, #7c3aed 50%, #be185d 100%) !important;
    color: #ffffff !important;
    border: 2px solid transparent !important;
    box-shadow: 0 6px 24px rgba(30, 64, 175, 0.4) !important;
}

.light .btn-primary-compact:hover, .light .btn-primary:hover {
    box-shadow: 0 10px 35px rgba(30, 64, 175, 0.6) !important;
    transform: translateY(-2px) !important;
}

/* Enhanced navigation in light mode */
.light nav a {
    color: #1e40af !important;
    font-weight: 500;
}

.light nav a:hover {
    color: #1e3a8a !important;
    background: rgba(30, 64, 175, 0.1);
    border-radius: 0.5rem;
    padding: 0.25rem 0.5rem;
}

.light .hover\:text-blue-600:hover {
    color: #1e3a8a !important;
}

.light .hover\:text-blue-400:hover {
    color: #1e3a8a !important;
}

/* Enhanced cards in light mode */
.light .card:hover {
    box-shadow: 0 16px 48px rgba(30, 64, 175, 0.15) !important;
    border-color: rgba(30, 64, 175, 0.2) !important;
}

/* Enhanced form elements in light mode */
.light input, .light select, .light textarea {
    border: 2px solid #e2e8f0;
    background: #ffffff;
    color: #1e40af;
}

.light input:focus, .light select:focus, .light textarea:focus {
    border-color: #1e40af !important;
    box-shadow: 0 0 0 3px rgba(30, 64, 175, 0.1) !important;
}

/* Enhanced links in light mode */
.light a {
    color: #1e40af !important;
    font-weight: 500;
}

.light a:hover {
    color: #1e3a8a !important;
    text-decoration: underline;
}

/* Enhanced dropdown in light mode */
.light .bg-white {
    background: #ffffff !important;
    border-color: #e2e8f0 !important;
}

.light .hover\:bg-gray-100:hover {
    background: #f1f5f9 !important;
}

.light .text-gray-700 {
    color: #374151 !important;
}

.light .text-gray-500 {
    color: #6b7280 !important;
}

/* Light mode animations */
.light .animate-pulse {
    animation: light-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes light-pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.8;
    }
}
