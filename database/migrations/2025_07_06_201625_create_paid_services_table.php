<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('paid_services', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('name_en');
            $table->string('name_ar');
            $table->string('name_fr');
            $table->text('description');
            $table->text('description_en');
            $table->text('description_ar');
            $table->text('description_fr');
            $table->enum('type', ['cv_creation', 'cv_improvement', 'personal_coaching', 'interview_training', 'custom']);
            $table->decimal('price', 8, 2);
            $table->string('currency', 3)->default('MAD');
            $table->integer('delivery_time_hours')->default(24); // وقت التسليم بالساعات
            $table->json('features'); // المميزات المتضمنة
            $table->boolean('requires_ai')->default(true);
            $table->boolean('requires_human_review')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('paid_services');
    }
};
