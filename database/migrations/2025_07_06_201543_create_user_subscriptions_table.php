<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_subscriptions', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->foreignId('subscription_plan_id')->constrained()->onDelete('cascade');
            $table->enum('billing_cycle', ['monthly', 'yearly']);
            $table->decimal('amount_paid', 8, 2);
            $table->string('currency', 3)->default('MAD');
            $table->enum('status', ['active', 'cancelled', 'expired', 'pending'])->default('pending');
            $table->datetime('starts_at');
            $table->datetime('ends_at');
            $table->datetime('cancelled_at')->nullable();
            $table->string('payment_method')->nullable(); // card, bank_transfer, etc.
            $table->string('transaction_id')->nullable();
            $table->json('usage_limits'); // حدود الاستخدام الحالية
            $table->json('usage_current')->nullable(); // الاستخدام الحالي
            $table->timestamps();

            $table->index(['user_id', 'status']);
            $table->index('ends_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_subscriptions');
    }
};
