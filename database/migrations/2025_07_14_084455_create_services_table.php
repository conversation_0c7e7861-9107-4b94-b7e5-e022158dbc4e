<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Insert professional services data
        DB::table('paid_services')->insert([
            [
                'name' => 'Professional Personality Analysis',
                'name_en' => 'Professional Personality Analysis',
                'name_ar' => 'تحليل الشخصية المحترف',
                'name_fr' => 'Analyse de Personnalité Professionnelle',
                'description' => 'Advanced AI-powered personality analysis with detailed report and personal consultation',
                'description_en' => 'Advanced AI-powered personality analysis with detailed report and personal consultation',
                'description_ar' => 'تحليل شخصية متقدم بالذكاء الاصطناعي مع تقرير مفصل واستشارة شخصية',
                'description_fr' => 'Analyse de personnalité avancée alimentée par l\'IA avec rapport détaillé et consultation personnelle',
                'type' => 'custom',
                'price' => 79.00,
                'currency' => 'MAD',
                'delivery_time_hours' => 2,
                'features' => json_encode([
                    'ai_analysis' => true,
                    'detailed_report' => true,
                    'personal_consultation' => true,
                    'career_recommendations' => true,
                    'strengths_weaknesses' => true
                ]),
                'requires_ai' => true,
                'requires_human_review' => true,
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Professional CV Creation',
                'name_en' => 'Professional CV Creation',
                'name_ar' => 'إنشاء السيرة الذاتية المحترف',
                'name_fr' => 'Création de CV Professionnelle',
                'description' => 'Professional AI-powered CV creation with advanced analysis and personalized recommendations',
                'description_en' => 'Professional AI-powered CV creation with advanced analysis and personalized recommendations',
                'description_ar' => 'إنشاء سيرة ذاتية احترافية بالذكاء الاصطناعي مع تحليل متقدم وتوصيات مخصصة',
                'description_fr' => 'Création de CV professionnelle alimentée par l\'IA avec analyse avancée et recommandations personnalisées',
                'type' => 'cv_creation',
                'price' => 89.00,
                'currency' => 'MAD',
                'delivery_time_hours' => 1,
                'features' => json_encode([
                    'ai_optimization' => true,
                    'multiple_templates' => true,
                    'ats_optimization' => true,
                    'keyword_optimization' => true,
                    'industry_specific' => true
                ]),
                'requires_ai' => true,
                'requires_human_review' => true,
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now()
            ],
            [
                'name' => 'Professional Interview Simulation',
                'name_en' => 'Professional Interview Simulation',
                'name_ar' => 'محاكاة المقابلات المحترفة',
                'name_fr' => 'Simulation d\'Entretien Professionnelle',
                'description' => 'Advanced AI-powered interview simulation with comprehensive evaluation and personal coaching',
                'description_en' => 'Advanced AI-powered interview simulation with comprehensive evaluation and personal coaching',
                'description_ar' => 'محاكاة مقابلة متقدمة بالذكاء الاصطناعي مع تقييم شامل وتدريب شخصي',
                'description_fr' => 'Simulation d\'entretien avancée alimentée par l\'IA avec évaluation complète et coaching personnel',
                'type' => 'interview_training',
                'price' => 99.00,
                'currency' => 'MAD',
                'delivery_time_hours' => 2,
                'features' => json_encode([
                    'ai_interviewer' => true,
                    'voice_analysis' => true,
                    'detailed_feedback' => true,
                    'performance_metrics' => true,
                    'improvement_plan' => true
                ]),
                'requires_ai' => true,
                'requires_human_review' => true,
                'is_active' => true,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now()
            ]
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove the inserted services
        DB::table('paid_services')->whereIn('type', ['custom'])->where('name', 'Professional Personality Analysis')->delete();
        DB::table('paid_services')->where('type', 'cv_creation')->where('name', 'Professional CV Creation')->delete();
        DB::table('paid_services')->where('type', 'interview_training')->where('name', 'Professional Interview Simulation')->delete();
    }
};
