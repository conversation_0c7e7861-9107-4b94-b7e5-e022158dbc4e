<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('service_results', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('service_type'); // personality_analysis, cv_improvement, interview_simulation, etc.
            $table->string('title');
            $table->text('description')->nullable();
            $table->json('results'); // Store AI results as JSON
            $table->json('metadata')->nullable(); // Additional data like scores, recommendations
            $table->string('status')->default('completed'); // completed, in_progress, failed
            $table->integer('score')->nullable(); // Overall score if applicable
            $table->timestamps();

            $table->index(['user_id', 'service_type']);
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_results');
    }
};
