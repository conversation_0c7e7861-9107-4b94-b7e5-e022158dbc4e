<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('subscription_plans', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // أساسي، ذهبي، Premium
            $table->string('name_en');
            $table->string('name_ar');
            $table->string('name_fr');
            $table->text('description');
            $table->text('description_en');
            $table->text('description_ar');
            $table->text('description_fr');
            $table->decimal('price_monthly', 8, 2);
            $table->decimal('price_yearly', 8, 2);
            $table->string('currency', 3)->default('MAD');
            $table->json('features'); // المميزات
            $table->integer('ai_requests_limit')->default(10); // حد طلبات الذكاء الاصطناعي
            $table->integer('cv_generations_limit')->default(3); // حد إنشاء CV
            $table->boolean('pdf_export')->default(false);
            $table->boolean('voice_interviews')->default(false);
            $table->boolean('personal_coaching')->default(false);
            $table->boolean('priority_support')->default(false);
            $table->boolean('is_active')->default(true);
            $table->integer('sort_order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('subscription_plans');
    }
};
