<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cv_generations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->longText('original_data'); // JSON data from form
            $table->longText('enhanced_data'); // AI-enhanced JSON data
            $table->string('cv_language', 5)->default('en'); // en, ar, fr
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending');
            $table->string('pdf_path')->nullable(); // Path to generated PDF
            $table->timestamps();

            $table->index(['user_id', 'created_at']);
            $table->index('status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cv_generations');
    }
};
