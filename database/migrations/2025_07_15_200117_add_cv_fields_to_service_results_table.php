<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('service_results', function (Blueprint $table) {
            $table->string('service_level')->nullable()->after('service_type');
            $table->json('input_data')->nullable()->after('results');
            $table->json('result_data')->nullable()->after('input_data');
            $table->string('file_path')->nullable()->after('result_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('service_results', function (Blueprint $table) {
            $table->dropColumn(['service_level', 'input_data', 'result_data', 'file_path']);
        });
    }
};
