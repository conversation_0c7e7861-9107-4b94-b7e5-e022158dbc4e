<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('blog_posts', function (Blueprint $table) {
            $table->id();
            $table->string('title');
            $table->string('title_ar')->nullable();
            $table->string('title_en')->nullable();
            $table->string('title_fr')->nullable();
            $table->string('slug')->unique();
            $table->text('excerpt');
            $table->text('excerpt_ar')->nullable();
            $table->text('excerpt_en')->nullable();
            $table->text('excerpt_fr')->nullable();
            $table->longText('content');
            $table->longText('content_ar')->nullable();
            $table->longText('content_en')->nullable();
            $table->longText('content_fr')->nullable();
            $table->string('featured_image')->nullable();
            $table->enum('status', ['draft', 'published', 'archived'])->default('draft');
            $table->enum('category', ['career_guidance', 'interview_tips', 'job_market', 'success_stories', 'ai_insights'])->default('career_guidance');
            $table->json('tags')->nullable();
            $table->json('seo_meta')->nullable(); // For SEO meta tags
            $table->integer('views_count')->default(0);
            $table->integer('reading_time')->nullable(); // In minutes
            $table->foreignId('author_id')->constrained('users')->onDelete('cascade');
            $table->timestamp('published_at')->nullable();
            $table->timestamps();

            $table->index(['status', 'published_at']);
            $table->index(['category', 'status']);
            $table->index(['author_id', 'status']);
            $table->fullText(['title', 'excerpt', 'content']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('blog_posts');
    }
};
