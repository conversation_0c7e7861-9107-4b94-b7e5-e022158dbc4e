<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\UserProfile;
use App\Models\UserActivity;
use App\Models\UserAssessment;
use Illuminate\Support\Facades\Hash;

class UserDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test user if it doesn't exist
        $user = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'محمد حمود',
                'password' => Hash::make('password123'),
            ]
        );

        // Create user profile
        UserProfile::updateOrCreate(
            ['user_id' => $user->id],
            [
                'phone' => '+966501234567',
                'birth_date' => '1995-05-15',
                'gender' => 'male',
                'city' => 'الرياض',
                'country' => 'السعودية',
                'bio' => 'مطور برمجيات شغوف بالتكنولوجيا والذكاء الاصطناعي، أسعى لتطوير حلول مبتكرة تساهم في تحسين حياة الناس.',
                'education_level' => 'بكالوريوس',
                'field_of_study' => 'علوم الحاسب',
                'current_job' => 'مطور برمجيات',
                'experience_years' => 3,
                'skills' => ['PHP', 'Laravel', 'JavaScript', 'Vue.js', 'MySQL', 'Git'],
                'interests' => ['الذكاء الاصطناعي', 'تطوير الويب', 'ريادة الأعمال', 'التصميم'],
                'linkedin_url' => 'https://linkedin.com/in/mohammed-hammoud',
                'github_url' => 'https://github.com/mohammed-hammoud',
                'portfolio_url' => 'https://mohammed-hammoud.dev',
            ]
        );

        // Create activities
        $activities = [
            [
                'type' => 'personality_test',
                'title' => 'اختبار تحليل الشخصية MBTI',
                'description' => 'تحليل شامل لنوع شخصيتك وفقاً لمؤشر مايرز-بريجز',
                'status' => 'completed',
                'progress_percentage' => 100,
                'completed_at' => now()->subDays(5),
                'data' => ['result' => 'ENFP', 'score' => 85]
            ],
            [
                'type' => 'cv_generation',
                'title' => 'تحسين السيرة الذاتية',
                'description' => 'تحسين وتطوير السيرة الذاتية باستخدام الذكاء الاصطناعي',
                'status' => 'completed',
                'progress_percentage' => 100,
                'completed_at' => now()->subDays(3),
                'data' => ['improvements' => 5, 'score' => 92]
            ],
            [
                'type' => 'interview_training',
                'title' => 'مقابلة تدريبية',
                'description' => 'تدريب على مقابلات العمل في مجال التكنولوجيا',
                'status' => 'in_progress',
                'progress_percentage' => 65,
                'data' => ['sessions_completed' => 3, 'total_sessions' => 5]
            ],
            [
                'type' => 'job_recommendation',
                'title' => 'توصيات وظيفية',
                'description' => 'الحصول على توصيات وظيفية مخصصة',
                'status' => 'pending',
                'progress_percentage' => 0,
                'data' => []
            ],
        ];

        foreach ($activities as $activity) {
            UserActivity::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'type' => $activity['type'],
                    'title' => $activity['title']
                ],
                $activity
            );
        }

        // Create assessments
        $assessments = [
            [
                'type' => 'personality',
                'title' => 'تقييم الشخصية المهنية',
                'score' => 85,
                'results' => [
                    'type' => 'ENFP',
                    'strengths' => ['إبداعي', 'متحمس', 'مرن', 'جيد في التواصل'],
                    'areas_for_improvement' => ['التركيز على التفاصيل', 'إدارة الوقت']
                ],
                'recommendations' => 'ينصح بالعمل في بيئات إبداعية ومتنوعة مع فرص للتفاعل مع الآخرين.',
                'completed_at' => now()->subDays(5)
            ],
            [
                'type' => 'skills',
                'title' => 'تقييم المهارات التقنية',
                'score' => 78,
                'results' => [
                    'technical_skills' => 80,
                    'soft_skills' => 75,
                    'leadership' => 70
                ],
                'recommendations' => 'تطوير مهارات القيادة والإدارة لتعزيز الفرص المهنية.',
                'completed_at' => now()->subDays(2)
            ]
        ];

        foreach ($assessments as $assessment) {
            UserAssessment::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'type' => $assessment['type'],
                    'title' => $assessment['title']
                ],
                $assessment
            );
        }
    }
}
