<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\CVTemplate;

class CVTemplateSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $templates = [
            [
                'name' => 'Modern Professional',
                'slug' => 'modern-professional',
                'description' => 'Clean and modern design perfect for tech professionals',
                'style' => 'modern',
                'layout' => 'two_column',
                'color_scheme' => [
                    'primary' => '#2563eb',
                    'secondary' => '#64748b',
                    'accent' => '#f8fafc',
                    'text' => '#1e293b'
                ],
                'is_premium' => false,
                'sort_order' => 1
            ],
            [
                'name' => 'Classic Elegant',
                'slug' => 'classic-elegant',
                'description' => 'Traditional and elegant design for formal industries',
                'style' => 'classic',
                'layout' => 'single_column',
                'color_scheme' => [
                    'primary' => '#1f2937',
                    'secondary' => '#6b7280',
                    'accent' => '#f9fafb',
                    'text' => '#111827'
                ],
                'is_premium' => false,
                'sort_order' => 2
            ],
            [
                'name' => 'Creative Designer',
                'slug' => 'creative-designer',
                'description' => 'Bold and creative design for designers and artists',
                'style' => 'creative',
                'layout' => 'sidebar',
                'color_scheme' => [
                    'primary' => '#7c3aed',
                    'secondary' => '#a78bfa',
                    'accent' => '#faf5ff',
                    'text' => '#581c87'
                ],
                'is_premium' => true,
                'sort_order' => 3
            ],
            [
                'name' => 'Minimal Clean',
                'slug' => 'minimal-clean',
                'description' => 'Ultra-clean minimal design for maximum impact',
                'style' => 'minimal',
                'layout' => 'single_column',
                'color_scheme' => [
                    'primary' => '#000000',
                    'secondary' => '#6b7280',
                    'accent' => '#ffffff',
                    'text' => '#374151'
                ],
                'is_premium' => false,
                'sort_order' => 4
            ],
            [
                'name' => 'Executive Premium',
                'slug' => 'executive-premium',
                'description' => 'Premium design for executives and senior professionals',
                'style' => 'professional',
                'layout' => 'two_column',
                'color_scheme' => [
                    'primary' => '#059669',
                    'secondary' => '#10b981',
                    'accent' => '#ecfdf5',
                    'text' => '#064e3b'
                ],
                'is_premium' => true,
                'sort_order' => 5
            ],
            [
                'name' => 'Tech Specialist',
                'slug' => 'tech-specialist',
                'description' => 'Modern tech-focused design with clean lines',
                'style' => 'modern',
                'layout' => 'sidebar',
                'color_scheme' => [
                    'primary' => '#0ea5e9',
                    'secondary' => '#38bdf8',
                    'accent' => '#f0f9ff',
                    'text' => '#0c4a6e'
                ],
                'is_premium' => true,
                'sort_order' => 6
            ]
        ];

        foreach ($templates as $template) {
            CVTemplate::create($template);
        }
    }
}
