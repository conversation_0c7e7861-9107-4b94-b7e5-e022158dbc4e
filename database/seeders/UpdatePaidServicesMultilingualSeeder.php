<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UpdatePaidServicesMultilingualSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // تحديث الخدمات المدفوعة بالميزات متعددة اللغات
        $services = [
            [
                'type' => 'cv_creation',
                'features_ar' => [
                    'إنشاء CV احترافي مخصص',
                    'تحليل شامل للمهارات',
                    'تحسين الكلمات المفتاحية',
                    'تصميم جذاب ومهني',
                    'تصدير PDF عالي الجودة'
                ],
                'features_en' => [
                    'Custom professional CV creation',
                    'Comprehensive skills analysis',
                    'Keyword optimization',
                    'Attractive and professional design',
                    'High-quality PDF export'
                ],
                'features_fr' => [
                    'Création de CV professionnel personnalisé',
                    'Analyse complète des compétences',
                    'Optimisation des mots-clés',
                    'Design attrayant et professionnel',
                    'Export PDF haute qualité'
                ]
            ],
            [
                'type' => 'cv_improvement',
                'features_ar' => [
                    'تحليل شامل للسيرة الذاتية',
                    'اقتراحات تحسين مخصصة',
                    'تحسين الكلمات المفتاحية',
                    'تقرير مفصل'
                ],
                'features_en' => [
                    'Comprehensive CV analysis',
                    'Customized improvement suggestions',
                    'Keyword optimization',
                    'Detailed report'
                ],
                'features_fr' => [
                    'Analyse complète du CV',
                    'Suggestions d\'amélioration personnalisées',
                    'Optimisation des mots-clés',
                    'Rapport détaillé'
                ]
            ],
            [
                'type' => 'personal_coaching',
                'features_ar' => [
                    'جلسة توجيه شخصية مدتها ساعة',
                    'تحليل مهني شامل',
                    'خطة تطوير مهني مخصصة',
                    'متابعة لمدة شهر'
                ],
                'features_en' => [
                    'One-hour personal coaching session',
                    'Comprehensive career analysis',
                    'Customized career development plan',
                    'One-month follow-up'
                ],
                'features_fr' => [
                    'Session de coaching personnel d\'une heure',
                    'Analyse de carrière complète',
                    'Plan de développement de carrière personnalisé',
                    'Suivi d\'un mois'
                ]
            ],
            [
                'type' => 'interview_training',
                'features_ar' => [
                    'محاكاة مقابلة عمل حقيقية',
                    'تقييم الأداء والتغذية الراجعة',
                    'نصائح وتوجيهات مخصصة',
                    'تسجيل الجلسة للمراجعة'
                ],
                'features_en' => [
                    'Real job interview simulation',
                    'Performance evaluation and feedback',
                    'Customized tips and guidance',
                    'Session recording for review'
                ],
                'features_fr' => [
                    'Simulation d\'entretien d\'embauche réel',
                    'Évaluation de performance et feedback',
                    'Conseils et orientations personnalisés',
                    'Enregistrement de session pour révision'
                ]
            ]
        ];

        foreach ($services as $serviceData) {
            $service = \App\Models\PaidService::where('type', $serviceData['type'])->first();
            if ($service) {
                $service->update([
                    'features_ar' => $serviceData['features_ar'],
                    'features_en' => $serviceData['features_en'],
                    'features_fr' => $serviceData['features_fr']
                ]);
            }
        }
    }
}
