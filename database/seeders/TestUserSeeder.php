<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create test user if doesn't exist
        $user = User::where('email', '<EMAIL>')->first();
        
        if (!$user) {
            User::create([
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'email_verified_at' => now(),
            ]);
            
            echo "Test user created: <EMAIL> / password\n";
        } else {
            echo "Test user already exists: <EMAIL>\n";
        }
    }
}
