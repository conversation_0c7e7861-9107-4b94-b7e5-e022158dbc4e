<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use App\Models\SiteAnalytics;
use App\Models\UserAction;
use App\Models\Subscription;
use App\Models\Payment;
use App\Models\BlogPost;
use App\Models\SubscriptionPlan;
use App\Models\PaidService;
use Carbon\Carbon;

class DashboardDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating sample users...');

        // Create sample users
        for ($i = 0; $i < 50; $i++) {
            $user = User::create([
                'name' => fake()->name(),
                'email' => fake()->unique()->safeEmail(),
                'password' => bcrypt('password'),
                'is_admin' => fake()->boolean(5), // 5% chance of being admin
                'email_verified_at' => fake()->boolean(80) ? now() : null,
                'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
            ]);

            // Create user actions for each user
            $actionTypes = ['login', 'logout', 'profile_update', 'test_completed', 'cv_generated', 'page_view'];

            for ($j = 0; $j < rand(1, 10); $j++) {
                UserAction::create([
                    'user_id' => $user->id,
                    'action_type' => fake()->randomElement($actionTypes),
                    'action_description' => fake()->sentence(),
                    'action_data' => [
                        'page' => fake()->randomElement(['/', '/services', '/about', '/contact']),
                        'duration' => rand(10, 300)
                    ],
                    'ip_address' => fake()->ipv4(),
                    'user_agent' => fake()->userAgent(),
                    'created_at' => fake()->dateTimeBetween('-7 days', 'now'),
                ]);
            }
        }

        $this->command->info('Creating site analytics data...');

        // Create site analytics data
        $pages = ['/', '/services', '/about', '/contact', '/login', '/register'];
        $devices = ['desktop', 'mobile', 'tablet'];
        $browsers = ['Chrome', 'Firefox', 'Safari', 'Edge'];

        for ($i = 0; $i < 1000; $i++) {
            SiteAnalytics::create([
                'page_url' => fake()->randomElement($pages),
                'user_ip' => fake()->ipv4(),
                'user_agent' => fake()->userAgent(),
                'referrer' => fake()->boolean(30) ? fake()->url() : null,
                'device_type' => fake()->randomElement($devices),
                'browser' => fake()->randomElement($browsers),
                'country' => fake()->country(),
                'session_duration' => rand(10, 600),
                'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
            ]);
        }

        $this->command->info('Creating subscription and payment data...');

        // Create some subscriptions and payments
        $users = User::take(20)->get();
        $plans = SubscriptionPlan::all();
        $services = PaidService::all();

        foreach ($users as $user) {
            // 60% chance of having a subscription
            if (fake()->boolean(60)) {
                $plan = $plans->random();
                $subscription = Subscription::create([
                    'user_id' => $user->id,
                    'subscription_plan_id' => $plan->id,
                    'status' => fake()->randomElement(['active', 'inactive', 'cancelled']),
                    'payment_method' => fake()->randomElement(['stripe', 'paypal']),
                    'amount' => $plan->price_monthly,
                    'currency' => 'MAD',
                    'billing_cycle' => fake()->randomElement(['monthly', 'yearly']),
                    'starts_at' => fake()->dateTimeBetween('-30 days', 'now'),
                    'ends_at' => fake()->dateTimeBetween('now', '+30 days'),
                    'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
                ]);

                // Create payment for subscription
                Payment::create([
                    'user_id' => $user->id,
                    'payable_type' => Subscription::class,
                    'payable_id' => $subscription->id,
                    'payment_id' => 'pay_' . fake()->uuid(),
                    'payment_method' => $subscription->payment_method,
                    'status' => fake()->randomElement(['completed', 'pending', 'failed']),
                    'amount' => $subscription->amount,
                    'currency' => 'MAD',
                    'description' => "اشتراك {$plan->name}",
                    'paid_at' => fake()->boolean(80) ? fake()->dateTimeBetween('-30 days', 'now') : null,
                    'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
                ]);
            }

            // 30% chance of ordering a service
            if (fake()->boolean(30) && $services->count() > 0) {
                $service = $services->random();
                Payment::create([
                    'user_id' => $user->id,
                    'payable_type' => PaidService::class,
                    'payable_id' => $service->id,
                    'payment_id' => 'pay_' . fake()->uuid(),
                    'payment_method' => fake()->randomElement(['stripe', 'paypal']),
                    'status' => fake()->randomElement(['completed', 'pending', 'failed']),
                    'amount' => $service->price,
                    'currency' => 'MAD',
                    'description' => "خدمة {$service->name}",
                    'paid_at' => fake()->boolean(80) ? fake()->dateTimeBetween('-30 days', 'now') : null,
                    'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
                ]);
            }
        }

        $this->command->info('Creating blog posts...');

        // Create blog posts
        $categories = ['career_guidance', 'interview_tips', 'job_market', 'success_stories', 'ai_insights'];
        $adminUsers = User::where('is_admin', true)->get();

        if ($adminUsers->count() == 0) {
            // Create at least one admin user
            $adminUser = User::create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'password' => bcrypt('password'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]);
            $adminUsers = collect([$adminUser]);
        }

        for ($i = 0; $i < 20; $i++) {
            $title = fake()->sentence(6);
            BlogPost::create([
                'title' => $title,
                'title_ar' => $title,
                'title_en' => fake()->sentence(6),
                'title_fr' => fake()->sentence(6),
                'slug' => \Illuminate\Support\Str::slug($title),
                'excerpt' => fake()->paragraph(2),
                'excerpt_ar' => fake()->paragraph(2),
                'excerpt_en' => fake()->paragraph(2),
                'excerpt_fr' => fake()->paragraph(2),
                'content' => fake()->paragraphs(8, true),
                'content_ar' => fake()->paragraphs(8, true),
                'content_en' => fake()->paragraphs(8, true),
                'content_fr' => fake()->paragraphs(8, true),
                'status' => fake()->randomElement(['published', 'draft']),
                'category' => fake()->randomElement($categories),
                'tags' => fake()->words(3),
                'views_count' => fake()->numberBetween(0, 1000),
                'reading_time' => fake()->numberBetween(2, 15),
                'author_id' => $adminUsers->random()->id,
                'published_at' => fake()->boolean(80) ? fake()->dateTimeBetween('-30 days', 'now') : null,
                'created_at' => fake()->dateTimeBetween('-30 days', 'now'),
            ]);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Users: 50');
        $this->command->info('User Actions: ~250-500');
        $this->command->info('Site Analytics: 1000');
        $this->command->info('Subscriptions: ~12');
        $this->command->info('Payments: ~18');
        $this->command->info('Blog Posts: 20');
    }
}
