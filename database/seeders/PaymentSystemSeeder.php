<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\PaidService;
use App\Models\PaymentTransaction;
use App\Models\AdminNotification;
use App\Models\User;
use Illuminate\Support\Str;

class PaymentSystemSeeder extends Seeder
{
    public function run()
    {
        // Create sample paid services if they don't exist
        $services = [
            [
                'name' => 'Professional CV Creation',
                'name_en' => 'Professional CV Creation',
                'name_ar' => 'إنشاء السيرة الذاتية المحترفة',
                'name_fr' => 'Création de CV Professionnel',
                'description' => 'Get a professionally designed CV with AI optimization',
                'description_en' => 'Get a professionally designed CV with AI optimization',
                'description_ar' => 'احصل على سيرة ذاتية مصممة بشكل احترافي مع تحسين الذكاء الاصطناعي',
                'description_fr' => 'Obtenez un CV conçu professionnellement avec optimisation IA',
                'type' => 'cv_creation',
                'price' => 55.00,
                'currency' => 'MAD',
                'delivery_time_hours' => 24,
                'features' => [
                    'AI-powered content optimization',
                    'Professional templates',
                    'ATS-friendly formatting',
                    'Multiple revisions included'
                ],
                'requires_ai' => true,
                'requires_human_review' => false,
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'AI CV Improvement',
                'name_en' => 'AI CV Improvement',
                'name_ar' => 'تحسين السيرة الذاتية بالذكاء الاصطناعي',
                'name_fr' => 'Amélioration CV par IA',
                'description' => 'Improve your existing CV with AI analysis and recommendations',
                'description_en' => 'Improve your existing CV with AI analysis and recommendations',
                'description_ar' => 'حسن سيرتك الذاتية الحالية بتحليل وتوصيات الذكاء الاصطناعي',
                'description_fr' => 'Améliorez votre CV existant avec analyse et recommandations IA',
                'type' => 'cv_improvement',
                'price' => 50.00,
                'currency' => 'MAD',
                'delivery_time_hours' => 12,
                'features' => [
                    'Comprehensive CV analysis',
                    'AI-powered suggestions',
                    'Industry-specific optimization',
                    'Detailed improvement report'
                ],
                'requires_ai' => true,
                'requires_human_review' => false,
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Personal AI Coaching',
                'name_en' => 'Personal AI Coaching',
                'name_ar' => 'التدريب الشخصي بالذكاء الاصطناعي',
                'name_fr' => 'Coaching Personnel IA',
                'description' => 'One-on-one AI coaching session for career development',
                'description_en' => 'One-on-one AI coaching session for career development',
                'description_ar' => 'جلسة تدريب فردية بالذكاء الاصطناعي لتطوير المسيرة المهنية',
                'description_fr' => 'Session de coaching individuel IA pour développement de carrière',
                'type' => 'personal_coaching',
                'price' => 100.00,
                'currency' => 'MAD',
                'delivery_time_hours' => 2,
                'features' => [
                    '60-minute AI coaching session',
                    'Personalized career roadmap',
                    'Goal setting and tracking',
                    'Follow-up recommendations'
                ],
                'requires_ai' => true,
                'requires_human_review' => true,
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Interview Training',
                'name_en' => 'Interview Training',
                'name_ar' => 'تدريب المقابلات',
                'name_fr' => 'Formation aux Entretiens',
                'description' => 'Advanced interview training with AI feedback',
                'description_en' => 'Advanced interview training with AI feedback',
                'description_ar' => 'تدريب متقدم على المقابلات مع تقييم الذكاء الاصطناعي',
                'description_fr' => 'Formation avancée aux entretiens avec feedback IA',
                'type' => 'interview_training',
                'price' => 70.00,
                'currency' => 'MAD',
                'delivery_time_hours' => 3,
                'features' => [
                    'Mock interview sessions',
                    'AI-powered feedback',
                    'Industry-specific questions',
                    'Performance analytics'
                ],
                'requires_ai' => true,
                'requires_human_review' => true,
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($services as $serviceData) {
            PaidService::firstOrCreate(
                ['name' => $serviceData['name']],
                $serviceData
            );
        }

        // Create sample users if they don't exist
        $users = User::all();
        if ($users->count() < 3) {
            User::factory()->create([
                'name' => 'Ahmed Hassan',
                'email' => '<EMAIL>',
                'is_admin' => false,
            ]);

            User::factory()->create([
                'name' => 'Fatima Al-Zahra',
                'email' => '<EMAIL>',
                'is_admin' => false,
            ]);

            User::factory()->create([
                'name' => 'Admin User',
                'email' => '<EMAIL>',
                'is_admin' => true,
            ]);
        }

        // Create sample payment transactions
        $services = PaidService::all();
        $users = User::where('is_admin', false)->get();

        foreach ($users as $user) {
            foreach ($services->take(2) as $service) {
                $transaction = PaymentTransaction::create([
                    'transaction_id' => 'TXN_' . strtoupper(Str::random(12)) . '_' . time(),
                    'user_id' => $user->id,
                    'paid_service_id' => $service->id,
                    'payment_method' => collect(['stripe', 'paypal'])->random(),
                    'amount' => $service->price,
                    'currency' => 'MAD',
                    'status' => collect(['completed', 'pending', 'failed'])->random(),
                    'customer_name' => $user->name,
                    'customer_email' => $user->email,
                    'customer_phone' => '+212' . rand(600000000, 799999999),
                    'gateway_transaction_id' => 'gw_' . Str::random(20),
                    'paid_at' => rand(0, 1) ? now()->subDays(rand(1, 30)) : null,
                    'gateway_response' => [
                        'status' => 'success',
                        'payment_id' => 'pay_' . Str::random(24),
                    ],
                    'payment_details' => [
                        'card_last_four' => '****' . rand(1000, 9999),
                        'card_brand' => collect(['visa', 'mastercard', 'amex'])->random(),
                    ],
                ]);

                // Create admin notification for completed payments
                if ($transaction->status === 'completed') {
                    AdminNotification::create([
                        'type' => 'payment_received',
                        'title' => 'New Payment Received',
                        'message' => "Payment of {$transaction->amount} MAD received from {$transaction->customer_name} for {$service->name}",
                        'data' => [
                            'transaction_id' => $transaction->id,
                            'user_id' => $transaction->user_id,
                            'service_id' => $transaction->paid_service_id,
                            'amount' => $transaction->amount,
                            'payment_method' => $transaction->payment_method,
                        ],
                        'is_read' => rand(0, 1),
                        'read_at' => rand(0, 1) ? now()->subHours(rand(1, 24)) : null,
                    ]);
                }
            }
        }

        $this->command->info('Payment system seeded successfully!');
    }
}
