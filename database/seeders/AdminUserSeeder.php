<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user if not exists
        $adminEmail = '<EMAIL>';

        if (!User::where('email', $adminEmail)->exists()) {
            User::create([
                'name' => 'MonOri AI Admin',
                'email' => $adminEmail,
                'password' => Hash::make('admin123456'),
                'is_admin' => true,
                'email_verified_at' => now(),
            ]);

            $this->command->info('Admin user created successfully!');
            $this->command->info('Email: ' . $adminEmail);
            $this->command->info('Password: admin123456');
        } else {
            // Update existing user to be admin
            User::where('email', $adminEmail)->update(['is_admin' => true]);
            $this->command->info('Existing user updated to admin!');
        }
    }
}
