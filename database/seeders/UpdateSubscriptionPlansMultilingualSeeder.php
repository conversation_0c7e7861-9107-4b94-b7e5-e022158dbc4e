<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class UpdateSubscriptionPlansMultilingualSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // تحديث الخطط الموجودة بالميزات متعددة اللغات
        $plans = [
            [
                'name' => 'Basic',
                'features_ar' => [
                    'تحليل الشخصية الأساسي',
                    'إنشاء CV بسيط',
                    'توصيات وظائف محدودة',
                    'دعم عبر البريد الإلكتروني',
                    '10 طلبات ذكاء اصطناعي شهرياً',
                    '3 سير ذاتية شهرياً'
                ],
                'features_en' => [
                    'Basic personality analysis',
                    'Simple CV creation',
                    'Limited job recommendations',
                    'Email support',
                    '10 AI requests monthly',
                    '3 CVs monthly'
                ],
                'features_fr' => [
                    'Analyse de personnalité de base',
                    'Création de CV simple',
                    'Recommandations d\'emploi limitées',
                    'Support par email',
                    '10 requêtes IA mensuelles',
                    '3 CV mensuels'
                ]
            ],
            [
                'name' => 'Gold',
                'features_ar' => [
                    'تحليل شخصية متقدم',
                    'إنشاء CV احترافي',
                    'توصيات وظائف غير محدودة',
                    'تصدير PDF',
                    'دعم أولوية',
                    'تحليل السوق',
                    '50 طلب ذكاء اصطناعي شهرياً',
                    '10 سير ذاتية شهرياً',
                    'تصدير PDF عالي الجودة',
                    'دعم أولوية 24/7'
                ],
                'features_en' => [
                    'Advanced personality analysis',
                    'Professional CV creation',
                    'Unlimited job recommendations',
                    'PDF export',
                    'Priority support',
                    'Market analysis',
                    '50 AI requests monthly',
                    '10 CVs monthly',
                    'High-quality PDF export',
                    '24/7 priority support'
                ],
                'features_fr' => [
                    'Analyse de personnalité avancée',
                    'Création de CV professionnel',
                    'Recommandations d\'emploi illimitées',
                    'Export PDF',
                    'Support prioritaire',
                    'Analyse de marché',
                    '50 requêtes IA mensuelles',
                    '10 CV mensuels',
                    'Export PDF haute qualité',
                    'Support prioritaire 24/7'
                ]
            ],
            [
                'name' => 'Premium',
                'features_ar' => [
                    'جميع مميزات الخطة الذهبية',
                    'مقابلات صوتية تفاعلية',
                    'جلسات توجيه شخصي',
                    'تحليل ذكاء اصطناعي متقدم',
                    'دعم 24/7',
                    'تقارير مفصلة',
                    'استشارات مهنية',
                    '200 طلب ذكاء اصطناعي شهرياً',
                    '50 سيرة ذاتية شهرياً',
                    'تصدير PDF عالي الجودة',
                    'مقابلات صوتية تفاعلية',
                    'جلسات توجيه شخصي'
                ],
                'features_en' => [
                    'All Gold plan features',
                    'Interactive voice interviews',
                    'Personal coaching sessions',
                    'Advanced AI analysis',
                    '24/7 support',
                    'Detailed reports',
                    'Career consultations',
                    '200 AI requests monthly',
                    '50 CVs monthly',
                    'High-quality PDF export',
                    'Interactive voice interviews',
                    'Personal coaching sessions'
                ],
                'features_fr' => [
                    'Toutes les fonctionnalités du plan Or',
                    'Entretiens vocaux interactifs',
                    'Sessions de coaching personnel',
                    'Analyse IA avancée',
                    'Support 24/7',
                    'Rapports détaillés',
                    'Consultations de carrière',
                    '200 requêtes IA mensuelles',
                    '50 CV mensuels',
                    'Export PDF haute qualité',
                    'Entretiens vocaux interactifs',
                    'Sessions de coaching personnel'
                ]
            ]
        ];

        foreach ($plans as $planData) {
            $plan = \App\Models\SubscriptionPlan::where('name', $planData['name'])->first();
            if ($plan) {
                $plan->update([
                    'features_ar' => $planData['features_ar'],
                    'features_en' => $planData['features_en'],
                    'features_fr' => $planData['features_fr']
                ]);
            }
        }
    }
}
