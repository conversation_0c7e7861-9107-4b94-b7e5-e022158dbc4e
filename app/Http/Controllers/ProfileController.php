<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\UserActivity;
use App\Models\UserAssessment;
use App\Models\UserAction;

class ProfileController extends Controller
{
    public function show()
    {
        $user = Auth::user();

        // Load user relationships
        $user->load(['profile', 'activities', 'assessments', 'serviceResults']);

        // Get comprehensive statistics
        $stats = [
            'total_tests' => $user->assessments()->count(),
            'completed_tests' => $user->assessments()->completed()->count(),
            'cv_improvements' => $user->activities()->byType('cv_generation')->completed()->count(),
            'interview_sessions' => $user->activities()->byType('interview_training')->count(),
            'overall_progress' => $user->calculateOverallProgress(),
            'total_activities' => $user->activities()->count(),
            'completed_activities' => $user->activities()->where('status', 'completed')->count(),
            'total_assessments' => $user->assessments()->count(),
            'completed_assessments' => $user->assessments()->whereNotNull('completed_at')->count(),
            'average_score' => $user->assessments()->whereNotNull('score')->avg('score') ?? 0,
            'join_date' => $user->created_at,
            // Add AI service results
            'ai_services_completed' => $user->serviceResults()->where('status', 'completed')->count(),
            'personality_analyses' => $user->serviceResults()->where('service_type', 'personality_analysis')->count(),
            'cv_improvements_ai' => $user->serviceResults()->where('service_type', 'cv_improvement')->count(),
            'interview_simulations' => $user->serviceResults()->where('service_type', 'interview_simulation')->count(),
        ];

        // Get recent activities (from dashboard)
        $recentActivities = $user->activities()
            ->orderBy('updated_at', 'desc')
            ->limit(5)
            ->get();

        // Get progress by category (from dashboard)
        $progressData = [
            'personality_tests' => [
                'completed' => $user->activities()->byType('personality_test')->completed()->count(),
                'total' => max(1, $user->activities()->byType('personality_test')->count()),
            ],
            'cv_generation' => [
                'completed' => $user->activities()->byType('cv_generation')->completed()->count(),
                'total' => max(1, $user->activities()->byType('cv_generation')->count()),
            ],
            'interview_training' => [
                'completed' => $user->activities()->byType('interview_training')->completed()->count(),
                'total' => max(1, $user->activities()->byType('interview_training')->count()),
            ],
        ];

        // Calculate progress percentages
        foreach ($progressData as $key => $data) {
            $progressData[$key]['percentage'] = round(($data['completed'] / $data['total']) * 100);
        }

        // Get quick actions (pending activities)
        $quickActions = $user->activities()
            ->whereIn('status', ['pending', 'in_progress'])
            ->orderBy('created_at', 'desc')
            ->limit(3)
            ->get();

        // Get recent achievements
        $recentAchievements = $user->activities()
            ->where('status', 'completed')
            ->orderBy('completed_at', 'desc')
            ->limit(6)
            ->get();

        // Get recent AI service results
        $recentServiceResults = $user->serviceResults()
            ->where('status', 'completed')
            ->orderBy('created_at', 'desc')
            ->limit(5)
            ->get();

        // Get skill assessments
        $skillAssessments = $user->assessments()
            ->where('type', 'skills')
            ->whereNotNull('completed_at')
            ->orderBy('completed_at', 'desc')
            ->get();

        // Calculate completion rate
        $completionRate = $stats['total_activities'] > 0
            ? round(($stats['completed_activities'] / $stats['total_activities']) * 100)
            : 0;

        return view('profile.show', compact(
            'user',
            'stats',
            'recentActivities',
            'progressData',
            'quickActions',
            'recentAchievements',
            'skillAssessments',
            'completionRate',
            'recentServiceResults'
        ));
    }

    /**
     * Show all user activities
     */
    public function activities()
    {
        $user = Auth::user();

        // Get all activities and actions combined
        $activities = collect();

        // Get user activities
        $userActivities = $user->activities()
            ->orderBy('created_at', 'desc')
            ->get();

        // Get user actions and convert to activity format
        $userActions = $user->actions()
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function($action) {
                return (object) [
                    'id' => $action->id,
                    'type' => $action->action_type,
                    'title' => $this->getActionTitle($action->action_type),
                    'description' => $action->action_description ?: $this->getActionDescription($action->action_type),
                    'data' => $action->action_data,
                    'status' => 'completed',
                    'progress_percentage' => 100,
                    'created_at' => $action->created_at,
                    'updated_at' => $action->updated_at,
                    'completed_at' => $action->created_at,
                    'is_action' => true
                ];
            });

        // Combine and sort by date
        $allActivities = $userActivities->concat($userActions)
            ->sortByDesc('created_at');

        // Manual pagination
        $perPage = 20;
        $currentPage = request()->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        $activities = $allActivities->slice($offset, $perPage);

        // Create paginator
        $activities = new \Illuminate\Pagination\LengthAwarePaginator(
            $activities,
            $allActivities->count(),
            $perPage,
            $currentPage,
            ['path' => request()->url(), 'pageName' => 'page']
        );

        // Calculate statistics
        $totalActivities = $userActivities->count() + $userActions->count();
        $completedActivities = $userActivities->where('status', 'completed')->count() + $userActions->count();
        $cvCount = $userActivities->whereIn('type', ['cv_generation', 'cv_creation'])->count() +
                   $userActions->whereIn('action_type', ['cv_created', 'cv_ai_generation'])->count();
        $testsCount = $userActivities->where('type', 'personality_test')->count() +
                      $userActions->where('action_type', 'personality_analysis')->count();

        return view('profile.activities', compact(
            'activities',
            'totalActivities',
            'completedActivities',
            'cvCount',
            'testsCount'
        ));
    }

    /**
     * Get action title in Arabic
     */
    private function getActionTitle($actionType)
    {
        $titles = [
            'cv_created' => 'إنشاء سيرة ذاتية',
            'cv_ai_generation' => 'إنشاء سيرة ذاتية بالذكاء الاصطناعي',
            'cv_download' => 'تحميل سيرة ذاتية',
            'personality_analysis' => 'اختبار تحليل الشخصية',
            'login' => 'تسجيل دخول',
            'logout' => 'تسجيل خروج',
            'profile_update' => 'تحديث الملف الشخصي',
        ];

        return $titles[$actionType] ?? ucfirst(str_replace('_', ' ', $actionType));
    }

    /**
     * Get action description in Arabic
     */
    private function getActionDescription($actionType)
    {
        $descriptions = [
            'cv_created' => 'تم إنشاء سيرة ذاتية جديدة بنجاح',
            'cv_ai_generation' => 'تم إنشاء سيرة ذاتية باستخدام الذكاء الاصطناعي',
            'cv_download' => 'تم تحميل ملف السيرة الذاتية',
            'personality_analysis' => 'تم إكمال اختبار تحليل الشخصية',
            'login' => 'تم تسجيل الدخول إلى المنصة',
            'logout' => 'تم تسجيل الخروج من المنصة',
            'profile_update' => 'تم تحديث بيانات الملف الشخصي',
        ];

        return $descriptions[$actionType] ?? 'تم تنفيذ العملية بنجاح';
    }

    public function edit()
    {
        $user = Auth::user();
        $user->load('profile');

        return view('profile.edit', compact('user'));
    }

    public function update(Request $request)
    {
        $user = Auth::user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'phone' => 'nullable|string|max:20',
            'birth_date' => 'nullable|date',
            'gender' => 'nullable|in:male,female',
            'city' => 'nullable|string|max:100',
            'country' => 'nullable|string|max:100',
            'bio' => 'nullable|string|max:1000',
            'education_level' => 'nullable|string|max:100',
            'field_of_study' => 'nullable|string|max:100',
            'current_job' => 'nullable|string|max:100',
            'experience_years' => 'nullable|integer|min:0|max:50',
            'skills' => 'nullable|string',
            'interests' => 'nullable|string',
            'linkedin_url' => 'nullable|url',
            'github_url' => 'nullable|url',
            'portfolio_url' => 'nullable|url',
        ]);

        // Update user basic info
        $user->update([
            'name' => $request->name,
            'email' => $request->email,
        ]);

        // Update or create profile
        $profileData = $request->only([
            'phone', 'birth_date', 'gender', 'city', 'country', 'bio',
            'education_level', 'field_of_study', 'current_job', 'experience_years',
            'linkedin_url', 'github_url', 'portfolio_url'
        ]);

        // Convert skills and interests from comma-separated strings to arrays
        if ($request->skills) {
            $profileData['skills'] = array_map('trim', explode(',', $request->skills));
        }
        if ($request->interests) {
            $profileData['interests'] = array_map('trim', explode(',', $request->interests));
        }

        $user->profile()->updateOrCreate(
            ['user_id' => $user->id],
            $profileData
        );

        return redirect()->route('profile')->with('success', 'تم تحديث الملف الشخصي بنجاح!');
    }

    public function share()
    {
        $user = Auth::user();

        // Generate a shareable link (you can implement token-based sharing later)
        $shareUrl = route('profile.public', ['user' => $user->id]);

        return response()->json([
            'success' => true,
            'share_url' => $shareUrl,
            'message' => 'تم إنشاء رابط المشاركة بنجاح!'
        ]);
    }
}
