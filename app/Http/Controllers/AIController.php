<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\AIService;
use App\Models\UserAction;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;

class AIController extends Controller
{
    protected $aiService;

    public function __construct(AIService $aiService)
    {
        $this->aiService = $aiService;
        $this->middleware('auth');
    }

    /**
     * Analyze user personality
     */
    public function analyzePersonality(Request $request)
    {
        $request->validate([
            'answers' => 'required|array|min:5',
            'answers.*' => 'required|string|max:500',
        ]);

        // Rate limiting
        $key = 'personality_analysis_' . Auth::id();
        if (RateLimiter::tooManyAttempts($key, 2)) {
            return response()->json([
                'success' => false,
                'error' => 'لقد تجاوزت الحد المسموح من المحاولات. يرجى المحاولة لاحقاً.'
            ], 429);
        }

        RateLimiter::hit($key, 3600); // 1 hour

        try {
            $language = app()->getLocale();
            $result = $this->aiService->analyzePersonality($request->answers, $language);

            if ($result['success']) {
                // Log user activity
                UserActivity::create([
                    'user_id' => Auth::id(),
                    'type' => 'personality_test',
                    'title' => 'اختبار تحليل الشخصية',
                    'description' => 'تم إكمال اختبار تحليل الشخصية بنجاح',
                    'data' => [
                        'answers_count' => count($request->answers),
                        'personality_type' => $result['personality_type'] ?? null,
                        'score' => $result['score'] ?? null,
                        'language' => $language
                    ],
                    'status' => 'completed',
                    'progress_percentage' => 100,
                    'completed_at' => now()
                ]);

                UserAction::logAction(
                    Auth::id(),
                    'personality_analysis',
                    'تم إكمال اختبار تحليل الشخصية',
                    [
                        'answers_count' => count($request->answers),
                        'personality_type' => $result['personality_type'] ?? null
                    ]
                );

                // Cache result
                Cache::put(
                    'personality_' . Auth::id(),
                    $result,
                    config('ai.cache.ttl.personality_analysis', 3600)
                );
            }

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'حدث خطأ في تحليل الشخصية. يرجى المحاولة مرة أخرى.'
            ], 500);
        }
    }

    /**
     * Generate CV
     */
    public function generateCV(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:100',
            'skills' => 'required|array|min:1',
            'experience' => 'array',
            'education' => 'string|max:500',
            'target_job' => 'string|max:100',
        ]);

        // Rate limiting
        $key = 'cv_generation_' . Auth::id();
        if (RateLimiter::tooManyAttempts($key, 3)) {
            return response()->json([
                'success' => false,
                'error' => 'لقد تجاوزت الحد المسموح من المحاولات. يرجى المحاولة لاحقاً.'
            ], 429);
        }

        RateLimiter::hit($key, 3600); // 1 hour

        try {
            $language = app()->getLocale();
            $result = $this->aiService->generateCV($request->all(), $language);

            if ($result['success']) {
                // Log user action
                UserAction::logAction(
                    Auth::id(),
                    'cv_generated',
                    'User generated CV with AI',
                    ['target_job' => $request->target_job]
                );
            }

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'حدث خطأ في إنشاء السيرة الذاتية. يرجى المحاولة مرة أخرى.'
            ], 500);
        }
    }

    /**
     * Get job recommendations
     */
    public function recommendJobs(Request $request)
    {
        $request->validate([
            'personality_type' => 'string|max:100',
            'skills' => 'array',
            'interests' => 'array',
            'experience_level' => 'string|max:50',
        ]);

        // Rate limiting
        $key = 'job_recommendations_' . Auth::id();
        if (RateLimiter::tooManyAttempts($key, 5)) {
            return response()->json([
                'success' => false,
                'error' => 'لقد تجاوزت الحد المسموح من المحاولات. يرجى المحاولة لاحقاً.'
            ], 429);
        }

        RateLimiter::hit($key, 3600); // 1 hour

        try {
            $language = app()->getLocale();
            $result = $this->aiService->recommendJobs($request->all(), $language);

            if ($result['success']) {
                // Log user action
                UserAction::logAction(
                    Auth::id(),
                    'job_recommendations',
                    'User requested job recommendations',
                    ['experience_level' => $request->experience_level]
                );
            }

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'حدث خطأ في توصيات الوظائف. يرجى المحاولة مرة أخرى.'
            ], 500);
        }
    }

    /**
     * Chat with AI assistant
     */
    public function chat(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:1000',
            'context' => 'array',
        ]);

        // Rate limiting
        $key = 'ai_chat_' . Auth::id();
        if (RateLimiter::tooManyAttempts($key, 20)) {
            return response()->json([
                'success' => false,
                'error' => 'لقد تجاوزت الحد المسموح من الرسائل. يرجى المحاولة لاحقاً.'
            ], 429);
        }

        RateLimiter::hit($key, 3600); // 1 hour

        try {
            $language = app()->getLocale();
            $result = $this->aiService->chatBot($request->message, $request->context ?? [], $language);

            if ($result['success']) {
                // Log user action
                UserAction::logAction(
                    Auth::id(),
                    'ai_chat',
                    'User chatted with AI assistant',
                    ['message_length' => strlen($request->message)]
                );
            }

            return response()->json($result);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'error' => 'حدث خطأ في المساعد الذكي. يرجى المحاولة مرة أخرى.'
            ], 500);
        }
    }

    /**
     * Get cached personality analysis
     */
    public function getPersonalityAnalysis()
    {
        $cached = Cache::get('personality_' . Auth::id());

        if ($cached) {
            return response()->json([
                'success' => true,
                'data' => $cached,
                'cached' => true
            ]);
        }

        return response()->json([
            'success' => false,
            'error' => 'لا يوجد تحليل شخصية محفوظ. يرجى إجراء التحليل أولاً.'
        ], 404);
    }
}
