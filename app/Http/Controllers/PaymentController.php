<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\PaymentService;
use App\Models\SubscriptionPlan;
use App\Models\PaidService;
use App\Models\Payment;
use App\Models\ServiceOrder;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Str;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->middleware('auth');
        $this->paymentService = $paymentService;
    }

    /**
     * Subscribe to a plan
     */
    public function subscribe(Request $request)
    {
        $request->validate([
            'plan_id' => 'required|exists:subscription_plans,id',
            'payment_method' => 'required|in:stripe,paypal',
            'billing_cycle' => 'required|in:monthly,yearly'
        ]);

        $plan = SubscriptionPlan::findOrFail($request->plan_id);
        $user = Auth::user();

        // Check if user already has an active subscription
        $activeSubscription = $this->paymentService->getUserActiveSubscription($user);
        if ($activeSubscription) {
            return response()->json([
                'success' => false,
                'message' => 'لديك اشتراك نشط بالفعل'
            ]);
        }

        $result = $this->paymentService->createSubscriptionPayment(
            $user,
            $plan,
            $request->payment_method,
            $request->billing_cycle
        );

        return response()->json($result);
    }

    /**
     * Order a paid service
     */
    public function orderService(Request $request)
    {
        $request->validate([
            'service_id' => 'required|exists:paid_services,id',
            'payment_method' => 'required|in:stripe,paypal'
        ]);

        $service = PaidService::findOrFail($request->service_id);
        $user = Auth::user();

        $result = $this->paymentService->createServicePayment(
            $user,
            $service,
            $request->payment_method
        );

        return response()->json($result);
    }

    /**
     * Stripe checkout page
     */
    public function stripeCheckout(Payment $payment)
    {
        if ($payment->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payment.stripe-checkout', compact('payment'));
    }

    /**
     * PayPal checkout page
     */
    public function paypalCheckout(Payment $payment)
    {
        if ($payment->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payment.paypal-checkout', compact('payment'));
    }

    /**
     * Handle Stripe webhook
     */
    public function stripeWebhook(Request $request)
    {
        // Handle Stripe webhook events
        $payload = $request->getContent();
        $sig_header = $request->header('Stripe-Signature');

        // Verify webhook signature and process events
        // This will be implemented when integrating with actual Stripe

        return response('OK', 200);
    }

    /**
     * Handle PayPal webhook
     */
    public function paypalWebhook(Request $request)
    {
        // Handle PayPal webhook events
        // This will be implemented when integrating with actual PayPal

        return response('OK', 200);
    }

    /**
     * Payment success page
     */
    public function success(Request $request)
    {
        $paymentId = $request->get('payment_id');
        $payment = Payment::where('id', $paymentId)
                         ->where('user_id', Auth::id())
                         ->first();

        if (!$payment) {
            return redirect()->route('pricing')->with('error', 'الدفعة غير موجودة');
        }

        return view('payment.success', compact('payment'));
    }

    /**
     * Payment cancel page
     */
    public function cancel(Request $request)
    {
        return view('payment.cancel');
    }

    /**
     * User's payment history
     */
    public function history()
    {
        $payments = Auth::user()->payments()
                       ->with('payable')
                       ->orderBy('created_at', 'desc')
                       ->paginate(10);

        return view('payment.history', compact('payments'));
    }

    /**
     * Cancel subscription
     */
    public function cancelSubscription(Request $request)
    {
        $user = Auth::user();
        $subscription = $this->paymentService->getUserActiveSubscription($user);

        if (!$subscription) {
            return response()->json([
                'success' => false,
                'message' => 'لا يوجد اشتراك نشط للإلغاء'
            ]);
        }

        $result = $this->paymentService->cancelSubscription($subscription);

        return response()->json([
            'success' => $result,
            'message' => $result ? 'تم إلغاء الاشتراك بنجاح' : 'حدث خطأ في إلغاء الاشتراك'
        ]);
    }

    /**
     * Show payment page for a service
     */
    public function showServicePayment(Request $request)
    {
        $request->validate([
            'service_id' => 'required|exists:paid_services,id',
            'service_type' => 'required|string',
            'level' => 'required|string'
        ]);

        $service = PaidService::findOrFail($request->service_id);
        $user = Auth::user();

        return view('payment.service', compact('service', 'user'));
    }

    /**
     * Process service payment
     */
    public function processServicePayment(Request $request)
    {
        $request->validate([
            'service_id' => 'required|exists:paid_services,id',
            'payment_method' => 'required|in:stripe,paypal',
            'service_data' => 'nullable|array'
        ]);

        $service = PaidService::findOrFail($request->service_id);
        $user = Auth::user();

        try {
            // Create service order
            $order = ServiceOrder::create([
                'order_number' => 'ORD-' . strtoupper(Str::random(8)),
                'user_id' => $user->id,
                'paid_service_id' => $service->id,
                'amount' => $service->price,
                'currency' => $service->currency,
                'status' => 'pending',
                'payment_status' => 'pending',
                'service_data' => $request->service_data ?? [],
                'due_date' => now()->addHours($service->delivery_time_hours)
            ]);

            // Create payment record
            $payment = Payment::create([
                'payment_id' => 'PAY-' . strtoupper(Str::random(12)),
                'user_id' => $user->id,
                'service_order_id' => $order->id,
                'amount' => $service->price,
                'currency' => $service->currency,
                'status' => 'pending',
                'payment_method' => $request->payment_method,
                'metadata' => [
                    'service_name' => $service->name,
                    'service_type' => $service->type,
                    'user_ip' => $request->ip(),
                    'user_agent' => $request->userAgent()
                ]
            ]);

            // Process payment based on method
            if ($request->payment_method === 'stripe') {
                $result = $this->processStripePayment($payment, $service);
            } else {
                $result = $this->processPayPalPayment($payment, $service);
            }

            if ($result['success']) {
                $payment->update([
                    'status' => 'completed',
                    'gateway_transaction_id' => $result['transaction_id'],
                    'gateway_response' => $result['response'],
                    'paid_at' => now()
                ]);

                $order->update([
                    'payment_status' => 'paid',
                    'status' => 'processing',
                    'paid_at' => now(),
                    'started_at' => now()
                ]);

                return response()->json([
                    'success' => true,
                    'message' => __('messages.payment_success'),
                    'order_id' => $order->id,
                    'redirect_url' => route('services.order.success', $order->id)
                ]);
            } else {
                $payment->update([
                    'status' => 'failed',
                    'failure_reason' => $result['error'],
                    'failed_at' => now()
                ]);

                return response()->json([
                    'success' => false,
                    'message' => $result['error']
                ]);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('messages.payment_error')
            ]);
        }
    }

    /**
     * Process Stripe payment
     */
    private function processStripePayment($payment, $service)
    {
        // Simulate Stripe payment processing
        // In real implementation, you would use Stripe SDK
        return [
            'success' => true,
            'transaction_id' => 'stripe_' . Str::random(16),
            'response' => [
                'status' => 'succeeded',
                'amount' => $payment->amount * 100, // Stripe uses cents
                'currency' => $payment->currency
            ]
        ];
    }

    /**
     * Process PayPal payment
     */
    private function processPayPalPayment($payment, $service)
    {
        // Simulate PayPal payment processing
        // In real implementation, you would use PayPal SDK
        return [
            'success' => true,
            'transaction_id' => 'paypal_' . Str::random(16),
            'response' => [
                'status' => 'COMPLETED',
                'amount' => $payment->amount,
                'currency' => $payment->currency
            ]
        ];
    }

    /**
     * Show payment success page
     */
    public function showPaymentSuccess($orderId)
    {
        $order = ServiceOrder::with(['paidService', 'user'])->findOrFail($orderId);

        // Ensure the order belongs to the authenticated user
        if ($order->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payment.success', compact('order'));
    }
}
