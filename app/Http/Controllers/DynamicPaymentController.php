<?php

namespace App\Http\Controllers;

use App\Models\PaidService;
use App\Models\PaymentTransaction;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class DynamicPaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
        $this->middleware('auth');
    }

    /**
     * Show payment page for a service
     */
    public function show(PaidService $service)
    {
        $user = Auth::user();
        
        return view('payment.dynamic', compact('service', 'user'));
    }

    /**
     * Process payment
     */
    public function process(Request $request, PaidService $service)
    {
        $validator = Validator::make($request->all(), [
            'payment_method' => 'required|in:stripe,paypal',
            'customer_name' => 'required|string|max:255',
            'customer_email' => 'required|email|max:255',
            'customer_phone' => 'nullable|string|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $user = Auth::user();

        try {
            // Create transaction
            $transaction = $this->paymentService->createTransaction([
                'user_id' => $user->id,
                'service_id' => $service->id,
                'payment_method' => $request->payment_method,
                'amount' => $service->price,
                'currency' => 'MAD',
                'customer_name' => $request->customer_name,
                'customer_email' => $request->customer_email,
                'customer_phone' => $request->customer_phone,
            ]);

            return response()->json([
                'success' => true,
                'transaction_id' => $transaction->id,
                'message' => __('messages.transaction_created')
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('messages.transaction_failed')
            ], 500);
        }
    }

    /**
     * Process Stripe card payment
     */
    public function processStripe(Request $request, PaymentTransaction $transaction)
    {
        $validator = Validator::make($request->all(), [
            'card_number' => 'required|string|min:13|max:19',
            'expiry_month' => 'required|integer|min:1|max:12',
            'expiry_year' => 'required|integer|min:' . date('Y'),
            'cvv' => 'required|string|min:3|max:4',
            'cardholder_name' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Validate card number format
        $cardNumber = preg_replace('/\D/', '', $request->card_number);
        if (!$this->isValidCardNumber($cardNumber)) {
            return response()->json([
                'success' => false,
                'message' => __('messages.invalid_card_number')
            ], 422);
        }

        try {
            $success = $this->paymentService->processStripeCardPayment($transaction, [
                'card_number' => $cardNumber,
                'expiry_month' => $request->expiry_month,
                'expiry_year' => $request->expiry_year,
                'cvv' => $request->cvv,
                'cardholder_name' => $request->cardholder_name,
            ]);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => __('messages.payment_successful'),
                    'redirect_url' => route('payment.success', $transaction->id)
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.payment_failed')
                ], 422);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('messages.payment_error')
            ], 500);
        }
    }

    /**
     * Process PayPal payment
     */
    public function processPayPal(Request $request, PaymentTransaction $transaction)
    {
        $validator = Validator::make($request->all(), [
            'paypal_email' => 'required|email|max:255',
            'payer_id' => 'nullable|string|max:50',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        try {
            $success = $this->paymentService->processPayPalTransactionPayment($transaction, [
                'paypal_email' => $request->paypal_email,
                'payer_id' => $request->payer_id,
            ]);

            if ($success) {
                return response()->json([
                    'success' => true,
                    'message' => __('messages.payment_successful'),
                    'redirect_url' => route('payment.success', $transaction->id)
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => __('messages.payment_failed')
                ], 422);
            }

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('messages.payment_error')
            ], 500);
        }
    }

    /**
     * Show payment success page
     */
    public function success(PaymentTransaction $transaction)
    {
        // Ensure user can only see their own transactions
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payment.success', compact('transaction'));
    }

    /**
     * Show payment failure page
     */
    public function failed(PaymentTransaction $transaction)
    {
        // Ensure user can only see their own transactions
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        return view('payment.failed', compact('transaction'));
    }

    /**
     * Get transaction status
     */
    public function status(PaymentTransaction $transaction)
    {
        // Ensure user can only see their own transactions
        if ($transaction->user_id !== Auth::id()) {
            abort(403);
        }

        return response()->json([
            'status' => $transaction->status,
            'transaction_id' => $transaction->transaction_id,
            'amount' => $transaction->formatted_amount,
            'paid_at' => $transaction->paid_at?->format('Y-m-d H:i:s'),
            'failure_reason' => $transaction->failure_reason,
        ]);
    }

    /**
     * Validate card number using Luhn algorithm
     */
    private function isValidCardNumber(string $cardNumber): bool
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);
        $length = strlen($cardNumber);
        
        if ($length < 13 || $length > 19) {
            return false;
        }

        $sum = 0;
        $alternate = false;
        
        for ($i = $length - 1; $i >= 0; $i--) {
            $digit = intval($cardNumber[$i]);
            
            if ($alternate) {
                $digit *= 2;
                if ($digit > 9) {
                    $digit = ($digit % 10) + 1;
                }
            }
            
            $sum += $digit;
            $alternate = !$alternate;
        }
        
        return ($sum % 10) === 0;
    }
}
