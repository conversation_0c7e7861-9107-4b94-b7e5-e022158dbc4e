<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\SubscriptionPlan;
use App\Models\PaidService;
use App\Models\UserSubscription;
use App\Models\ServiceOrder;
use Illuminate\Support\Facades\Auth;

class SubscriptionController extends Controller
{
    /**
     * Display pricing page with dynamic data
     */
    public function pricing()
    {
        // Get all active subscription plans ordered by sort_order
        $subscriptionPlans = SubscriptionPlan::active()
            ->ordered()
            ->get();

        // Get all active paid services ordered by sort_order
        $paidServices = PaidService::where('is_active', true)
            ->orderBy('sort_order')
            ->get();

        // Get user's current subscription if authenticated
        $currentSubscription = null;
        if (Auth::check()) {
            $currentSubscription = UserSubscription::where('user_id', Auth::id())
                ->where('status', 'active')
                ->with('subscriptionPlan')
                ->first();
        }

        // Calculate savings for yearly plans
        foreach ($subscriptionPlans as $plan) {
            $plan->yearly_savings = $plan->yearly_savings;
            $plan->yearly_savings_percentage = $plan->yearly_savings_percentage;
        }

        return view('pricing', compact('subscriptionPlans', 'paidServices', 'currentSubscription'));
    }

    /**
     * Subscribe to a plan
     */
    public function subscribe(Request $request, SubscriptionPlan $plan)
    {
        $request->validate([
            'billing_cycle' => 'required|in:monthly,yearly',
            'payment_method' => 'required|string',
        ]);

        // Check if user already has an active subscription
        $existingSubscription = UserSubscription::where('user_id', Auth::id())
            ->where('status', 'active')
            ->first();

        if ($existingSubscription) {
            return response()->json([
                'success' => false,
                'error' => 'لديك اشتراك نشط بالفعل. يرجى إلغاؤه أولاً.'
            ], 400);
        }

        $billingCycle = $request->billing_cycle;
        $amount = $billingCycle === 'yearly' ? $plan->price_yearly : $plan->price_monthly;

        // Create subscription record
        $subscription = UserSubscription::create([
            'user_id' => Auth::id(),
            'subscription_plan_id' => $plan->id,
            'billing_cycle' => $billingCycle,
            'amount_paid' => $amount,
            'currency' => $plan->currency,
            'status' => 'pending',
            'starts_at' => now(),
            'ends_at' => $billingCycle === 'yearly' ? now()->addYear() : now()->addMonth(),
            'payment_method' => $request->payment_method,
            'usage_limits' => [
                'ai_requests' => $plan->ai_requests_limit,
                'cv_generations' => $plan->cv_generations_limit,
                'pdf_export' => $plan->pdf_export,
                'voice_interviews' => $plan->voice_interviews,
                'personal_coaching' => $plan->personal_coaching,
                'priority_support' => $plan->priority_support,
            ],
            'usage_current' => [
                'ai_requests' => 0,
                'cv_generations' => 0,
                'pdf_exports' => 0,
                'voice_interviews' => 0,
                'coaching_sessions' => 0,
            ],
        ]);

        // Here you would integrate with payment gateway
        // For now, we'll simulate successful payment
        $subscription->update([
            'status' => 'active',
            'paid_at' => now(),
            'transaction_id' => 'sim_' . uniqid(),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم الاشتراك بنجاح!',
            'subscription' => $subscription
        ]);
    }

    /**
     * Order a paid service
     */
    public function orderService(Request $request, PaidService $service)
    {
        $request->validate([
            'service_data' => 'required|array',
            'customer_notes' => 'nullable|string|max:1000',
        ]);

        $orderNumber = 'ORD-' . date('Ymd') . '-' . strtoupper(uniqid());

        $order = ServiceOrder::create([
            'order_number' => $orderNumber,
            'user_id' => Auth::id(),
            'paid_service_id' => $service->id,
            'amount' => $service->price,
            'currency' => $service->currency,
            'status' => 'pending',
            'payment_status' => 'pending',
            'service_data' => $request->service_data,
            'customer_notes' => $request->customer_notes,
            'due_date' => now()->addHours($service->delivery_time_hours),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'تم إنشاء الطلب بنجاح!',
            'order' => $order
        ]);
    }

    /**
     * Get user's subscription status
     */
    public function getSubscriptionStatus()
    {
        if (!Auth::check()) {
            return response()->json(['authenticated' => false]);
        }

        $subscription = UserSubscription::where('user_id', Auth::id())
            ->where('status', 'active')
            ->with('subscriptionPlan')
            ->first();

        return response()->json([
            'authenticated' => true,
            'has_subscription' => $subscription ? true : false,
            'subscription' => $subscription,
            'usage' => $subscription ? $subscription->usage_current : null,
            'limits' => $subscription ? $subscription->usage_limits : null,
        ]);
    }
}
