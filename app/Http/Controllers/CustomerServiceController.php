<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\AIService;
use App\Models\UserAction;
use Illuminate\Support\Facades\Auth;

class CustomerServiceController extends Controller
{
    protected $aiService;

    public function __construct(AIService $aiService)
    {
        $this->aiService = $aiService;
    }

    /**
     * Handle customer service chat
     */
    public function chat(Request $request)
    {
        $validated = $request->validate([
            'message' => 'required|string|max:500',
            'language' => 'nullable|string|in:ar,en,fr'
        ]);

        $language = $validated['language'] ?? app()->getLocale();
        $message = $validated['message'];

        // Get AI response
        $aiResponse = $this->aiService->getCustomerServiceResponse($message, $language);

        // Log user action if authenticated
        if (Auth::check()) {
            UserAction::logAction(
                Auth::id(),
                'customer_service_chat',
                'استخدام خدمة العملاء الذكية',
                [
                    'message' => $message,
                    'language' => $language,
                    'response_success' => $aiResponse['success']
                ]
            );
        }

        if ($aiResponse['success']) {
            return response()->json([
                'success' => true,
                'response' => $aiResponse['response']
            ]);
        }

        return response()->json([
            'success' => false,
            'message' => 'حدث خطأ في الخدمة. يرجى المحاولة مرة أخرى.'
        ], 500);
    }

    /**
     * Get quick action responses
     */
    public function quickAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|string|in:services,pricing,cv_help,contact',
            'language' => 'nullable|string|in:ar,en,fr'
        ]);

        $language = $validated['language'] ?? app()->getLocale();
        $action = $validated['action'];

        $responses = [
            'ar' => [
                'services' => 'نحن نقدم خدمات متنوعة في MonOri AI:\n• إنشاء السير الذاتية بالذكاء الاصطناعي\n• اختبارات تحليل الشخصية المهنية\n• مقابلات العمل التفاعلية مع الروبوت\n• توصيات الوظائف المخصصة\n• تحسين الملف المهني',
                'pricing' => 'لدينا خطط اشتراك متنوعة تناسب جميع الاحتياجات:\n• الخطة الأساسية: 19 درهم/شهر\n• الخطة الذهبية: 49 درهم/شهر\n• الخطة المميزة: 99 درهم/شهر\n\nكما نقدم خدمات منفردة مثل إنشاء السيرة الذاتية والتدريب على المقابلات.',
                'cv' => 'إنشاء السيرة الذاتية سهل جداً! يمكنك:\n1. الذهاب إلى قسم "إنشاء السيرة الذاتية"\n2. ملء بياناتك الأساسية\n3. استخدام الذكاء الاصطناعي للحصول على محتوى احترافي\n4. اختيار التصميم المناسب\n5. تحميل السيرة الذاتية بصيغة PDF',
                'contact' => 'يمكنك التواصل معنا بعدة طرق:\n• عبر هذا الشات الذكي (متاح 24/7)\n• إرسال إيميل لفريق الدعم\n• زيارة صفحة "اتصل بنا"\n• متابعتنا على وسائل التواصل الاجتماعي'
            ],
            'en' => [
                'services' => 'We offer various services at MonOri AI:\n• AI-powered CV creation\n• Professional personality analysis tests\n• Interactive job interviews with robot\n• Personalized job recommendations\n• Professional profile enhancement',
                'pricing' => 'We have flexible subscription plans for all needs:\n• Basic Plan: 19 DH/month\n• Gold Plan: 49 DH/month\n• Premium Plan: 99 DH/month\n\nWe also offer individual services like CV creation and interview training.',
                'cv' => 'Creating a CV is very easy! You can:\n1. Go to "CV Builder" section\n2. Fill in your basic information\n3. Use AI for professional content\n4. Choose the right design\n5. Download your CV as PDF',
                'contact' => 'You can contact us in several ways:\n• Through this smart chat (available 24/7)\n• Send email to our support team\n• Visit our "Contact Us" page\n• Follow us on social media'
            ],
            'fr' => [
                'services' => 'Nous offrons divers services chez MonOri AI:\n• Création de CV alimentée par IA\n• Tests d\'analyse de personnalité professionnelle\n• Entretiens d\'embauche interactifs avec robot\n• Recommandations d\'emploi personnalisées\n• Amélioration du profil professionnel',
                'pricing' => 'Nous avons des plans d\'abonnement flexibles pour tous les besoins:\n• Plan de base: 19 DH/mois\n• Plan or: 49 DH/mois\n• Plan premium: 99 DH/mois\n\nNous offrons également des services individuels comme la création de CV et la formation aux entretiens.',
                'cv' => 'Créer un CV est très facile! Vous pouvez:\n1. Aller à la section "Créateur de CV"\n2. Remplir vos informations de base\n3. Utiliser l\'IA pour un contenu professionnel\n4. Choisir le bon design\n5. Télécharger votre CV en PDF',
                'contact' => 'Vous pouvez nous contacter de plusieurs façons:\n• Via ce chat intelligent (disponible 24/7)\n• Envoyer un email à notre équipe de support\n• Visiter notre page "Contactez-nous"\n• Nous suivre sur les réseaux sociaux'
            ]
        ];

        $response = $responses[$language][$action] ?? $responses['ar'][$action];

        return response()->json([
            'success' => true,
            'response' => $response
        ]);
    }
}
