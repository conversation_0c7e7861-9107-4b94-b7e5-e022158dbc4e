<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Models\User;
use App\Models\ServiceResult;
use Barryvdh\DomPDF\Facade\Pdf;

class CVImprovementController extends Controller
{
    public function index()
    {
        return view('services.cv-improvement');
    }

    public function basicBuilder()
    {
        return view('services.free.cv-improvement');
    }

    public function generateCV(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string|max:255',
            'email' => 'required|email',
            'phone' => 'required|string',
            'location' => 'nullable|string',
            'summary' => 'nullable|string',
            'experience' => 'nullable|array',
            'education' => 'nullable|array',
            'skills' => 'nullable|array',
            'languages' => 'nullable|array',
            'cv_language' => 'required|in:en,ar,fr',
            'level' => 'required|in:basic,professional,premium'
        ]);

        try {
            // Prepare data for AI
            $cvData = [
                'personal_info' => [
                    'name' => $request->full_name,
                    'email' => $request->email,
                    'phone' => $request->phone,
                    'location' => $request->location,
                ],
                'summary' => $request->summary,
                'experience' => $request->experience ?? [],
                'education' => $request->education ?? [],
                'skills' => $request->skills ?? [],
                'languages' => $request->languages ?? [],
                'cv_language' => $request->cv_language,
                'level' => $request->level
            ];

            // Generate AI-improved CV
            $improvedCV = $this->improveWithAI($cvData);

            // Generate PDF
            $pdfPath = $this->generatePDF($improvedCV, $request->level);

            // Save to database if user is authenticated
            if (Auth::check()) {
                ServiceResult::create([
                    'user_id' => Auth::id(),
                    'service_type' => 'cv_improvement',
                    'service_level' => $request->level,
                    'input_data' => $cvData,
                    'result_data' => $improvedCV,
                    'file_path' => $pdfPath,
                    'status' => 'completed'
                ]);
            }

            return response()->json([
                'success' => true,
                'message' => __('messages.cv_generated_successfully'),
                'improved_cv' => $improvedCV,
                'download_url' => route('cv-improvement.download', ['file' => basename($pdfPath)])
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => __('messages.cv_generation_failed'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function improveWithAI($cvData)
    {
        $level = $cvData['level'];
        
        // Different AI improvements based on level
        switch ($level) {
            case 'basic':
                return $this->basicAIImprovement($cvData);
            case 'professional':
                return $this->professionalAIImprovement($cvData);
            case 'premium':
                return $this->premiumAIImprovement($cvData);
            default:
                return $this->basicAIImprovement($cvData);
        }
    }

    private function basicAIImprovement($cvData)
    {
        // Basic AI improvements
        $improved = $cvData;
        
        // Improve summary
        if (empty($improved['summary'])) {
            $improved['summary'] = $this->generateBasicSummary($cvData['personal_info']['name']);
        } else {
            $improved['summary'] = $this->enhanceText($improved['summary'], 'basic');
        }

        // Add basic skills if none provided
        if (empty($improved['skills'])) {
            $improved['skills'] = $this->suggestBasicSkills();
        }

        // Format experience
        $improved['experience'] = $this->formatExperience($improved['experience'], 'basic');

        return $improved;
    }

    private function professionalAIImprovement($cvData)
    {
        // Professional AI improvements
        $improved = $this->basicAIImprovement($cvData);
        
        // Enhanced summary with keywords
        $improved['summary'] = $this->enhanceText($improved['summary'], 'professional');
        
        // Add professional skills
        $improved['skills'] = array_merge($improved['skills'], $this->suggestProfessionalSkills());
        
        // Enhanced experience descriptions
        $improved['experience'] = $this->formatExperience($improved['experience'], 'professional');
        
        // Add achievements section
        $improved['achievements'] = $this->generateAchievements($cvData);
        
        return $improved;
    }

    private function premiumAIImprovement($cvData)
    {
        // Premium AI improvements
        $improved = $this->professionalAIImprovement($cvData);
        
        // Advanced AI enhancements
        $improved['summary'] = $this->enhanceText($improved['summary'], 'premium');
        
        // Industry-specific optimizations
        $improved = $this->optimizeForIndustry($improved);
        
        // Add certifications suggestions
        $improved['suggested_certifications'] = $this->suggestCertifications($cvData);
        
        // Add career recommendations
        $improved['career_recommendations'] = $this->generateCareerRecommendations($cvData);
        
        return $improved;
    }

    private function generateBasicSummary($name)
    {
        return "Motivated professional with strong work ethic and excellent communication skills. Seeking opportunities to contribute to organizational success while developing professional expertise.";
    }

    private function enhanceText($text, $level)
    {
        // Simulate AI text enhancement based on level
        $enhancements = [
            'basic' => [
                'good' => 'excellent',
                'nice' => 'outstanding',
                'work' => 'collaborate effectively'
            ],
            'professional' => [
                'managed' => 'successfully managed and optimized',
                'worked' => 'collaborated cross-functionally',
                'helped' => 'facilitated and streamlined'
            ],
            'premium' => [
                'led' => 'spearheaded strategic initiatives',
                'improved' => 'revolutionized and enhanced',
                'created' => 'architected and implemented'
            ]
        ];

        $levelEnhancements = $enhancements[$level] ?? $enhancements['basic'];
        
        foreach ($levelEnhancements as $old => $new) {
            $text = str_ireplace($old, $new, $text);
        }

        return $text;
    }

    private function suggestBasicSkills()
    {
        return [
            'Communication',
            'Teamwork',
            'Problem Solving',
            'Time Management',
            'Microsoft Office',
            'Customer Service'
        ];
    }

    private function suggestProfessionalSkills()
    {
        return [
            'Project Management',
            'Data Analysis',
            'Strategic Planning',
            'Leadership',
            'Process Improvement',
            'Digital Marketing'
        ];
    }

    private function formatExperience($experience, $level)
    {
        if (empty($experience)) {
            return [];
        }

        foreach ($experience as &$exp) {
            if (isset($exp['description'])) {
                $exp['description'] = $this->enhanceText($exp['description'], $level);
            }
        }

        return $experience;
    }

    private function generateAchievements($cvData)
    {
        return [
            'Demonstrated strong analytical and problem-solving capabilities',
            'Proven track record of meeting deadlines and exceeding expectations',
            'Excellent interpersonal and communication skills'
        ];
    }

    private function optimizeForIndustry($cvData)
    {
        // Industry-specific optimizations would go here
        return $cvData;
    }

    private function suggestCertifications($cvData)
    {
        return [
            'Project Management Professional (PMP)',
            'Google Analytics Certified',
            'Microsoft Office Specialist'
        ];
    }

    private function generateCareerRecommendations($cvData)
    {
        return [
            'Consider pursuing leadership roles in your current field',
            'Develop expertise in emerging technologies',
            'Build a strong professional network through industry events'
        ];
    }

    private function generatePDF($cvData, $level)
    {
        $template = $this->selectTemplate($level);

        // Get language-specific labels
        $labels = $this->getLanguageLabels($cvData['cv_language'] ?? 'en');

        $pdf = PDF::loadView('pdf.cv-templates.' . $template, [
            'cv' => $cvData,
            'level' => $level,
            'labels' => $labels,
            'language' => $cvData['cv_language'] ?? 'en'
        ]);

        // Set RTL for Arabic
        if (($cvData['cv_language'] ?? 'en') === 'ar') {
            $pdf->getDomPDF()->getOptions()->set('isRtl', true);
        }

        $fileName = 'cv_' . time() . '_' . $level . '.pdf';
        $filePath = storage_path('app/public/cvs/' . $fileName);

        // Create directory if it doesn't exist
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }

        $pdf->save($filePath);

        return $filePath;
    }

    private function getLanguageLabels($language)
    {
        $labels = [
            'en' => [
                'professional_summary' => 'Professional Summary',
                'work_experience' => 'Work Experience',
                'education' => 'Education',
                'skills' => 'Skills',
                'languages' => 'Languages',
                'achievements' => 'Key Achievements',
                'career_recommendations' => 'Career Development Recommendations',
                'suggested_certifications' => 'Suggested Certifications',
                'generated_by' => 'Generated by MonOri AI',
                'basic_level' => 'Basic AI-Assisted CV',
                'professional_level' => 'Professional AI-Enhanced CV',
                'premium_level' => 'Premium AI-Optimized CV'
            ],
            'ar' => [
                'professional_summary' => 'الملخص المهني',
                'work_experience' => 'الخبرة العملية',
                'education' => 'التعليم',
                'skills' => 'المهارات',
                'languages' => 'اللغات',
                'achievements' => 'الإنجازات الرئيسية',
                'career_recommendations' => 'توصيات التطوير المهني',
                'suggested_certifications' => 'الشهادات المقترحة',
                'generated_by' => 'تم الإنشاء بواسطة MonOri AI',
                'basic_level' => 'سيرة ذاتية أساسية بمساعدة الذكاء الاصطناعي',
                'professional_level' => 'سيرة ذاتية محترفة محسنة بالذكاء الاصطناعي',
                'premium_level' => 'سيرة ذاتية متميزة محسنة بالذكاء الاصطناعي'
            ],
            'fr' => [
                'professional_summary' => 'Résumé Professionnel',
                'work_experience' => 'Expérience Professionnelle',
                'education' => 'Formation',
                'skills' => 'Compétences',
                'languages' => 'Langues',
                'achievements' => 'Réalisations Clés',
                'career_recommendations' => 'Recommandations de Développement de Carrière',
                'suggested_certifications' => 'Certifications Suggérées',
                'generated_by' => 'Généré par MonOri AI',
                'basic_level' => 'CV de Base Assisté par IA',
                'professional_level' => 'CV Professionnel Amélioré par IA',
                'premium_level' => 'CV Premium Optimisé par IA'
            ]
        ];

        return $labels[$language] ?? $labels['en'];
    }

    private function selectTemplate($level)
    {
        $templates = [
            'basic' => 'basic',
            'professional' => 'professional',
            'premium' => 'premium'
        ];

        return $templates[$level] ?? 'basic';
    }

    public function download(Request $request)
    {
        $fileName = $request->get('file');
        $filePath = storage_path('app/public/cvs/' . $fileName);

        if (!file_exists($filePath)) {
            abort(404, 'File not found');
        }

        return response()->download($filePath, $fileName);
    }
}
