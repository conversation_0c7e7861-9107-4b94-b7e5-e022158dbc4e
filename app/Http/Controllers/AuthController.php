<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\UserAction;

class AuthController extends Controller
{
    // Show login form
    public function showLoginForm()
    {
        return view('auth.login');
    }

    // Handle login
    public function login(Request $request)
    {
        $credentials = $request->validate([
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if (Auth::attempt($credentials)) {
            $request->session()->regenerate();

            // Log login activity
            UserAction::logAction(
                Auth::id(),
                'login',
                'تم تسجيل الدخول إلى المنصة',
                ['login_time' => now(), 'ip_address' => $request->ip()]
            );

            return redirect()->intended('/profile')->with('success', __('auth.login_success'));
        }

        return back()->withErrors([
            'email' => __('auth.login_failed'),
        ])->onlyInput('email');
    }

    // Show register form
    public function showRegisterForm()
    {
        return view('auth.register');
    }

    // Handle registration
    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        Auth::login($user);

        return redirect('/profile')->with('success', __('auth.register_success'));
    }

    // Handle logout
    public function logout(Request $request)
    {
        // Log logout activity before logging out
        if (Auth::check()) {
            UserAction::logAction(
                Auth::id(),
                'logout',
                'تم تسجيل الخروج من المنصة',
                ['logout_time' => now(), 'ip_address' => $request->ip()]
            );
        }

        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return redirect('/')->with('success', __('auth.logout_success'));
    }
}
