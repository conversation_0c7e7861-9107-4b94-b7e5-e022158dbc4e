<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\PaymentTransaction;
use App\Models\Subscription;
use App\Models\PaidService;
use App\Models\SiteAnalytics;
use App\Models\UserAction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function dashboard()
    {
        try {
            // إحصائيات المستخدمين
            $users_stats = [
                'total_users' => User::count(),
                'new_users_today' => User::whereDate('created_at', today())->count(),
                'new_users_this_week' => User::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'new_users_this_month' => User::whereMonth('created_at', now()->month)->count(),
                'admin_users' => User::where('is_admin', true)->count(),
                'active_users' => User::where('created_at', '>=', now()->subDays(30))->count(),
            ];

            // إحصائيات الإيرادات
            $revenue_stats = [
                'total_revenue' => PaymentTransaction::where('status', 'completed')->sum('amount') ?? 0,
                'today_revenue' => PaymentTransaction::where('status', 'completed')
                                 ->whereDate('created_at', today())->sum('amount') ?? 0,
                'monthly_revenue' => PaymentTransaction::where('status', 'completed')
                                   ->whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->sum('amount') ?? 0,
                'yearly_revenue' => PaymentTransaction::where('status', 'completed')
                                  ->whereYear('created_at', now()->year)
                                  ->sum('amount') ?? 0,
            ];

            // إحصائيات المعاملات
            $transactions_stats = [
                'total_transactions' => PaymentTransaction::count(),
                'completed_transactions' => PaymentTransaction::where('status', 'completed')->count(),
                'pending_transactions' => PaymentTransaction::where('status', 'pending')->count(),
                'failed_transactions' => PaymentTransaction::where('status', 'failed')->count(),
                'stripe_transactions' => PaymentTransaction::where('payment_method', 'stripe')->count(),
                'paypal_transactions' => PaymentTransaction::where('payment_method', 'paypal')->count(),
            ];

            // إحصائيات الاشتراكات
            $subscriptions_stats = [
                'total_subscriptions' => Subscription::count(),
                'active_subscriptions' => Subscription::where('status', 'active')->count(),
                'expired_subscriptions' => Subscription::where('status', 'expired')->count(),
                'cancelled_subscriptions' => Subscription::where('status', 'cancelled')->count(),
            ];

            // إحصائيات الخدمات
            $services_stats = [
                'total_services' => PaidService::count(),
                'active_services' => PaidService::where('is_active', true)->count(),
                'most_popular_service' => PaidService::withCount('paymentTransactions')
                                        ->orderBy('payment_transactions_count', 'desc')
                                        ->first(),
            ];

            // المستخدمون الجدد (آخر 10)
            $recent_users = User::latest()->take(10)->get();

            // المعاملات الأخيرة (آخر 10)
            $recent_transactions = PaymentTransaction::with(['user', 'paidService'])
                                 ->latest()->take(10)->get();

            // بيانات الرسوم البيانية - نمو المستخدمين (آخر 30 يوم)
            $user_growth_data = [];
            for ($i = 29; $i >= 0; $i--) {
                $date = now()->subDays($i);
                $user_growth_data[] = [
                    'date' => $date->format('M d'),
                    'users' => User::whereDate('created_at', $date)->count(),
                ];
            }

            // بيانات الإيرادات (آخر 12 شهر)
            $revenue_chart_data = [];
            for ($i = 11; $i >= 0; $i--) {
                $date = now()->subMonths($i);
                $revenue_chart_data[] = [
                    'month' => $date->format('M Y'),
                    'revenue' => PaymentTransaction::where('status', 'completed')
                               ->whereYear('created_at', $date->year)
                               ->whereMonth('created_at', $date->month)
                               ->sum('amount'),
                ];
            }

            // إحصائيات الموقع
            $site_stats = [
                'total_page_views' => SiteAnalytics::count(),
                'today_page_views' => SiteAnalytics::whereDate('created_at', today())->count(),
                'unique_visitors' => SiteAnalytics::distinct('ip_address')->count(),
                'bounce_rate' => $this->calculateBounceRate(),
            ];

            return view('admin.dashboard', compact(
                'users_stats',
                'revenue_stats', 
                'transactions_stats',
                'subscriptions_stats',
                'services_stats',
                'recent_users',
                'recent_transactions',
                'user_growth_data',
                'revenue_chart_data',
                'site_stats'
            ));

        } catch (\Exception $e) {
            return view('admin.dashboard')->with('error', 'حدث خطأ في تحميل البيانات: ' . $e->getMessage());
        }
    }

    public function users()
    {
        $users = User::with(['subscriptions', 'paymentTransactions'])
                    ->withCount(['paymentTransactions', 'subscriptions'])
                    ->paginate(20);

        $users_stats = [
            'total_users' => User::count(),
            'admin_users' => User::where('is_admin', true)->count(),
            'verified_users' => User::whereNotNull('email_verified_at')->count(),
            'unverified_users' => User::whereNull('email_verified_at')->count(),
        ];

        return view('admin.users', compact('users', 'users_stats'));
    }

    public function analytics()
    {
        // إحصائيات تفصيلية للموقع
        $analytics_data = [
            'page_views' => SiteAnalytics::count(),
            'unique_visitors' => SiteAnalytics::distinct('ip_address')->count(),
            'top_pages' => SiteAnalytics::select('url', DB::raw('count(*) as views'))
                         ->groupBy('url')
                         ->orderBy('views', 'desc')
                         ->take(10)
                         ->get(),
            'traffic_sources' => SiteAnalytics::select('referrer', DB::raw('count(*) as visits'))
                               ->groupBy('referrer')
                               ->orderBy('visits', 'desc')
                               ->take(10)
                               ->get(),
        ];

        return view('admin.analytics', compact('analytics_data'));
    }

    public function settings()
    {
        return view('admin.settings');
    }

    private function calculateBounceRate()
    {
        // حساب معدل الارتداد (نسبة تقريبية)
        $total_sessions = SiteAnalytics::distinct('ip_address')->count();
        $single_page_sessions = SiteAnalytics::select('ip_address')
                              ->groupBy('ip_address')
                              ->havingRaw('COUNT(*) = 1')
                              ->count();
        
        return $total_sessions > 0 ? round(($single_page_sessions / $total_sessions) * 100, 2) : 0;
    }
}
