<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\PaymentTransaction;
use App\Models\Subscription;
use App\Models\PaidService;
use App\Models\SiteAnalytics;
use App\Models\UserAction;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function dashboard()
    {
        try {
            // Basic stats
            $totalUsers = User::count();
            $activeSubscriptions = Subscription::where('status', 'active')->count();
            $totalRevenue = PaymentTransaction::where('status', 'completed')->sum('amount') ?? 0;
            $servicesUsed = PaidService::count();

            // Recent users
            $recentUsers = User::latest()->take(5)->get();

            // Recent payments
            $recentPayments = PaymentTransaction::with('user')
                ->where('status', 'completed')
                ->latest()
                ->take(5)
                ->get();

            return view('admin.dashboard', compact(
                'totalUsers',
                'activeSubscriptions',
                'totalRevenue',
                'servicesUsed',
                'recentUsers',
                'recentPayments'
            ));

        } catch (\Exception $e) {
            return view('admin.dashboard')->with('error', 'حدث خطأ في تحميل البيانات: ' . $e->getMessage());
        }
    }

    public function users()
    {
        $users = User::latest()->paginate(20);
        return view('admin.users', compact('users'));
    }

    public function analytics()
    {
        $analytics_data = [
            'page_views' => SiteAnalytics::count(),
            'unique_visitors' => SiteAnalytics::count(), // مؤقتاً حتى نضيف العمود
            'top_pages' => [],
            'traffic_sources' => [],
        ];

        return view('admin.analytics', compact('analytics_data'));
    }

    public function subscriptions()
    {
        $subscriptions = Subscription::with('user')->paginate(20);
        return view('admin.subscriptions', compact('subscriptions'));
    }

    public function services()
    {
        $services = PaidService::paginate(20);
        return view('admin.services', compact('services'));
    }

    public function payments()
    {
        $payments = PaymentTransaction::with('user')->latest()->paginate(20);
        return view('admin.payments', compact('payments'));
    }

    public function settings()
    {
        return view('admin.settings');
    }

    public function getDashboardData()
    {
        try {
            // إحصائيات المستخدمين
            $users_stats = [
                'total_users' => User::count(),
                'new_users_today' => User::whereDate('created_at', today())->count(),
                'new_users_this_week' => User::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count(),
                'new_users_this_month' => User::whereMonth('created_at', now()->month)->count(),
                'admin_users' => User::where('is_admin', true)->count(),
                'active_users' => User::where('created_at', '>=', now()->subDays(30))->count(),
            ];

            // إحصائيات الإيرادات
            $revenue_stats = [
                'total_revenue' => PaymentTransaction::where('status', 'completed')->sum('amount') ?? 0,
                'today_revenue' => PaymentTransaction::where('status', 'completed')
                                 ->whereDate('created_at', today())->sum('amount') ?? 0,
                'monthly_revenue' => PaymentTransaction::where('status', 'completed')
                                   ->whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->sum('amount') ?? 0,
                'yearly_revenue' => PaymentTransaction::where('status', 'completed')
                                  ->whereYear('created_at', now()->year)
                                  ->sum('amount') ?? 0,
            ];

            // إحصائيات المعاملات
            $transactions_stats = [
                'total_transactions' => PaymentTransaction::count(),
                'completed_transactions' => PaymentTransaction::where('status', 'completed')->count(),
                'pending_transactions' => PaymentTransaction::where('status', 'pending')->count(),
                'failed_transactions' => PaymentTransaction::where('status', 'failed')->count(),
                'stripe_transactions' => PaymentTransaction::where('payment_method', 'stripe')->count(),
                'paypal_transactions' => PaymentTransaction::where('payment_method', 'paypal')->count(),
            ];

            // إحصائيات الاشتراكات
            $subscriptions_stats = [
                'total_subscriptions' => Subscription::count(),
                'active_subscriptions' => Subscription::where('status', 'active')->count(),
                'expired_subscriptions' => Subscription::where('status', 'expired')->count(),
                'cancelled_subscriptions' => Subscription::where('status', 'cancelled')->count(),
            ];

            // المستخدمون الجدد (آخر 5)
            $recent_users = User::latest()->take(5)->get(['id', 'name', 'email', 'created_at']);

            // المعاملات الأخيرة (آخر 5)
            $recent_transactions = PaymentTransaction::with(['user:id,name', 'paidService:id,name'])
                                 ->latest()->take(5)->get(['id', 'user_id', 'paid_service_id', 'amount', 'status', 'created_at']);

            // بيانات الرسوم البيانية - نمو المستخدمين (آخر 7 أيام)
            $user_growth_data = [];
            for ($i = 6; $i >= 0; $i--) {
                $date = now()->subDays($i);
                $user_growth_data[] = [
                    'date' => $date->format('M d'),
                    'users' => User::whereDate('created_at', $date)->count(),
                ];
            }

            // بيانات الإيرادات (آخر 6 أشهر)
            $revenue_chart_data = [];
            for ($i = 5; $i >= 0; $i--) {
                $date = now()->subMonths($i);
                $revenue_chart_data[] = [
                    'month' => $date->format('M Y'),
                    'revenue' => PaymentTransaction::where('status', 'completed')
                               ->whereYear('created_at', $date->year)
                               ->whereMonth('created_at', $date->month)
                               ->sum('amount'),
                ];
            }

            return response()->json([
                'users_stats' => $users_stats,
                'revenue_stats' => $revenue_stats,
                'transactions_stats' => $transactions_stats,
                'subscriptions_stats' => $subscriptions_stats,
                'recent_users' => $recent_users,
                'recent_transactions' => $recent_transactions,
                'user_growth_data' => $user_growth_data,
                'revenue_chart_data' => $revenue_chart_data,
                'last_updated' => now()->format('H:i:s')
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ في تحميل البيانات'], 500);
        }
    }

    public function getNotifications()
    {
        try {
            // إشعارات المعاملات الجديدة
            $new_transactions = PaymentTransaction::where('created_at', '>=', now()->subHours(24))
                              ->where('status', 'completed')
                              ->with(['user:id,name', 'paidService:id,name'])
                              ->latest()
                              ->take(10)
                              ->get();

            // إشعارات المستخدمين الجدد
            $new_users = User::where('created_at', '>=', now()->subHours(24))
                           ->latest()
                           ->take(5)
                           ->get(['id', 'name', 'email', 'created_at']);

            // إشعارات المعاملات الفاشلة
            $failed_transactions = PaymentTransaction::where('status', 'failed')
                                 ->where('created_at', '>=', now()->subHours(24))
                                 ->with(['user:id,name'])
                                 ->latest()
                                 ->take(5)
                                 ->get();

            return response()->json([
                'new_transactions' => $new_transactions,
                'new_users' => $new_users,
                'failed_transactions' => $failed_transactions,
                'unread_count' => $new_transactions->count() + $new_users->count() + $failed_transactions->count()
            ]);

        } catch (\Exception $e) {
            return response()->json(['error' => 'حدث خطأ في تحميل الإشعارات'], 500);
        }
    }

    private function calculateBounceRate()
    {
        // حساب معدل الارتداد (نسبة تقريبية)
        $total_sessions = SiteAnalytics::distinct('ip_address')->count();
        $single_page_sessions = SiteAnalytics::select('ip_address')
                              ->groupBy('ip_address')
                              ->havingRaw('COUNT(*) = 1')
                              ->count();

        return $total_sessions > 0 ? round(($single_page_sessions / $total_sessions) * 100, 2) : 0;
    }
}
