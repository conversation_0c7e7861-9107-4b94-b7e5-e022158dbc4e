<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\SiteAnalytics;
use App\Models\UserAction;
use App\Models\UserSubscription;
use App\Models\ServiceOrder;
use App\Models\SubscriptionPlan;
use App\Models\PaidService;
use App\Models\Subscription;
use App\Models\Payment;
use App\Models\PaymentTransaction;
use App\Models\AdminNotification;
use App\Models\BlogPost;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class AdminController extends Controller
{
    public function dashboard()
    {
        try {
            // Get user statistics
            $stats = [
                'total_users' => User::count() ?? 0,
                'new_users_today' => User::whereDate('created_at', today())->count() ?? 0,
                'new_users_this_week' => User::whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])->count() ?? 0,
                'new_users_this_month' => User::whereMonth('created_at', now()->month)->whereYear('created_at', now()->year)->count() ?? 0,
                'admin_users' => User::where('is_admin', true)->count() ?? 0,
            ];

        // Get analytics statistics
        $analytics_stats = [
            'total_page_views' => SiteAnalytics::count(),
            'page_views_today' => SiteAnalytics::today()->count(),
            'page_views_this_week' => SiteAnalytics::thisWeek()->count(),
            'page_views_this_month' => SiteAnalytics::thisMonth()->count(),
        ];

        // Get user actions statistics
        $actions_stats = [
            'total_actions' => UserAction::count(),
            'actions_today' => UserAction::today()->count(),
            'logins_today' => UserAction::today()->byAction('login')->count(),
            'tests_completed' => UserAction::byAction('test_completed')->count(),
        ];

        // Get recent users
        $recent_users = User::with('actions')->latest()->take(10)->get();

        // Get user registration chart data (last 7 days)
        $chart_data = [];
        for ($i = 6; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $chart_data[] = [
                'date' => $date->format('M d'),
                'users' => User::whereDate('created_at', $date)->count(),
                'page_views' => SiteAnalytics::whereDate('created_at', $date)->count(),
                'actions' => UserAction::whereDate('created_at', $date)->count(),
            ];
        }

        // Get top pages
        $top_pages = SiteAnalytics::selectRaw('page_url, count(*) as views')
            ->groupBy('page_url')
            ->orderBy('views', 'desc')
            ->take(5)
            ->get();

        // Get device statistics
        $device_stats = SiteAnalytics::selectRaw('device_type, count(*) as count')
            ->groupBy('device_type')
            ->get()
            ->pluck('count', 'device_type')
            ->toArray();

        // Get recent actions
        $recent_actions = UserAction::with('user')
            ->latest()
            ->take(10)
            ->get();

        // Get revenue statistics (updated for new payment system)
        $revenue_stats = [
            'total_revenue' => PaymentTransaction::where('status', 'completed')->sum('amount') ?? 0,
            'today_revenue' => PaymentTransaction::where('status', 'completed')
                              ->whereDate('created_at', today())->sum('amount') ?? 0,
            'monthly_revenue' => PaymentTransaction::where('status', 'completed')
                                ->whereMonth('created_at', now()->month)
                                ->whereYear('created_at', now()->year)
                                ->sum('amount') ?? 0,
            'services_revenue' => PaymentTransaction::where('status', 'completed')->sum('amount') ?? 0,
            'completed_transactions' => PaymentTransaction::where('status', 'completed')->count() ?? 0,
            'pending_transactions' => PaymentTransaction::where('status', 'pending')->count() ?? 0,
            'failed_transactions' => PaymentTransaction::where('status', 'failed')->count() ?? 0,
            'total_transactions' => PaymentTransaction::count() ?? 0,
            'stripe_transactions' => PaymentTransaction::where('payment_method', 'stripe')->count() ?? 0,
            'paypal_transactions' => PaymentTransaction::where('payment_method', 'paypal')->count() ?? 0,
        ];

        // Get paid services stats
        $services_stats = PaidService::withCount(['paymentTransactions' => function($query) {
            $query->where('status', 'completed');
        }])->get();

        // Get recent transactions
        $recent_transactions = PaymentTransaction::with(['user', 'paidService'])
            ->latest()
            ->take(10)
            ->get();

        // Get admin notifications
        $admin_notifications = AdminNotification::latest()
            ->take(5)
            ->get();

        // Get blog statistics
        $blog_stats = [
            'total_posts' => BlogPost::count() ?? 0,
            'published_posts' => BlogPost::where('status', 'published')->count() ?? 0,
            'draft_posts' => BlogPost::where('status', 'draft')->count() ?? 0,
            'total_views' => BlogPost::sum('views_count') ?? 0,
            'popular_posts' => BlogPost::orderBy('views_count', 'desc')->take(5)->get() ?? collect(),
        ];

        // Get payment method distribution
        $payment_methods = Payment::where('status', 'completed')
            ->selectRaw('payment_method, count(*) as count, sum(amount) as total')
            ->groupBy('payment_method')
            ->get();

        // Get monthly revenue chart (last 12 months)
        $monthly_revenue = [];
        for ($i = 11; $i >= 0; $i--) {
            $date = now()->subMonths($i);
            $monthly_revenue[] = [
                'month' => $date->format('M Y'),
                'revenue' => Payment::where('status', 'completed')
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->sum('amount'),
                'subscriptions' => Subscription::whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
                'payments' => Payment::where('status', 'completed')
                    ->whereYear('created_at', $date->year)
                    ->whereMonth('created_at', $date->month)
                    ->count(),
            ];
        }

        // Get user growth data (last 30 days)
        $user_growth = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = now()->subDays($i);
            $user_growth[] = [
                'date' => $date->format('M d'),
                'users' => User::whereDate('created_at', $date)->count(),
                'subscriptions' => Subscription::whereDate('created_at', $date)->count(),
                'payments' => Payment::where('status', 'completed')->whereDate('created_at', $date)->count(),
            ];
        }

            return view('admin.dashboard', compact(
                'stats',
                'analytics_stats',
                'actions_stats',
                'recent_users',
                'chart_data',
                'top_pages',
                'device_stats',
                'recent_actions',
                'revenue_stats',
                'services_stats',
                'recent_transactions',
                'admin_notifications',
                'blog_stats',
                'payment_methods',
                'monthly_revenue',
                'user_growth'
            ));
        } catch (\Exception $e) {
            // Return with default data if there's an error
            return view('admin.dashboard', [
                'stats' => ['total_users' => 0, 'new_users_today' => 0, 'new_users_this_week' => 0, 'new_users_this_month' => 0, 'admin_users' => 0],
                'analytics_stats' => ['total_page_views' => 0, 'page_views_today' => 0, 'page_views_this_week' => 0, 'page_views_this_month' => 0],
                'actions_stats' => ['total_actions' => 0, 'actions_today' => 0, 'logins_today' => 0, 'tests_completed' => 0],
                'recent_users' => collect(),
                'chart_data' => [],
                'top_pages' => collect(),
                'device_stats' => [],
                'recent_actions' => collect(),
                'revenue_stats' => ['total_revenue' => 0, 'active_subscriptions' => 0, 'pending_payments' => 0, 'failed_payments' => 0, 'total_payments' => 0],
                'subscription_stats' => collect(),
                'blog_stats' => ['total_posts' => 0, 'published_posts' => 0, 'draft_posts' => 0, 'total_views' => 0],
                'payment_methods' => collect(),
                'monthly_revenue' => [],
                'user_growth' => []
            ]);
        }
    }

    public function users()
    {
        $users = User::latest()->paginate(20);
        return view('admin.users.index', compact('users'));
    }

    public function settings()
    {
        return view('admin.settings');
    }

    public function analytics()
    {
        // Get monthly user registrations
        $monthly_data = User::select(
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as count')
        )
        ->whereYear('created_at', now()->year)
        ->groupBy('month')
        ->get();

        return view('admin.analytics', compact('monthly_data'));
    }
}
