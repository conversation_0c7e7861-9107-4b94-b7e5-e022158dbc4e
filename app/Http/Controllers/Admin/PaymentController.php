<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PaymentTransaction;
use App\Models\AdminNotification;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PaymentController extends Controller
{
    protected $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
        $this->middleware(['auth', 'admin']);
    }

    /**
     * Display payment dashboard
     */
    public function index(Request $request)
    {
        $query = PaymentTransaction::with(['user', 'paidService']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by payment method
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Search by transaction ID or customer name
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('transaction_id', 'like', "%{$search}%")
                  ->orWhere('customer_name', 'like', "%{$search}%")
                  ->orWhere('customer_email', 'like', "%{$search}%");
            });
        }

        $transactions = $query->orderBy('created_at', 'desc')->paginate(20);

        // Get statistics
        $stats = $this->getPaymentStats();

        // Get recent notifications
        $notifications = AdminNotification::where('type', 'payment_received')
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();

        return view('admin.payments.index', compact('transactions', 'stats', 'notifications'));
    }

    /**
     * Show payment details
     */
    public function show(PaymentTransaction $transaction)
    {
        $transaction->load(['user', 'paidService']);
        
        return view('admin.payments.show', compact('transaction'));
    }

    /**
     * Get payment statistics
     */
    private function getPaymentStats()
    {
        $today = now()->startOfDay();
        $thisMonth = now()->startOfMonth();
        $thisYear = now()->startOfYear();

        return [
            'total_transactions' => PaymentTransaction::count(),
            'completed_transactions' => PaymentTransaction::where('status', 'completed')->count(),
            'pending_transactions' => PaymentTransaction::where('status', 'pending')->count(),
            'failed_transactions' => PaymentTransaction::where('status', 'failed')->count(),
            
            'total_revenue' => PaymentTransaction::where('status', 'completed')->sum('amount'),
            'today_revenue' => PaymentTransaction::where('status', 'completed')
                ->whereDate('paid_at', $today)
                ->sum('amount'),
            'month_revenue' => PaymentTransaction::where('status', 'completed')
                ->whereDate('paid_at', '>=', $thisMonth)
                ->sum('amount'),
            'year_revenue' => PaymentTransaction::where('status', 'completed')
                ->whereDate('paid_at', '>=', $thisYear)
                ->sum('amount'),
            
            'stripe_transactions' => PaymentTransaction::where('payment_method', 'stripe')->count(),
            'paypal_transactions' => PaymentTransaction::where('payment_method', 'paypal')->count(),
            
            'success_rate' => $this->calculateSuccessRate(),
            'average_transaction_amount' => PaymentTransaction::where('status', 'completed')
                ->avg('amount'),
        ];
    }

    /**
     * Calculate payment success rate
     */
    private function calculateSuccessRate()
    {
        $total = PaymentTransaction::whereIn('status', ['completed', 'failed'])->count();
        $successful = PaymentTransaction::where('status', 'completed')->count();
        
        return $total > 0 ? round(($successful / $total) * 100, 2) : 0;
    }

    /**
     * Get payment analytics data
     */
    public function analytics(Request $request)
    {
        $period = $request->get('period', '30'); // days
        $startDate = now()->subDays($period);

        // Daily revenue chart data
        $dailyRevenue = PaymentTransaction::select(
                DB::raw('DATE(paid_at) as date'),
                DB::raw('SUM(amount) as revenue'),
                DB::raw('COUNT(*) as transactions')
            )
            ->where('status', 'completed')
            ->where('paid_at', '>=', $startDate)
            ->groupBy('date')
            ->orderBy('date')
            ->get();

        // Payment method distribution
        $paymentMethods = PaymentTransaction::select(
                'payment_method',
                DB::raw('COUNT(*) as count'),
                DB::raw('SUM(amount) as total_amount')
            )
            ->where('status', 'completed')
            ->where('paid_at', '>=', $startDate)
            ->groupBy('payment_method')
            ->get();

        // Top services by revenue
        $topServices = PaymentTransaction::select(
                'paid_services.name',
                DB::raw('COUNT(payment_transactions.id) as transactions'),
                DB::raw('SUM(payment_transactions.amount) as revenue')
            )
            ->join('paid_services', 'payment_transactions.paid_service_id', '=', 'paid_services.id')
            ->where('payment_transactions.status', 'completed')
            ->where('payment_transactions.paid_at', '>=', $startDate)
            ->groupBy('paid_services.id', 'paid_services.name')
            ->orderBy('revenue', 'desc')
            ->limit(10)
            ->get();

        // Status distribution
        $statusDistribution = PaymentTransaction::select(
                'status',
                DB::raw('COUNT(*) as count')
            )
            ->where('created_at', '>=', $startDate)
            ->groupBy('status')
            ->get();

        return response()->json([
            'daily_revenue' => $dailyRevenue,
            'payment_methods' => $paymentMethods,
            'top_services' => $topServices,
            'status_distribution' => $statusDistribution,
        ]);
    }

    /**
     * Export transactions
     */
    public function export(Request $request)
    {
        $query = PaymentTransaction::with(['user', 'paidService']);

        // Apply same filters as index
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }
        if ($request->filled('payment_method')) {
            $query->where('payment_method', $request->payment_method);
        }
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        $transactions = $query->orderBy('created_at', 'desc')->get();

        $csvData = [];
        $csvData[] = [
            'Transaction ID',
            'Customer Name',
            'Customer Email',
            'Service',
            'Amount',
            'Currency',
            'Payment Method',
            'Status',
            'Created At',
            'Paid At',
            'Failure Reason'
        ];

        foreach ($transactions as $transaction) {
            $csvData[] = [
                $transaction->transaction_id,
                $transaction->customer_name,
                $transaction->customer_email,
                $transaction->paidService->name,
                $transaction->amount,
                $transaction->currency,
                $transaction->payment_method,
                $transaction->status,
                $transaction->created_at->format('Y-m-d H:i:s'),
                $transaction->paid_at?->format('Y-m-d H:i:s') ?? '',
                $transaction->failure_reason ?? ''
            ];
        }

        $filename = 'payment_transactions_' . now()->format('Y_m_d_H_i_s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($csvData) {
            $file = fopen('php://output', 'w');
            foreach ($csvData as $row) {
                fputcsv($file, $row);
            }
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Mark notifications as read
     */
    public function markNotificationsRead(Request $request)
    {
        $notificationIds = $request->get('notification_ids', []);
        
        AdminNotification::whereIn('id', $notificationIds)
            ->update(['is_read' => true, 'read_at' => now()]);

        return response()->json(['success' => true]);
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadNotificationsCount()
    {
        $count = AdminNotification::where('type', 'payment_received')
            ->where('is_read', false)
            ->count();

        return response()->json(['count' => $count]);
    }
}
