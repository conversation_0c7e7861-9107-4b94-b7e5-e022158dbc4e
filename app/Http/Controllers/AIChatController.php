<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use OpenAI\Laravel\Facades\OpenAI;

class AIChatController extends Controller
{
    /**
     * Handle AI chat messages
     */
    public function chat(Request $request): JsonResponse
    {
        try {
            $message = $request->input('message');
            $language = $request->input('language', 'ar');
            
            if (empty($message)) {
                return response()->json([
                    'error' => 'Message is required'
                ], 400);
            }

            // Get AI response
            $response = $this->getAIResponse($message, $language);
            
            return response()->json([
                'success' => true,
                'response' => $response,
                'language' => $language
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Failed to get AI response: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get AI response from OpenAI
     */
    private function getAIResponse(string $message, string $language): string
    {
        // Website knowledge base
        $websiteInfo = $this->getWebsiteKnowledge($language);
        
        // Create system prompt based on language
        $systemPrompt = $this->getSystemPrompt($language, $websiteInfo);
        
        try {
            $result = OpenAI::chat()->create([
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => $systemPrompt
                    ],
                    [
                        'role' => 'user',
                        'content' => $message
                    ]
                ],
                'max_tokens' => 500,
                'temperature' => 0.7,
            ]);

            return $result->choices[0]->message->content ?? 'عذراً، لم أتمكن من فهم سؤالك. يرجى المحاولة مرة أخرى.';
            
        } catch (\Exception $e) {
            // Fallback to local responses if OpenAI fails
            return $this->getFallbackResponse($message, $language);
        }
    }

    /**
     * Get system prompt based on language
     */
    private function getSystemPrompt(string $language, array $websiteInfo): string
    {
        $prompts = [
            'ar' => "أنت مساعد ذكي لموقع MonOri AI. أنت خبير في التوجيه المهني والذكاء الاصطناعي. 

معلومات الموقع:
{$websiteInfo['ar']}

تعليمات:
- أجب باللغة العربية فقط
- كن ودوداً ومفيداً
- إذا سأل المستخدم بالدارجة المغربية، افهمها ولكن أجب بالعربية الفصحى
- قدم معلومات دقيقة عن خدماتنا وأسعارنا
- ساعد في التنقل في الموقع
- إذا لم تعرف الإجابة، اعترف بذلك واقترح التواصل مع الدعم

أسلوبك: ودود، مهني، مفيد",

            'en' => "You are an AI assistant for MonOri AI website. You are an expert in career guidance and artificial intelligence.

Website Information:
{$websiteInfo['en']}

Instructions:
- Answer in English only
- Be friendly and helpful
- Provide accurate information about our services and pricing
- Help with website navigation
- If you don't know the answer, admit it and suggest contacting support

Your style: Friendly, professional, helpful",

            'fr' => "Vous êtes un assistant IA pour le site MonOri AI. Vous êtes un expert en orientation professionnelle et intelligence artificielle.

Informations du site:
{$websiteInfo['fr']}

Instructions:
- Répondez en français uniquement
- Soyez amical et utile
- Fournissez des informations précises sur nos services et tarifs
- Aidez à la navigation du site
- Si vous ne connaissez pas la réponse, admettez-le et suggérez de contacter le support

Votre style: Amical, professionnel, utile"
        ];

        return $prompts[$language] ?? $prompts['ar'];
    }

    /**
     * Get website knowledge base
     */
    private function getWebsiteKnowledge(string $language): array
    {
        return [
            'ar' => "
موقع MonOri AI - منصة التوجيه المهني بالذكاء الاصطناعي

الخدمات:
1. تحليل الشخصية:
   - أساسي: 19 درهم (15-20 دقيقة)
   - متقدم: 39 درهم (25-30 دقيقة) - الأكثر شعبية
   - احترافي: 69 درهم (35-40 دقيقة)

2. تحسين السيرة الذاتية:
   - أساسي: 25 درهم (10-15 دقيقة)
   - متقدم: 49 درهم (20-25 دقيقة) - الأكثر شعبية
   - احترافي: 79 درهم (30-35 دقيقة)

3. محاكاة المقابلات:
   - أساسي: 29 درهم (20-25 دقيقة)
   - متقدم: 55 درهم (30-35 دقيقة) - الأكثر شعبية
   - احترافي: 89 درهم (40-45 دقيقة)

خطط الاشتراك:
- أساسي: 19 درهم/شهر
- ذهبي: 49 درهم/شهر
- متميز: 99 درهم/شهر

الصفحات:
- الرئيسية: /
- الخدمات: /services
- الأسعار: /pricing
- حولنا: /about
- اتصل بنا: /contact

طرق الدفع: بطاقات الائتمان، PayPal
اللغات المدعومة: العربية، الإنجليزية، الفرنسية
",

            'en' => "
MonOri AI Website - AI-Powered Career Guidance Platform

Services:
1. Personality Analysis:
   - Basic: 19 DH (15-20 minutes)
   - Premium: 39 DH (25-30 minutes) - Most Popular
   - Professional: 69 DH (35-40 minutes)

2. CV Improvement:
   - Basic: 25 DH (10-15 minutes)
   - Premium: 49 DH (20-25 minutes) - Most Popular
   - Professional: 79 DH (30-35 minutes)

3. Interview Simulation:
   - Basic: 29 DH (20-25 minutes)
   - Premium: 55 DH (30-35 minutes) - Most Popular
   - Professional: 89 DH (40-45 minutes)

Subscription Plans:
- Basic: 19 DH/month
- Gold: 49 DH/month
- Premium: 99 DH/month

Pages:
- Home: /
- Services: /services
- Pricing: /pricing
- About: /about
- Contact: /contact

Payment Methods: Credit Cards, PayPal
Supported Languages: Arabic, English, French
",

            'fr' => "
Site MonOri AI - Plateforme d'Orientation Professionnelle par IA

Services:
1. Analyse de Personnalité:
   - Basique: 19 DH (15-20 minutes)
   - Premium: 39 DH (25-30 minutes) - Le Plus Populaire
   - Professionnel: 69 DH (35-40 minutes)

2. Amélioration de CV:
   - Basique: 25 DH (10-15 minutes)
   - Premium: 49 DH (20-25 minutes) - Le Plus Populaire
   - Professionnel: 79 DH (30-35 minutes)

3. Simulation d'Entretien:
   - Basique: 29 DH (20-25 minutes)
   - Premium: 55 DH (30-35 minutes) - Le Plus Populaire
   - Professionnel: 89 DH (40-45 minutes)

Plans d'Abonnement:
- Basique: 19 DH/mois
- Or: 49 DH/mois
- Premium: 99 DH/mois

Pages:
- Accueil: /
- Services: /services
- Tarifs: /pricing
- À propos: /about
- Contact: /contact

Méthodes de Paiement: Cartes de Crédit, PayPal
Langues Supportées: Arabe, Anglais, Français
"
        ];
    }

    /**
     * Fallback response when OpenAI is not available
     */
    private function getFallbackResponse(string $message, string $language): string
    {
        $message = strtolower($message);
        
        // Simple keyword matching for fallback
        if (strpos($message, 'خدمات') !== false || strpos($message, 'services') !== false) {
            return $language === 'ar' ? 
                'نحن نقدم ثلاث خدمات رئيسية: تحليل الشخصية، تحسين السيرة الذاتية، ومحاكاة المقابلات. كل خدمة لها ثلاثة مستويات بأسعار مختلفة.' :
                'We offer three main services: Personality Analysis, CV Improvement, and Interview Simulation. Each service has three levels with different prices.';
        }
        
        if (strpos($message, 'أسعار') !== false || strpos($message, 'price') !== false) {
            return $language === 'ar' ? 
                'أسعارنا تبدأ من 19 درهم للخدمات الأساسية وتصل إلى 89 درهم للخدمات الاحترافية.' :
                'Our prices start from 19 DH for basic services and go up to 89 DH for professional services.';
        }
        
        return $language === 'ar' ? 
            'شكراً لسؤالك! يمكنني مساعدتك في معرفة المزيد عن خدماتنا وأسعارنا. ما الذي تريد معرفته؟' :
            'Thank you for your question! I can help you learn more about our services and pricing. What would you like to know?';
    }
}
