<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;

class LanguageController extends Controller
{
    public function setLanguage($locale)
    {
        $supportedLocales = ['ar', 'en', 'fr'];

        if (in_array($locale, $supportedLocales)) {
            // Store the selected language in session
            Session::put('locale', $locale);
            app()->setLocale($locale);

            // Flash message to confirm language change
            Session::flash('language_changed', true);
        }

        return redirect()->back();
    }
}
