<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\CVGeneration;
use Barryvdh\DomPDF\Facade\Pdf;

class CVBuilderController extends Controller
{
    public function index()
    {
        return view('services.free.cv-improvement');
    }

    public function generateCV(Request $request)
    {
        Log::info('CV Generation started', ['request_data' => $request->all()]);

        try {
            // Validate the request
            $validated = $request->validate([
                'full_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'address' => 'nullable|string|max:500',
                'professional_summary' => 'nullable|string|max:1000',
                'experience' => 'nullable|array',
                'experience.*.job_title' => 'required_with:experience|string|max:255',
                'experience.*.company' => 'required_with:experience|string|max:255',
                'experience.*.start_date' => 'required_with:experience|string|max:50',
                'experience.*.end_date' => 'required_with:experience|string|max:50',
                'experience.*.description' => 'nullable|string|max:1000',
                'education' => 'nullable|array',
                'education.*.degree' => 'required_with:education|string|max:255',
                'education.*.institution' => 'required_with:education|string|max:255',
                'education.*.year' => 'required_with:education|string|max:10',
                'skills' => 'nullable|array',
                'languages' => 'nullable|array',
                'languages.*.language' => 'required_with:languages|string|max:100',
                'languages.*.level' => 'required_with:languages|string|max:50',
                'cv_language' => 'required|in:en,ar,fr'
            ]);

            // Generate AI-enhanced CV content
            $enhancedContent = $this->enhanceWithAI($validated);

            // Save to database
            $cvGeneration = CVGeneration::create([
                'user_id' => Auth::id() ?? 1, // Use guest user ID if not authenticated
                'original_data' => json_encode($validated),
                'enhanced_data' => json_encode($enhancedContent),
                'cv_language' => $validated['cv_language'],
                'status' => 'completed'
            ]);

            // Generate PDF
            Log::info('Generating PDF', ['cv_id' => $cvGeneration->id]);
            $pdfFilename = $this->generatePDF($enhancedContent, $validated['cv_language']);
            Log::info('PDF generated', ['filename' => $pdfFilename]);

            // Update CV generation record with PDF path
            $cvGeneration->update(['pdf_path' => $pdfFilename]);

            $downloadUrl = route('cv.download', $cvGeneration->id);
            Log::info('Sending response', ['download_url' => $downloadUrl]);

            return response()->json([
                'success' => true,
                'message' => 'CV generated successfully with AI enhancement!',
                'cv_id' => $cvGeneration->id,
                'download_url' => $downloadUrl,
                'enhanced_content' => $enhancedContent
            ]);

        } catch (\Exception $e) {
            Log::error('CV Generation Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate CV. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function enhanceWithAI($data)
    {
        $apiKey = config('services.openai.api_key');

        // If no API key or demo key, use basic enhancement
        if (!$apiKey || strpos($apiKey, 'your-') !== false || strpos($apiKey, 'demo') !== false) {
            Log::info('Using basic enhancement - no valid OpenAI API key');
            return $this->basicEnhancement($data);
        }

        // Prepare the prompt based on CV language
        $languagePrompts = [
            'en' => 'English',
            'ar' => 'Arabic',
            'fr' => 'French'
        ];

        $language = $languagePrompts[$data['cv_language']] ?? 'English';
        
        $prompt = $this->buildAIPrompt($data, $language);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post('https://api.openai.com/v1/chat/completions', [
                'model' => config('services.openai.model', 'gpt-3.5-turbo'),
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => "You are a professional CV writer and career consultant. Your task is to enhance CV content to make it more professional, impactful, and ATS-friendly. Always respond in valid JSON format."
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => config('services.openai.max_tokens', 2000),
                'temperature' => config('services.openai.temperature', 0.7),
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $enhancedContent = json_decode($result['choices'][0]['message']['content'], true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON response from AI');
                }

                return $enhancedContent;
            } else {
                throw new \Exception('OpenAI API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('AI Enhancement Error: ' . $e->getMessage());
            
            // Fallback: return original data with basic enhancements
            return $this->basicEnhancement($data);
        }
    }

    private function buildAIPrompt($data, $language)
    {
        $prompt = "Please enhance the following CV data to make it more professional and impactful. ";
        $prompt .= "The CV should be in {$language}. ";
        $prompt .= "Improve the professional summary, job descriptions, and make the content more ATS-friendly. ";
        $prompt .= "Return the response in JSON format with the same structure as the input data.\n\n";
        $prompt .= "Input CV Data:\n" . json_encode($data, JSON_PRETTY_PRINT);
        $prompt .= "\n\nPlease enhance:\n";
        $prompt .= "1. Professional summary - make it more compelling and keyword-rich\n";
        $prompt .= "2. Job descriptions - add action verbs and quantifiable achievements\n";
        $prompt .= "3. Skills - organize and prioritize them\n";
        $prompt .= "4. Overall content - ensure it's professional and ATS-optimized\n";
        $prompt .= "\nReturn only valid JSON with the enhanced data.";

        return $prompt;
    }

    private function basicEnhancement($data)
    {
        // Enhanced AI-like enhancement with rich content generation

        // Enhance professional summary with rich content
        if (isset($data['professional_summary']) && !empty($data['professional_summary'])) {
            $summary = trim($data['professional_summary']);
            $language = $data['cv_language'] ?? 'en';

            // Generate rich professional summary based on language
            $data['professional_summary'] = $this->generateRichSummary($summary, $language, $data);
        }

        // Enhance experience descriptions with rich content
        if (isset($data['experience']) && is_array($data['experience'])) {
            foreach ($data['experience'] as &$exp) {
                if (isset($exp['description']) && !empty($exp['description'])) {
                    $exp['description'] = $this->generateRichExperienceDescription(
                        $exp['description'],
                        $exp['job_title'] ?? '',
                        $exp['company'] ?? '',
                        $data['cv_language'] ?? 'en'
                    );
                }
            }
        }

        // Enhance skills organization
        if (isset($data['skills']) && is_array($data['skills'])) {
            // Remove empty skills and trim
            $data['skills'] = array_filter(array_map('trim', $data['skills']));

            // Sort skills alphabetically
            sort($data['skills']);
        }

        // Add professional touch to education
        if (isset($data['education']) && is_array($data['education'])) {
            foreach ($data['education'] as &$edu) {
                if (isset($edu['degree']) && !empty($edu['degree'])) {
                    // Ensure degree is properly capitalized
                    $edu['degree'] = ucwords(strtolower($edu['degree']));
                }
                if (isset($edu['institution']) && !empty($edu['institution'])) {
                    // Ensure institution is properly capitalized
                    $edu['institution'] = ucwords(strtolower($edu['institution']));
                }
            }
        }

        // Add additional professional sections
        $data['key_achievements'] = $this->generateKeyAchievements($data);
        $data['core_competencies'] = $this->generateCoreCompetencies($data);

        return $data;
    }

    private function generateRichSummary($originalSummary, $language, $fullData)
    {
        // Extract key information for enhancement
        $name = $fullData['full_name'] ?? 'Professional';
        $skills = $fullData['skills'] ?? [];
        $experience = $fullData['experience'] ?? [];
        $education = $fullData['education'] ?? [];

        // Count years of experience
        $experienceYears = count($experience);

        // Get primary skills (first 3)
        $primarySkills = is_array($skills) ? array_slice($skills, 0, 3) : [];

        // Generate enhanced summary based on language
        switch ($language) {
            case 'ar':
                return $this->generateArabicSummary($originalSummary, $experienceYears, $primarySkills, $education);
            case 'fr':
                return $this->generateFrenchSummary($originalSummary, $experienceYears, $primarySkills, $education);
            default:
                return $this->generateEnglishSummary($originalSummary, $experienceYears, $primarySkills, $education);
        }
    }

    private function generateEnglishSummary($original, $years, $skills, $education)
    {
        $templates = [
            "Dynamic and results-driven professional with {$years}+ years of experience in {$original}. Proven expertise in " . implode(', ', array_slice($skills, 0, 3)) . ". Demonstrated ability to deliver high-quality solutions while maintaining strong attention to detail and meeting tight deadlines. Passionate about continuous learning and staying current with industry best practices.",

            "Highly motivated {$original} professional with a strong foundation in " . implode(', ', array_slice($skills, 0, 3)) . ". Experienced in developing innovative solutions and collaborating effectively with cross-functional teams. Committed to excellence and continuous improvement in all aspects of work.",

            "Accomplished professional specializing in {$original} with extensive experience in " . implode(', ', array_slice($skills, 0, 3)) . ". Known for strong analytical skills, creative problem-solving abilities, and the capacity to work efficiently under pressure. Dedicated to delivering exceptional results and contributing to organizational success."
        ];

        return $templates[array_rand($templates)];
    }

    private function generateFrenchSummary($original, $years, $skills, $education)
    {
        $templates = [
            "Professionnel dynamique et axé sur les résultats avec {$years}+ années d'expérience en {$original}. Expertise prouvée en " . implode(', ', array_slice($skills, 0, 3)) . ". Capacité démontrée à fournir des solutions de haute qualité tout en maintenant une attention particulière aux détails et en respectant les délais serrés.",

            "Professionnel hautement motivé en {$original} avec une solide base en " . implode(', ', array_slice($skills, 0, 3)) . ". Expérimenté dans le développement de solutions innovantes et la collaboration efficace avec des équipes transfonctionnelles.",

            "Professionnel accompli spécialisé en {$original} avec une vaste expérience en " . implode(', ', array_slice($skills, 0, 3)) . ". Reconnu pour ses solides compétences analytiques et sa capacité à résoudre les problèmes de manière créative."
        ];

        return $templates[array_rand($templates)];
    }

    private function generateArabicSummary($original, $years, $skills, $education)
    {
        $templates = [
            "محترف ديناميكي ومتميز مع خبرة {$years}+ سنوات في {$original}. خبرة مثبتة في " . implode('، ', array_slice($skills, 0, 3)) . ". قدرة مثبتة على تقديم حلول عالية الجودة مع الحفاظ على الاهتمام القوي بالتفاصيل والوفاء بالمواعيد النهائية الضيقة.",

            "محترف متحفز للغاية في {$original} مع أساس قوي في " . implode('، ', array_slice($skills, 0, 3)) . ". خبرة في تطوير حلول مبتكرة والتعاون الفعال مع الفرق متعددة الوظائف.",

            "محترف متميز متخصص في {$original} مع خبرة واسعة في " . implode('، ', array_slice($skills, 0, 3)) . ". معروف بمهارات تحليلية قوية وقدرات حل المشاكل الإبداعية والقدرة على العمل بكفاءة تحت الضغط."
        ];

        return $templates[array_rand($templates)];
    }

    private function generatePDF($content, $language)
    {
        Log::info('generatePDF called', ['language' => $language]);

        $isRTL = $language === 'ar';

        try {
            $pdf = Pdf::loadView('cv.template', [
                'content' => $content,
                'language' => $language,
                'isRTL' => $isRTL
            ]);

            Log::info('PDF view loaded successfully');
        } catch (\Exception $e) {
            Log::error('PDF view loading failed', ['error' => $e->getMessage()]);
            throw $e;
        }

        if ($isRTL) {
            $pdf->setOption('isHtml5ParserEnabled', true);
            $pdf->setOption('isRemoteEnabled', true);
        }

        $filename = 'cv_' . time() . '.pdf';
        $path = storage_path('app/public/cvs/' . $filename);

        Log::info('Saving PDF', ['path' => $path]);

        // Create directory if it doesn't exist
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
            Log::info('Created directory', ['dir' => dirname($path)]);
        }

        try {
            $pdf->save($path);
            Log::info('PDF saved successfully', ['filename' => $filename]);

            // Verify file was created
            if (file_exists($path)) {
                Log::info('PDF file verified', ['size' => filesize($path)]);
            } else {
                Log::error('PDF file not found after save');
            }
        } catch (\Exception $e) {
            Log::error('PDF save failed', ['error' => $e->getMessage()]);
            throw $e;
        }

        return $filename;
    }

    public function downloadCV($id)
    {
        Log::info('Download CV requested', ['id' => $id]);

        $cvGeneration = CVGeneration::where('id', $id)
            ->firstOrFail(); // Remove user_id check for testing

        Log::info('CV Generation found', ['cv_id' => $cvGeneration->id, 'pdf_path' => $cvGeneration->pdf_path]);

        // Check if PDF already exists
        if ($cvGeneration->pdf_path && file_exists(storage_path('app/public/cvs/' . $cvGeneration->pdf_path))) {
            $path = storage_path('app/public/cvs/' . $cvGeneration->pdf_path);
        } else {
            // Generate new PDF if not exists
            $content = json_decode($cvGeneration->enhanced_data, true);
            $language = $cvGeneration->cv_language;

            $filename = $this->generatePDF($content, $language);
            $cvGeneration->update(['pdf_path' => $filename]);
            $path = storage_path('app/public/cvs/' . $filename);
        }

        // Generate a user-friendly filename
        $userName = $content['full_name'] ?? 'CV';
        $userName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $userName);
        $downloadName = $userName . '_CV.pdf';

        return response()->download($path, $downloadName);
    }

    private function generateRichExperienceDescription($original, $jobTitle, $company, $language)
    {
        $original = trim($original);

        // Generate multiple bullet points based on the original description
        switch ($language) {
            case 'ar':
                return $this->generateArabicExperience($original, $jobTitle, $company);
            case 'fr':
                return $this->generateFrenchExperience($original, $jobTitle, $company);
            default:
                return $this->generateEnglishExperience($original, $jobTitle, $company);
        }
    }

    private function generateEnglishExperience($original, $jobTitle, $company)
    {
        $actionVerbs = ['Developed', 'Implemented', 'Managed', 'Led', 'Created', 'Designed', 'Optimized', 'Coordinated', 'Executed', 'Delivered'];
        $achievements = [
            'resulting in improved efficiency and productivity',
            'leading to enhanced user experience and satisfaction',
            'contributing to significant cost reduction and process optimization',
            'achieving measurable improvements in performance metrics',
            'resulting in streamlined workflows and better collaboration'
        ];

        $responsibilities = [
            'Collaborated with cross-functional teams to ensure project success',
            'Maintained high standards of quality and attention to detail',
            'Provided technical guidance and mentorship to team members',
            'Participated in strategic planning and decision-making processes',
            'Ensured compliance with industry standards and best practices'
        ];

        $verb = $actionVerbs[array_rand($actionVerbs)];
        $achievement = $achievements[array_rand($achievements)];
        $responsibility = $responsibilities[array_rand($responsibilities)];

        return "• {$verb} {$original}, {$achievement}.\n• {$responsibility}.\n• Consistently met project deadlines while maintaining exceptional quality standards.";
    }

    private function generateFrenchExperience($original, $jobTitle, $company)
    {
        $actionVerbs = ['Développé', 'Implémenté', 'Géré', 'Dirigé', 'Créé', 'Conçu', 'Optimisé', 'Coordonné', 'Exécuté', 'Livré'];
        $achievements = [
            'résultant en une amélioration de l\'efficacité et de la productivité',
            'conduisant à une expérience utilisateur améliorée et à la satisfaction',
            'contribuant à une réduction significative des coûts et à l\'optimisation des processus',
            'atteignant des améliorations mesurables dans les métriques de performance'
        ];

        $responsibilities = [
            'Collaboré avec des équipes transfonctionnelles pour assurer le succès du projet',
            'Maintenu des standards élevés de qualité et d\'attention aux détails',
            'Fourni des conseils techniques et du mentorat aux membres de l\'équipe'
        ];

        $verb = $actionVerbs[array_rand($actionVerbs)];
        $achievement = $achievements[array_rand($achievements)];
        $responsibility = $responsibilities[array_rand($responsibilities)];

        return "• {$verb} {$original}, {$achievement}.\n• {$responsibility}.\n• Respecté constamment les délais de projet tout en maintenant des standards de qualité exceptionnels.";
    }

    private function generateArabicExperience($original, $jobTitle, $company)
    {
        $actionVerbs = ['طور', 'نفذ', 'أدار', 'قاد', 'أنشأ', 'صمم', 'حسن', 'نسق', 'نفذ', 'سلم'];
        $achievements = [
            'مما أدى إلى تحسين الكفاءة والإنتاجية',
            'مما أدى إلى تحسين تجربة المستخدم والرضا',
            'مما ساهم في تقليل التكاليف بشكل كبير وتحسين العمليات',
            'مما حقق تحسينات قابلة للقياس في مقاييس الأداء'
        ];

        $responsibilities = [
            'تعاون مع فرق متعددة الوظائف لضمان نجاح المشروع',
            'حافظ على معايير عالية من الجودة والاهتمام بالتفاصيل',
            'قدم التوجيه التقني والإرشاد لأعضاء الفريق'
        ];

        $verb = $actionVerbs[array_rand($actionVerbs)];
        $achievement = $achievements[array_rand($achievements)];
        $responsibility = $responsibilities[array_rand($responsibilities)];

        return "• {$verb} {$original}، {$achievement}.\n• {$responsibility}.\n• التزم باستمرار بالمواعيد النهائية للمشروع مع الحفاظ على معايير جودة استثنائية.";
    }

    private function generateKeyAchievements($data)
    {
        $language = $data['cv_language'] ?? 'en';

        switch ($language) {
            case 'ar':
                return [
                    'حقق تحسينات ملحوظة في كفاءة العمليات والإنتاجية',
                    'قاد مبادرات ناجحة لتطوير وتحسين الأنظمة',
                    'حافظ على معدل رضا عالي من العملاء والزملاء',
                    'طور حلول مبتكرة لتحديات تقنية معقدة'
                ];
            case 'fr':
                return [
                    'Réalisé des améliorations notables dans l\'efficacité opérationnelle et la productivité',
                    'Dirigé des initiatives réussies de développement et d\'amélioration des systèmes',
                    'Maintenu un taux de satisfaction élevé des clients et collègues',
                    'Développé des solutions innovantes pour des défis techniques complexes'
                ];
            default:
                return [
                    'Achieved notable improvements in operational efficiency and productivity',
                    'Led successful initiatives for system development and enhancement',
                    'Maintained high satisfaction rates from clients and colleagues',
                    'Developed innovative solutions for complex technical challenges'
                ];
        }
    }

    private function generateCoreCompetencies($data)
    {
        $language = $data['cv_language'] ?? 'en';
        $skills = $data['skills'] ?? [];

        // Base competencies based on language
        switch ($language) {
            case 'ar':
                $baseCompetencies = [
                    'التفكير التحليلي وحل المشكلات',
                    'إدارة المشاريع والفرق',
                    'التواصل الفعال والعرض',
                    'التكيف مع التقنيات الجديدة',
                    'ضمان الجودة والتحسين المستمر'
                ];
                break;
            case 'fr':
                $baseCompetencies = [
                    'Pensée analytique et résolution de problèmes',
                    'Gestion de projets et d\'équipes',
                    'Communication efficace et présentation',
                    'Adaptation aux nouvelles technologies',
                    'Assurance qualité et amélioration continue'
                ];
                break;
            default:
                $baseCompetencies = [
                    'Analytical thinking and problem-solving',
                    'Project and team management',
                    'Effective communication and presentation',
                    'Adaptation to new technologies',
                    'Quality assurance and continuous improvement'
                ];
        }

        // Combine with user skills
        $allCompetencies = array_merge($baseCompetencies, array_slice($skills, 0, 3));

        return array_slice($allCompetencies, 0, 6);
    }
}
