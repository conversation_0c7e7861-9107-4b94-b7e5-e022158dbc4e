<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Http;
use App\Models\CV;
use App\Models\User;

class CVBuilderController extends Controller
{
    public function index()
    {
        $cvs = [];
        if (Auth::check()) {
            $cvs = CV::where('user_id', Auth::id())->latest()->get();
        }
        
        return view('cv-builder.index', compact('cvs'));
    }

    public function create()
    {
        return view('cv-builder.create-new');
    }

    public function getCVData($id)
    {
        $cv = CV::findOrFail($id);
        
        // Check if user owns this CV
        if (Auth::check() && $cv->user_id !== Auth::id()) {
            abort(403);
        }
        
        return response()->json($cv->data);
    }

    public function aiQuickGenerate(Request $request)
    {
        try {
            $jobTitle = $request->input('job_title');
            $experience = $request->input('experience', 'entry');
            $industry = $request->input('industry', 'general');
            
            // Mock AI response for now
            $aiResponse = [
                'personal_info' => [
                    'name' => '<PERSON> Doe',
                    'email' => '<EMAIL>',
                    'phone' => '+1234567890',
                    'location' => 'City, Country',
                    'summary' => "Experienced {$jobTitle} with strong background in {$industry}. Passionate about delivering high-quality results and continuous learning."
                ],
                'experience' => [
                    [
                        'title' => $jobTitle,
                        'company' => 'Previous Company',
                        'duration' => '2020 - Present',
                        'description' => "Led multiple projects in {$industry} sector, achieving significant improvements in efficiency and quality."
                    ]
                ],
                'education' => [
                    [
                        'degree' => 'Bachelor\'s Degree',
                        'institution' => 'University Name',
                        'year' => '2020',
                        'field' => 'Related Field'
                    ]
                ],
                'skills' => [
                    'Technical Skills',
                    'Communication',
                    'Problem Solving',
                    'Team Leadership',
                    'Project Management'
                ]
            ];
            
            return response()->json([
                'success' => true,
                'data' => $aiResponse
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate CV data'
            ], 500);
        }
    }

    public function getTemplateRecommendations(Request $request)
    {
        try {
            $jobTitle = $request->input('job_title');
            $experience = $request->input('experience', 'entry');
            $industry = $request->input('industry', 'general');
            
            // Mock template recommendations based on job requirements
            $templates = [
                [
                    'id' => 'modern-professional',
                    'name' => 'Modern Professional',
                    'description' => 'Clean and modern design perfect for corporate roles',
                    'preview_url' => '/images/templates/modern-professional.jpg',
                    'match_score' => 95,
                    'recommended_for' => ['Corporate', 'Business', 'Management']
                ],
                [
                    'id' => 'creative-portfolio',
                    'name' => 'Creative Portfolio',
                    'description' => 'Vibrant design ideal for creative professionals',
                    'preview_url' => '/images/templates/creative-portfolio.jpg',
                    'match_score' => 88,
                    'recommended_for' => ['Design', 'Marketing', 'Creative']
                ],
                [
                    'id' => 'technical-expert',
                    'name' => 'Technical Expert',
                    'description' => 'Structured layout perfect for technical roles',
                    'preview_url' => '/images/templates/technical-expert.jpg',
                    'match_score' => 92,
                    'recommended_for' => ['Engineering', 'IT', 'Development']
                ]
            ];
            
            return response()->json([
                'success' => true,
                'templates' => $templates,
                'analysis' => [
                    'job_title' => $jobTitle,
                    'experience_level' => $experience,
                    'industry' => $industry,
                    'recommendations' => "Based on your {$experience} level experience in {$industry}, we recommend templates that emphasize professionalism and clarity."
                ]
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get template recommendations'
            ], 500);
        }
    }

    public function generatePreview(Request $request)
    {
        try {
            $cvData = $request->input('cv_data');
            $templateId = $request->input('template_id', 'modern-professional');
            
            // Generate preview HTML based on template and data
            $previewHtml = $this->generatePreviewHtml($cvData, $templateId);
            
            return response()->json([
                'success' => true,
                'preview_html' => $previewHtml
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate preview'
            ], 500);
        }
    }

    public function store(Request $request)
    {
        try {
            $cvData = $request->input('cv_data');
            $templateId = $request->input('template_id');
            $title = $request->input('title', 'My CV');
            
            $cv = new CV();
            $cv->user_id = Auth::id();
            $cv->title = $title;
            $cv->template = $templateId;
            $cv->data = $cvData;
            $cv->save();
            
            return response()->json([
                'success' => true,
                'message' => 'CV saved successfully',
                'cv_id' => $cv->id,
                'redirect_url' => route('index')
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save CV'
            ], 500);
        }
    }

    public function getAISuggestions(Request $request)
    {
        try {
            $section = $request->input('section');
            $currentData = $request->input('current_data');
            $jobTitle = $request->input('job_title');
            
            // Mock AI suggestions
            $suggestions = [
                'suggestions' => [
                    'Enhance your summary with specific achievements',
                    'Add quantifiable results to your experience',
                    'Include relevant technical skills for ' . $jobTitle,
                    'Consider adding certifications or training'
                ],
                'improvements' => [
                    'Use action verbs to start bullet points',
                    'Quantify your achievements with numbers',
                    'Tailor content to match job requirements',
                    'Ensure consistent formatting throughout'
                ]
            ];
            
            return response()->json([
                'success' => true,
                'suggestions' => $suggestions
            ]);
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to get AI suggestions'
            ], 500);
        }
    }

    public function performAIReview(Request $request)
    {
        // Implementation for AI review
        return $this->getAISuggestions($request);
    }

    public function optimizeWithAI(Request $request)
    {
        // Implementation for AI optimization
        return $this->getAISuggestions($request);
    }

    public function generateWithAI(Request $request)
    {
        // Implementation for AI generation
        return $this->aiQuickGenerate($request);
    }

    public function improveWithAI(Request $request)
    {
        // Implementation for AI improvement
        return $this->getAISuggestions($request);
    }

    public function generateJobDescription(Request $request)
    {
        // Implementation for job description generation
        return $this->getAISuggestions($request);
    }

    public function preview(Request $request)
    {
        // Implementation for preview
        return $this->generatePreview($request);
    }

    public function download(Request $request)
    {
        // Implementation for download
        return response()->json(['success' => true, 'message' => 'Download feature coming soon']);
    }

    private function generatePreviewHtml($cvData, $templateId)
    {
        // Generate HTML preview based on template
        $html = '<div class="cv-preview">';
        $html .= '<h1>' . ($cvData['personal_info']['name'] ?? 'Name') . '</h1>';
        $html .= '<p>' . ($cvData['personal_info']['email'] ?? '<EMAIL>') . '</p>';
        $html .= '<div class="summary">' . ($cvData['personal_info']['summary'] ?? 'Professional summary') . '</div>';
        $html .= '</div>';
        
        return $html;
    }
}
