<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\CVGeneration;
use Barryvdh\DomPDF\Facade\Pdf;

class CVBuilderController extends Controller
{
    public function index()
    {
        return view('services.free.cv-improvement');
    }

    public function generateCV(Request $request)
    {
        try {
            // Validate the request
            $validated = $request->validate([
                'full_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'address' => 'nullable|string|max:500',
                'professional_summary' => 'nullable|string|max:1000',
                'experience' => 'nullable|array',
                'experience.*.job_title' => 'required_with:experience|string|max:255',
                'experience.*.company' => 'required_with:experience|string|max:255',
                'experience.*.start_date' => 'required_with:experience|string|max:50',
                'experience.*.end_date' => 'required_with:experience|string|max:50',
                'experience.*.description' => 'nullable|string|max:1000',
                'education' => 'nullable|array',
                'education.*.degree' => 'required_with:education|string|max:255',
                'education.*.institution' => 'required_with:education|string|max:255',
                'education.*.year' => 'required_with:education|string|max:10',
                'skills' => 'nullable|array',
                'languages' => 'nullable|array',
                'languages.*.language' => 'required_with:languages|string|max:100',
                'languages.*.level' => 'required_with:languages|string|max:50',
                'cv_language' => 'required|in:en,ar,fr'
            ]);

            // Generate AI-enhanced CV content
            $enhancedContent = $this->enhanceWithAI($validated);

            // Save to database
            $cvGeneration = CVGeneration::create([
                'user_id' => Auth::id(),
                'original_data' => json_encode($validated),
                'enhanced_data' => json_encode($enhancedContent),
                'cv_language' => $validated['cv_language'],
                'status' => 'completed'
            ]);

            // Generate PDF
            $pdfPath = $this->generatePDF($enhancedContent, $validated['cv_language']);

            return response()->json([
                'success' => true,
                'message' => 'CV generated successfully with AI enhancement!',
                'cv_id' => $cvGeneration->id,
                'download_url' => route('cv.download', $cvGeneration->id),
                'enhanced_content' => $enhancedContent
            ]);

        } catch (\Exception $e) {
            Log::error('CV Generation Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate CV. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function enhanceWithAI($data)
    {
        $apiKey = config('services.openai.api_key');
        
        if (!$apiKey) {
            throw new \Exception('OpenAI API key not configured');
        }

        // Prepare the prompt based on CV language
        $languagePrompts = [
            'en' => 'English',
            'ar' => 'Arabic',
            'fr' => 'French'
        ];

        $language = $languagePrompts[$data['cv_language']] ?? 'English';
        
        $prompt = $this->buildAIPrompt($data, $language);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post('https://api.openai.com/v1/chat/completions', [
                'model' => config('services.openai.model', 'gpt-3.5-turbo'),
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => "You are a professional CV writer and career consultant. Your task is to enhance CV content to make it more professional, impactful, and ATS-friendly. Always respond in valid JSON format."
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => config('services.openai.max_tokens', 2000),
                'temperature' => config('services.openai.temperature', 0.7),
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $enhancedContent = json_decode($result['choices'][0]['message']['content'], true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON response from AI');
                }

                return $enhancedContent;
            } else {
                throw new \Exception('OpenAI API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('AI Enhancement Error: ' . $e->getMessage());
            
            // Fallback: return original data with basic enhancements
            return $this->basicEnhancement($data);
        }
    }

    private function buildAIPrompt($data, $language)
    {
        $prompt = "Please enhance the following CV data to make it more professional and impactful. ";
        $prompt .= "The CV should be in {$language}. ";
        $prompt .= "Improve the professional summary, job descriptions, and make the content more ATS-friendly. ";
        $prompt .= "Return the response in JSON format with the same structure as the input data.\n\n";
        $prompt .= "Input CV Data:\n" . json_encode($data, JSON_PRETTY_PRINT);
        $prompt .= "\n\nPlease enhance:\n";
        $prompt .= "1. Professional summary - make it more compelling and keyword-rich\n";
        $prompt .= "2. Job descriptions - add action verbs and quantifiable achievements\n";
        $prompt .= "3. Skills - organize and prioritize them\n";
        $prompt .= "4. Overall content - ensure it's professional and ATS-optimized\n";
        $prompt .= "\nReturn only valid JSON with the enhanced data.";

        return $prompt;
    }

    private function basicEnhancement($data)
    {
        // Basic fallback enhancement if AI fails
        if (isset($data['professional_summary']) && !empty($data['professional_summary'])) {
            $data['professional_summary'] = "Professional " . $data['professional_summary'];
        }

        if (isset($data['experience']) && is_array($data['experience'])) {
            foreach ($data['experience'] as &$exp) {
                if (isset($exp['description'])) {
                    $exp['description'] = "• " . $exp['description'];
                }
            }
        }

        return $data;
    }

    private function generatePDF($content, $language)
    {
        $isRTL = $language === 'ar';
        
        $pdf = Pdf::loadView('cv.template', [
            'content' => $content,
            'language' => $language,
            'isRTL' => $isRTL
        ]);

        if ($isRTL) {
            $pdf->setOption('isHtml5ParserEnabled', true);
            $pdf->setOption('isRemoteEnabled', true);
        }

        $filename = 'cv_' . time() . '.pdf';
        $path = storage_path('app/public/cvs/' . $filename);
        
        // Create directory if it doesn't exist
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
        }

        $pdf->save($path);
        
        return $filename;
    }

    public function downloadCV($id)
    {
        $cvGeneration = CVGeneration::where('id', $id)
            ->where('user_id', Auth::id())
            ->firstOrFail();

        $content = json_decode($cvGeneration->enhanced_data, true);
        $language = $cvGeneration->cv_language;
        
        $filename = $this->generatePDF($content, $language);
        $path = storage_path('app/public/cvs/' . $filename);

        return response()->download($path, 'CV_' . $cvGeneration->id . '.pdf');
    }
}
