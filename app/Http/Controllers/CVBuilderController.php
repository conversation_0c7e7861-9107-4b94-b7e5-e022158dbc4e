<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Auth;
use App\Models\CVGeneration;
use Barryvdh\DomPDF\Facade\Pdf;

class CVBuilderController extends Controller
{
    public function index()
    {
        return view('services.free.cv-improvement');
    }

    public function generateCV(Request $request)
    {
        Log::info('CV Generation started', ['request_data' => $request->all()]);

        try {
            // Validate the request
            $validated = $request->validate([
                'full_name' => 'required|string|max:255',
                'email' => 'required|email|max:255',
                'phone' => 'required|string|max:20',
                'address' => 'nullable|string|max:500',
                'professional_summary' => 'nullable|string|max:1000',
                'experience' => 'nullable|array',
                'experience.*.job_title' => 'required_with:experience|string|max:255',
                'experience.*.company' => 'required_with:experience|string|max:255',
                'experience.*.start_date' => 'required_with:experience|string|max:50',
                'experience.*.end_date' => 'required_with:experience|string|max:50',
                'experience.*.description' => 'nullable|string|max:1000',
                'education' => 'nullable|array',
                'education.*.degree' => 'required_with:education|string|max:255',
                'education.*.institution' => 'required_with:education|string|max:255',
                'education.*.year' => 'required_with:education|string|max:10',
                'skills' => 'nullable|array',
                'languages' => 'nullable|array',
                'languages.*.language' => 'required_with:languages|string|max:100',
                'languages.*.level' => 'required_with:languages|string|max:50',
                'cv_language' => 'required|in:en,ar,fr'
            ]);

            // Generate AI-enhanced CV content
            $enhancedContent = $this->enhanceWithAI($validated);

            // Save to database
            $cvGeneration = CVGeneration::create([
                'user_id' => Auth::id() ?? 1, // Use guest user ID if not authenticated
                'original_data' => json_encode($validated),
                'enhanced_data' => json_encode($enhancedContent),
                'cv_language' => $validated['cv_language'],
                'status' => 'completed'
            ]);

            // Generate PDF
            Log::info('Generating PDF', ['cv_id' => $cvGeneration->id]);
            $pdfFilename = $this->generatePDF($enhancedContent, $validated['cv_language']);
            Log::info('PDF generated', ['filename' => $pdfFilename]);

            // Update CV generation record with PDF path
            $cvGeneration->update(['pdf_path' => $pdfFilename]);

            $downloadUrl = route('cv.download', $cvGeneration->id);
            Log::info('Sending response', ['download_url' => $downloadUrl]);

            return response()->json([
                'success' => true,
                'message' => 'CV generated successfully with AI enhancement!',
                'cv_id' => $cvGeneration->id,
                'download_url' => $downloadUrl,
                'enhanced_content' => $enhancedContent
            ]);

        } catch (\Exception $e) {
            Log::error('CV Generation Error: ' . $e->getMessage());
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to generate CV. Please try again.',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    private function enhanceWithAI($data)
    {
        $apiKey = config('services.openai.api_key');

        // If no API key or demo key, use basic enhancement
        if (!$apiKey || strpos($apiKey, 'your-') !== false || strpos($apiKey, 'demo') !== false) {
            Log::info('Using basic enhancement - no valid OpenAI API key');
            return $this->basicEnhancement($data);
        }

        // Prepare the prompt based on CV language
        $languagePrompts = [
            'en' => 'English',
            'ar' => 'Arabic',
            'fr' => 'French'
        ];

        $language = $languagePrompts[$data['cv_language']] ?? 'English';
        
        $prompt = $this->buildAIPrompt($data, $language);

        try {
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $apiKey,
                'Content-Type' => 'application/json',
            ])->timeout(60)->post('https://api.openai.com/v1/chat/completions', [
                'model' => config('services.openai.model', 'gpt-3.5-turbo'),
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => "You are a professional CV writer and career consultant. Your task is to enhance CV content to make it more professional, impactful, and ATS-friendly. Always respond in valid JSON format."
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => config('services.openai.max_tokens', 2000),
                'temperature' => config('services.openai.temperature', 0.7),
            ]);

            if ($response->successful()) {
                $result = $response->json();
                $enhancedContent = json_decode($result['choices'][0]['message']['content'], true);
                
                if (json_last_error() !== JSON_ERROR_NONE) {
                    throw new \Exception('Invalid JSON response from AI');
                }

                return $enhancedContent;
            } else {
                throw new \Exception('OpenAI API request failed: ' . $response->body());
            }

        } catch (\Exception $e) {
            Log::error('AI Enhancement Error: ' . $e->getMessage());
            
            // Fallback: return original data with basic enhancements
            return $this->basicEnhancement($data);
        }
    }

    private function buildAIPrompt($data, $language)
    {
        $prompt = "Please enhance the following CV data to make it more professional and impactful. ";
        $prompt .= "The CV should be in {$language}. ";
        $prompt .= "Improve the professional summary, job descriptions, and make the content more ATS-friendly. ";
        $prompt .= "Return the response in JSON format with the same structure as the input data.\n\n";
        $prompt .= "Input CV Data:\n" . json_encode($data, JSON_PRETTY_PRINT);
        $prompt .= "\n\nPlease enhance:\n";
        $prompt .= "1. Professional summary - make it more compelling and keyword-rich\n";
        $prompt .= "2. Job descriptions - add action verbs and quantifiable achievements\n";
        $prompt .= "3. Skills - organize and prioritize them\n";
        $prompt .= "4. Overall content - ensure it's professional and ATS-optimized\n";
        $prompt .= "\nReturn only valid JSON with the enhanced data.";

        return $prompt;
    }

    private function basicEnhancement($data)
    {
        // Enhanced basic enhancement with professional improvements

        // Enhance professional summary
        if (isset($data['professional_summary']) && !empty($data['professional_summary'])) {
            $summary = $data['professional_summary'];

            // Add professional language if not already present
            if (!str_contains(strtolower($summary), 'experienced') && !str_contains(strtolower($summary), 'professional')) {
                $data['professional_summary'] = "Experienced professional with " . lcfirst($summary);
            }

            // Ensure it ends with a period
            if (!str_ends_with(trim($summary), '.')) {
                $data['professional_summary'] .= '.';
            }
        }

        // Enhance experience descriptions
        if (isset($data['experience']) && is_array($data['experience'])) {
            $actionVerbs = [
                'Managed', 'Led', 'Developed', 'Implemented', 'Coordinated', 'Supervised',
                'Created', 'Designed', 'Optimized', 'Improved', 'Achieved', 'Delivered',
                'Collaborated', 'Analyzed', 'Executed', 'Maintained', 'Established'
            ];

            foreach ($data['experience'] as &$exp) {
                if (isset($exp['description']) && !empty($exp['description'])) {
                    $description = trim($exp['description']);

                    // Add bullet point if not present
                    if (!str_starts_with($description, '•') && !str_starts_with($description, '-')) {
                        $description = '• ' . $description;
                    }

                    // Enhance with action verb if doesn't start with one
                    $firstWord = explode(' ', ltrim($description, '• -'))[0] ?? '';
                    if (!in_array(ucfirst($firstWord), $actionVerbs)) {
                        $randomVerb = $actionVerbs[array_rand($actionVerbs)];
                        $description = str_replace('• ', "• {$randomVerb} ", $description);
                    }

                    // Ensure proper ending
                    if (!str_ends_with(trim($description), '.')) {
                        $description .= '.';
                    }

                    $exp['description'] = $description;
                }
            }
        }

        // Enhance skills organization
        if (isset($data['skills']) && is_array($data['skills'])) {
            // Remove empty skills and trim
            $data['skills'] = array_filter(array_map('trim', $data['skills']));

            // Sort skills alphabetically
            sort($data['skills']);
        }

        // Add professional touch to education
        if (isset($data['education']) && is_array($data['education'])) {
            foreach ($data['education'] as &$edu) {
                if (isset($edu['degree']) && !empty($edu['degree'])) {
                    // Ensure degree is properly capitalized
                    $edu['degree'] = ucwords(strtolower($edu['degree']));
                }
                if (isset($edu['institution']) && !empty($edu['institution'])) {
                    // Ensure institution is properly capitalized
                    $edu['institution'] = ucwords(strtolower($edu['institution']));
                }
            }
        }

        return $data;
    }

    private function generatePDF($content, $language)
    {
        Log::info('generatePDF called', ['language' => $language]);

        $isRTL = $language === 'ar';

        try {
            $pdf = Pdf::loadView('cv.template', [
                'content' => $content,
                'language' => $language,
                'isRTL' => $isRTL
            ]);

            Log::info('PDF view loaded successfully');
        } catch (\Exception $e) {
            Log::error('PDF view loading failed', ['error' => $e->getMessage()]);
            throw $e;
        }

        if ($isRTL) {
            $pdf->setOption('isHtml5ParserEnabled', true);
            $pdf->setOption('isRemoteEnabled', true);
        }

        $filename = 'cv_' . time() . '.pdf';
        $path = storage_path('app/public/cvs/' . $filename);

        Log::info('Saving PDF', ['path' => $path]);

        // Create directory if it doesn't exist
        if (!file_exists(dirname($path))) {
            mkdir(dirname($path), 0755, true);
            Log::info('Created directory', ['dir' => dirname($path)]);
        }

        try {
            $pdf->save($path);
            Log::info('PDF saved successfully', ['filename' => $filename]);

            // Verify file was created
            if (file_exists($path)) {
                Log::info('PDF file verified', ['size' => filesize($path)]);
            } else {
                Log::error('PDF file not found after save');
            }
        } catch (\Exception $e) {
            Log::error('PDF save failed', ['error' => $e->getMessage()]);
            throw $e;
        }

        return $filename;
    }

    public function downloadCV($id)
    {
        Log::info('Download CV requested', ['id' => $id]);

        $cvGeneration = CVGeneration::where('id', $id)
            ->firstOrFail(); // Remove user_id check for testing

        Log::info('CV Generation found', ['cv_id' => $cvGeneration->id, 'pdf_path' => $cvGeneration->pdf_path]);

        // Check if PDF already exists
        if ($cvGeneration->pdf_path && file_exists(storage_path('app/public/cvs/' . $cvGeneration->pdf_path))) {
            $path = storage_path('app/public/cvs/' . $cvGeneration->pdf_path);
        } else {
            // Generate new PDF if not exists
            $content = json_decode($cvGeneration->enhanced_data, true);
            $language = $cvGeneration->cv_language;

            $filename = $this->generatePDF($content, $language);
            $cvGeneration->update(['pdf_path' => $filename]);
            $path = storage_path('app/public/cvs/' . $filename);
        }

        // Generate a user-friendly filename
        $userName = $content['full_name'] ?? 'CV';
        $userName = preg_replace('/[^a-zA-Z0-9_-]/', '_', $userName);
        $downloadName = $userName . '_CV.pdf';

        return response()->download($path, $downloadName);
    }
}
