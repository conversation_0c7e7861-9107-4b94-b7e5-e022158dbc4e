<?php

namespace App\Http\Controllers;

use App\Services\AIService;
use App\Models\ServiceResult;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class AIServiceController extends Controller
{
    public function __construct()
    {
        // Apply auth middleware only to specific methods
        $this->middleware('auth')->only(['processPersonalityAnalysis', 'processCvImprovement', 'processInterviewSimulation', 'showResult', 'history']);
    }

    /**
     * Show personality analysis form
     */
    public function personalityAnalysis()
    {
        return view('services.personality-analysis');
    }

    /**
     * Process personality analysis
     */
    public function processPersonalityAnalysis(Request $request)
    {
        $request->validate([
            'answers' => 'required|array',
            'answers.*' => 'required|string'
        ]);

        try {
            $user = Auth::user();
            $language = app()->getLocale();

            // Call AI service
            $aiService = new AIService();
            $analysis = $aiService->analyzePersonality($request->answers, $language);

            if ($analysis['success']) {
                // Save result to database only if user is authenticated
                if ($user) {
                    $result = ServiceResult::create([
                        'user_id' => $user->id,
                        'service_type' => 'personality_analysis',
                        'title' => __('messages.personality_analysis_title'),
                        'description' => __('messages.personality_analysis_description'),
                        'results' => $analysis,
                        'metadata' => [
                            'answers' => $request->answers,
                            'language' => $language,
                            'analysis_date' => now()
                        ],
                        'status' => 'completed',
                        'score' => $this->calculatePersonalityScore($analysis)
                    ]);

                    return redirect()->route('ai-service.result', $result->id)
                        ->with('success', __('messages.analysis_completed'));
                } else {
                    // For non-authenticated users, show results directly
                    return view('services.result', [
                        'service_type' => 'personality_analysis',
                        'title' => __('messages.personality_analysis_title'),
                        'results' => $analysis,
                        'show_login_prompt' => true
                    ]);
                }
            }

            return back()->with('error', __('messages.analysis_failed'));

        } catch (\Exception $e) {
            Log::error('Personality Analysis Error: ' . $e->getMessage());
            return back()->with('error', __('messages.service_error'));
        }
    }

    /**
     * Show CV improvement form
     */
    public function cvImprovement()
    {
        return view('services.cv-improvement');
    }

    /**
     * Process CV improvement
     */
    public function processCvImprovement(Request $request)
    {
        $request->validate([
            'cv_content' => 'required|string',
            'improvement_type' => 'required|string'
        ]);

        try {
            $user = Auth::user();
            $language = app()->getLocale();

            // Call AI service
            $aiService = new AIService();
            $improvement = $aiService->improveCVContent(
                $request->cv_content,
                $request->improvement_type,
                $language
            );

            if ($improvement['success']) {
                // Save result to database
                $result = ServiceResult::create([
                    'user_id' => $user->id,
                    'service_type' => 'cv_improvement',
                    'title' => __('messages.cv_improvement_title'),
                    'description' => __('messages.cv_improvement_description'),
                    'results' => $improvement,
                    'metadata' => [
                        'original_content' => $request->cv_content,
                        'improvement_type' => $request->improvement_type,
                        'language' => $language,
                        'improvement_date' => now()
                    ],
                    'status' => 'completed',
                    'score' => $this->calculateCvScore($improvement)
                ]);

                return redirect()->route('ai-service.result', $result->id)
                    ->with('success', __('messages.improvement_completed'));
            }

            return back()->with('error', __('messages.improvement_failed'));

        } catch (\Exception $e) {
            Log::error('CV Improvement Error: ' . $e->getMessage());
            return back()->with('error', __('messages.service_error'));
        }
    }

    /**
     * Show interview simulation form
     */
    public function interviewSimulation()
    {
        return view('services.interview-simulation');
    }

    /**
     * Process interview simulation
     */
    public function processInterviewSimulation(Request $request)
    {
        $request->validate([
            'job_position' => 'required|string',
            'interview_type' => 'required|string',
            'answers' => 'required|array'
        ]);

        try {
            $user = Auth::user();
            $language = app()->getLocale();

            // Generate interview summary using AI service
            $questions = $request->input('questions', []);
            $aiService = new AIService();
            $summary = $aiService->generateInterviewSummary(
                $questions,
                $request->answers,
                $request->job_position,
                $request->interview_type
            );

            if ($summary['success']) {
                // Save result to database
                $result = ServiceResult::create([
                    'user_id' => $user->id,
                    'service_type' => 'interview_simulation',
                    'title' => __('messages.interview_simulation_title'),
                    'description' => __('messages.interview_simulation_description'),
                    'results' => $summary,
                    'metadata' => [
                        'job_position' => $request->job_position,
                        'interview_type' => $request->interview_type,
                        'questions' => $questions,
                        'answers' => $request->answers,
                        'language' => $language,
                        'simulation_date' => now()
                    ],
                    'status' => 'completed',
                    'score' => $summary['overall_score'] ?? 70
                ]);

                return redirect()->route('ai-service.result', $result->id)
                    ->with('success', __('messages.simulation_completed'));
            }

            return back()->with('error', __('messages.simulation_failed'));

        } catch (\Exception $e) {
            Log::error('Interview Simulation Error: ' . $e->getMessage());
            return back()->with('error', __('messages.service_error'));
        }
    }

    /**
     * Show service result
     */
    public function showResult($id)
    {
        $result = ServiceResult::where('user_id', Auth::id())
            ->findOrFail($id);

        return view('services.result', compact('result'));
    }

    /**
     * Get user's service history
     */
    public function history()
    {
        $results = ServiceResult::where('user_id', Auth::id())
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('services.history', compact('results'));
    }

    /**
     * Calculate personality score
     */
    protected function calculatePersonalityScore(array $analysis): int
    {
        $score = 70; // Base score

        if (isset($analysis['personality_type'])) $score += 10;
        if (isset($analysis['strengths']) && !empty($analysis['strengths'])) $score += 10;
        if (isset($analysis['recommendations']) && !empty($analysis['recommendations'])) $score += 10;

        return min($score, 100);
    }

    /**
     * Calculate CV score
     */
    protected function calculateCvScore(array $improvement): int
    {
        // Extract score from improvement results
        if (isset($improvement['score'])) {
            return (int) $improvement['score'];
        }

        return 75; // Default score
    }

    /**
     * Demo personality analysis for non-authenticated users
     */
    public function demoPersonalityAnalysis(Request $request)
    {
        $request->validate([
            'answers' => 'required|array',
            'answers.*' => 'required|string'
        ]);

        try {
            $language = app()->getLocale();

            // Provide demo analysis without AI service
            $demoAnalysis = [
                'success' => true,
                'personality_type' => 'Demo Analysis',
                'strengths' => [
                    'Leadership skills',
                    'Creative thinking',
                    'Problem solving'
                ],
                'recommendations' => [
                    'Focus on team management roles',
                    'Develop technical skills',
                    'Consider project management'
                ],
                'description' => 'This is a demo analysis. Sign up for full AI-powered results!'
            ];

            return view('services.demo-result', [
                'service_type' => 'personality_analysis',
                'title' => 'Personality Analysis Demo',
                'results' => $demoAnalysis,
                'is_demo' => true,
                'show_login_prompt' => true
            ]);

        } catch (\Exception $e) {
            Log::error('Demo Personality Analysis Error: ' . $e->getMessage());
            return back()->with('error', 'Service temporarily unavailable');
        }
    }

    /**
     * Demo CV improvement for non-authenticated users
     */
    public function demoCvImprovement(Request $request)
    {
        try {
            $demoImprovement = [
                'success' => true,
                'improvements' => [
                    'Add more specific achievements',
                    'Include relevant keywords',
                    'Improve formatting and structure'
                ],
                'suggestions' => [
                    'Quantify your accomplishments',
                    'Tailor CV to job description',
                    'Use action verbs'
                ],
                'score' => 75
            ];

            return view('services.demo-result', [
                'service_type' => 'cv_improvement',
                'title' => 'CV Improvement Demo',
                'results' => $demoImprovement,
                'is_demo' => true,
                'show_login_prompt' => true
            ]);

        } catch (\Exception $e) {
            Log::error('Demo CV Improvement Error: ' . $e->getMessage());
            return back()->with('error', 'Service temporarily unavailable');
        }
    }

    /**
     * Demo interview simulation for non-authenticated users
     */
    public function demoInterviewSimulation(Request $request)
    {
        try {
            $demoSimulation = [
                'success' => true,
                'feedback' => [
                    'Good communication skills',
                    'Need to provide more specific examples',
                    'Show more confidence in answers'
                ],
                'tips' => [
                    'Practice the STAR method',
                    'Research the company thoroughly',
                    'Prepare questions for the interviewer'
                ],
                'score' => 80
            ];

            return view('services.demo-result', [
                'service_type' => 'interview_simulation',
                'title' => 'Interview Simulation Demo',
                'results' => $demoSimulation,
                'is_demo' => true,
                'show_login_prompt' => true
            ]);

        } catch (\Exception $e) {
            Log::error('Demo Interview Simulation Error: ' . $e->getMessage());
            return back()->with('error', 'Service temporarily unavailable');
        }
    }
}
