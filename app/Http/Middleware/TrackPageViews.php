<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\SiteAnalytics;

class TrackPageViews
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $response = $next($request);

        // Only track GET requests and successful responses
        if ($request->isMethod('GET') && $response->getStatusCode() === 200) {
            // Don't track admin routes, API routes, or asset requests
            if (!$request->is('admin/*') &&
                !$request->is('api/*') &&
                !$request->is('_debugbar/*') &&
                !str_contains($request->path(), '.')) {

                try {
                    SiteAnalytics::create([
                        'page_url' => $request->path() === '/' ? '/' : '/' . $request->path(),
                        'user_ip' => $request->ip(),
                        'user_agent' => $request->userAgent(),
                        'referrer' => $request->header('referer'),
                        'device_type' => $this->getDeviceType($request->userAgent()),
                        'browser' => $this->getBrowser($request->userAgent()),
                        'country' => 'Unknown', // You can integrate with GeoIP service
                        'session_duration' => 0, // Can be updated with JavaScript
                    ]);
                } catch (\Exception $e) {
                    // Silently fail to avoid breaking the application
                    \Log::error('Failed to track page view: ' . $e->getMessage());
                }
            }
        }

        return $response;
    }

    /**
     * Detect device type from user agent
     */
    private function getDeviceType($userAgent): string
    {
        if (preg_match('/Mobile|Android|iPhone|iPad/', $userAgent)) {
            if (preg_match('/iPad/', $userAgent)) {
                return 'tablet';
            }
            return 'mobile';
        }
        return 'desktop';
    }

    /**
     * Detect browser from user agent
     */
    private function getBrowser($userAgent): string
    {
        if (preg_match('/Chrome/', $userAgent)) {
            return 'Chrome';
        } elseif (preg_match('/Firefox/', $userAgent)) {
            return 'Firefox';
        } elseif (preg_match('/Safari/', $userAgent)) {
            return 'Safari';
        } elseif (preg_match('/Edge/', $userAgent)) {
            return 'Edge';
        }
        return 'Unknown';
    }
}
