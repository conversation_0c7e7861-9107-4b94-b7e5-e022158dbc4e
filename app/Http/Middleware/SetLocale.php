<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;
use Symfony\Component\HttpFoundation\Response;

class SetLocale
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        $supportedLocales = ['ar', 'en', 'fr'];
        $defaultLocale = 'en'; // Always default to English

        // Priority 1: Check session for stored locale (user's choice)
        if (Session::has('locale') && in_array(Session::get('locale'), $supportedLocales)) {
            $locale = Session::get('locale');
        }
        // Priority 2: Check if locale is provided in URL
        elseif ($request->has('locale') && in_array($request->get('locale'), $supportedLocales)) {
            $locale = $request->get('locale');
            Session::put('locale', $locale);
        }
        // Priority 3: Default to English (ignore browser language)
        else {
            $locale = $defaultLocale;
            // Only set session if not already set
            if (!Session::has('locale')) {
                Session::put('locale', $locale);
            }
        }

        App::setLocale($locale);

        return $next($request);
    }
}
