<?php

namespace App\Services;

use OpenAI\Laravel\Facades\OpenAI;
use Illuminate\Support\Facades\Log;
use Exception;

class AIService
{
    protected $defaultModel;
    protected $maxTokens;
    protected $temperature;

    public function __construct()
    {
        $this->defaultModel = config('ai.default_model', 'gpt-3.5-turbo');
        $this->maxTokens = config('ai.max_tokens', 2000);
        $this->temperature = config('ai.temperature', 0.7);
    }

    /**
     * Analyze user personality based on answers
     */
    public function analyzePersonality(array $answers, string $language = 'ar'): array
    {
        try {
            $prompt = $this->buildPersonalityPrompt($answers, $language);
            
            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('personality', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => $this->maxTokens,
                'temperature' => $this->temperature,
            ]);

            $analysis = $response->choices[0]->message->content;
            
            return [
                'success' => true,
                'analysis' => $analysis,
                'personality_type' => $this->extractPersonalityType($analysis),
                'strengths' => $this->extractStrengths($analysis),
                'recommendations' => $this->extractRecommendations($analysis),
            ];

        } catch (Exception $e) {
            Log::error('AI Personality Analysis Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في تحليل الشخصية. يرجى المحاولة مرة أخرى.',
            ];
        }
    }

    /**
     * Generate CV based on user data
     */
    public function generateCV(array $userData, string $language = 'ar'): array
    {
        try {
            // For demo purposes, generate a mock CV
            $personalInfo = $userData['personal_info'] ?? [];
            $fullName = $personalInfo['full_name'] ?? 'غير محدد';
            $jobTitle = $userData['job_title'] ?? 'مطور';
            $skills = implode('، ', $userData['skills'] ?? ['PHP', 'JavaScript', 'MySQL']);
            $experienceLevel = $userData['experience_level'] ?? 'متوسط';

            $cvContent = $this->generateMockCV($fullName, $jobTitle, $skills, $experienceLevel, $language);

            return [
                'success' => true,
                'cv_content' => $cvContent,
                'sections' => $this->parseCVSections($cvContent),
            ];

        } catch (Exception $e) {
            Log::error('AI CV Generation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في إنشاء السيرة الذاتية. يرجى المحاولة مرة أخرى.',
            ];
        }
    }

    /**
     * Generate mock CV content for demo
     */
    private function generateMockCV(string $name, string $jobTitle, string $skills, string $experienceLevel, string $language): string
    {
        if ($language === 'ar') {
            return "# الملخص المهني

{$name} هو {$jobTitle} {$experienceLevel} الخبرة مع شغف كبير بالتكنولوجيا والابتكار. يتمتع بخبرة واسعة في تطوير الحلول التقنية المبتكرة وإدارة المشاريع بكفاءة عالية. يسعى دائماً لتطوير مهاراته ومواكبة أحدث التقنيات في المجال.

# المهارات

المهارات التقنية:
- {$skills}
- إدارة قواعد البيانات
- تطوير واجهات المستخدم
- اختبار البرمجيات
- إدارة الخوادم

المهارات الشخصية:
- العمل الجماعي
- حل المشكلات
- التواصل الفعال
- إدارة الوقت
- القيادة

# الخبرة العملية

مطور برمجيات أول | شركة التقنيات المتقدمة | 2020 - حتى الآن
- تطوير وصيانة التطبيقات الويب باستخدام أحدث التقنيات
- قيادة فريق من 5 مطورين في مشاريع متعددة
- تحسين أداء التطبيقات بنسبة 40%
- تطوير واجهات برمجة التطبيقات (APIs) للتطبيقات المحمولة

مطور ويب | شركة الحلول الرقمية | 2018 - 2020
- تطوير مواقع ويب تفاعلية ومتجاوبة
- العمل مع قواعد البيانات وتحسين الاستعلامات
- التعاون مع فرق التصميم والتسويق
- صيانة وتحديث المواقع الموجودة

# التعليم

بكالوريوس علوم الحاسوب | جامعة محمد الخامس | 2014 - 2018
- تخصص: هندسة البرمجيات
- المعدل التراكمي: 3.8/4.0
- مشروع التخرج: تطوير نظام إدارة المكتبات الذكي

# الشهادات والدورات

- شهادة AWS Certified Developer
- دورة Laravel المتقدمة
- شهادة إدارة المشاريع (PMP)
- دورة الأمن السيبراني

# الإنجازات

- تطوير تطبيق حاز على جائزة أفضل تطبيق محلي لعام 2022
- قيادة مشروع نجح في توفير 30% من التكاليف التشغيلية
- نشر 3 مقالات تقنية في مجلات متخصصة
- المشاركة في 5 مؤتمرات تقنية كمتحدث";
        }

        // English version
        return "# Professional Summary

{$name} is a {$experienceLevel} {$jobTitle} with a great passion for technology and innovation. Has extensive experience in developing innovative technical solutions and managing projects with high efficiency. Always strives to develop skills and keep up with the latest technologies in the field.

# Skills

Technical Skills:
- {$skills}
- Database Management
- User Interface Development
- Software Testing
- Server Management

Personal Skills:
- Teamwork
- Problem Solving
- Effective Communication
- Time Management
- Leadership

# Work Experience

Senior Software Developer | Advanced Technologies Company | 2020 - Present
- Develop and maintain web applications using latest technologies
- Lead a team of 5 developers on multiple projects
- Improved application performance by 40%
- Develop APIs for mobile applications

Web Developer | Digital Solutions Company | 2018 - 2020
- Develop interactive and responsive websites
- Work with databases and optimize queries
- Collaborate with design and marketing teams
- Maintain and update existing websites

# Education

Bachelor of Computer Science | Mohammed V University | 2014 - 2018
- Major: Software Engineering
- GPA: 3.8/4.0
- Graduation Project: Smart Library Management System

# Certifications

- AWS Certified Developer
- Advanced Laravel Course
- Project Management Professional (PMP)
- Cybersecurity Course

# Achievements

- Developed an app that won Best Local App Award 2022
- Led a project that saved 30% of operational costs
- Published 3 technical articles in specialized journals
- Participated in 5 technical conferences as a speaker";
    }

    /**
     * Recommend jobs based on user profile
     */
    public function recommendJobs(array $userProfile, string $language = 'ar'): array
    {
        try {
            $prompt = $this->buildJobRecommendationPrompt($userProfile, $language);
            
            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('jobs', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => $this->maxTokens,
                'temperature' => $this->temperature,
            ]);

            $recommendations = $response->choices[0]->message->content;
            
            return [
                'success' => true,
                'recommendations' => $recommendations,
                'jobs' => $this->parseJobRecommendations($recommendations),
            ];

        } catch (Exception $e) {
            Log::error('AI Job Recommendation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في توصيات الوظائف. يرجى المحاولة مرة أخرى.',
            ];
        }
    }

    /**
     * Chat bot for career guidance
     */
    public function chatBot(string $message, array $context = [], string $language = 'ar'): array
    {
        try {
            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('chatbot', $language)],
                    ['role' => 'user', 'content' => $message]
                ],
                'max_tokens' => 1000,
                'temperature' => $this->temperature,
            ]);

            $botResponse = $response->choices[0]->message->content;
            
            return [
                'success' => true,
                'response' => $botResponse,
                'suggestions' => $this->extractSuggestions($botResponse),
            ];

        } catch (Exception $e) {
            Log::error('AI ChatBot Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في المساعد الذكي. يرجى المحاولة مرة أخرى.',
            ];
        }
    }

    /**
     * Build personality analysis prompt
     */
    private function buildPersonalityPrompt(array $answers, string $language): string
    {
        $answersText = implode("\n", array_map(function($answer, $index) {
            return "السؤال " . ($index + 1) . ": " . $answer;
        }, $answers, array_keys($answers)));

        return "قم بتحليل شخصية المستخدم بناءً على إجاباته التالية:\n\n" . $answersText . 
               "\n\nيرجى تقديم تحليل شامل يتضمن:\n" .
               "1. نوع الشخصية\n" .
               "2. نقاط القوة\n" .
               "3. المجالات المناسبة للعمل\n" .
               "4. توصيات للتطوير المهني";
    }

    /**
     * Build CV generation prompt
     */
    private function buildCVPrompt(array $userData, string $language): string
    {
        $personalInfo = $userData['personal_info'] ?? [];
        $userInfo = "الاسم: " . ($personalInfo['full_name'] ?? 'غير محدد') . "\n";
        $userInfo .= "البريد الإلكتروني: " . ($personalInfo['email'] ?? 'غير محدد') . "\n";
        $userInfo .= "رقم الهاتف: " . ($personalInfo['phone'] ?? 'غير محدد') . "\n";
        $userInfo .= "المسمى الوظيفي المرغوب: " . ($userData['job_title'] ?? 'غير محدد') . "\n";
        $userInfo .= "المجال/الصناعة: " . ($userData['industry'] ?? 'غير محدد') . "\n";
        $userInfo .= "مستوى الخبرة: " . ($userData['experience_level'] ?? 'غير محدد') . "\n";
        $userInfo .= "المهارات: " . implode(', ', $userData['skills'] ?? []) . "\n";

        return "قم بإنشاء محتوى سيرة ذاتية احترافية باللغة العربية بناءً على المعلومات التالية:\n\n" .
               $userInfo .
               "\n\nيرجى إنشاء:\n" .
               "1. ملخص مهني قوي ومقنع (2-3 جمل)\n" .
               "2. قائمة بالمهارات المهنية والتقنية\n" .
               "3. اقتراحات للخبرات العملية المناسبة\n" .
               "4. اقتراحات للتعليم والمؤهلات\n" .
               "5. نصائح لتحسين السيرة الذاتية\n\n" .
               "قم بتنسيق الإجابة بشكل منظم ومقسم إلى أقسام واضحة.";
    }

    /**
     * Build job recommendation prompt
     */
    private function buildJobRecommendationPrompt(array $userProfile, string $language): string
    {
        $profile = "نوع الشخصية: " . ($userProfile['personality_type'] ?? 'غير محدد') . "\n";
        $profile .= "المهارات: " . implode(', ', $userProfile['skills'] ?? []) . "\n";
        $profile .= "الاهتمامات: " . implode(', ', $userProfile['interests'] ?? []) . "\n";
        $profile .= "مستوى الخبرة: " . ($userProfile['experience_level'] ?? 'مبتدئ');

        return "بناءً على الملف الشخصي التالي، قم بتوصية 5 وظائف مناسبة:\n\n" . 
               $profile . 
               "\n\nيرجى تقديم:\n" .
               "1. اسم الوظيفة\n" .
               "2. وصف مختصر\n" .
               "3. المهارات المطلوبة\n" .
               "4. متوسط الراتب المتوقع\n" .
               "5. فرص النمو";
    }

    /**
     * Get system prompts for different AI functions
     */
    private function getSystemPrompt(string $type, string $language): string
    {
        $prompts = [
            'personality' => 'أنت خبير في تحليل الشخصية والإرشاد المهني. قم بتحليل شخصية المستخدم بطريقة علمية ومهنية باللغة العربية.',
            'cv' => 'أنت خبير في كتابة السير الذاتية. قم بإنشاء سيرة ذاتية احترافية ومنظمة باللغة العربية.',
            'jobs' => 'أنت مستشار مهني خبير. قم بتوصية وظائف مناسبة بناءً على ملف المستخدم الشخصي باللغة العربية.',
            'chatbot' => 'أنت مساعد ذكي متخصص في الإرشاد المهني. ساعد المستخدمين في أسئلتهم المهنية باللغة العربية بطريقة ودودة ومفيدة.',
            'cv_improvement' => 'أنت خبير في تحسين السير الذاتية. قم بتحسين وتطوير محتوى السيرة الذاتية لتكون أكثر احترافية وجاذبية.',
            'cv_suggestions' => 'أنت مستشار مهني متخصص في السير الذاتية. قدم اقتراحات مفيدة ومحددة لتحسين أقسام السيرة الذاتية.',
            'template_recommendation' => $this->getTemplateRecommendationSystemPrompt($language),
            'section_suggestions' => 'أنت خبير في السير الذاتية. قدم اقتراحات محددة وعملية لتحسين أقسام السيرة الذاتية بناءً على أفضل الممارسات.',
            'cv_review' => 'أنت خبير في مراجعة السير الذاتية. قم بتحليل السيرة الذاتية وقدم تقييماً شاملاً يتضمن نقاط القوة والضعف والنقاط.',
            'cv_optimization' => 'أنت خبير في تحسين السير الذاتية. قم بتحليل السيرة الذاتية وقدم تحسينات محددة لكل قسم لجعلها أكثر احترافية وجاذبية.',
        ];

        return $prompts[$type] ?? $prompts['chatbot'];
    }

    /**
     * Get template recommendation system prompt
     */
    private function getTemplateRecommendationSystemPrompt(string $language): string
    {
        if ($language === 'ar') {
            return 'أنت خبير في تصميم السير الذاتية ومستشار مهني متخصص. مهمتك هي تحليل الملف المهني للمستخدم وتقديم توصيات دقيقة لأفضل قوالب السيرة الذاتية التي تناسب مجاله المهني ومستوى خبرته.

قم بتحليل المعلومات المقدمة وقدم:
1. تحليل موجز للملف المهني
2. أفضل 3 توصيات للقوالب مع الأسباب التفصيلية
3. نصائح عملية لاختيار القالب المناسب

يجب أن تكون توصياتك مبنية على:
- طبيعة المجال المهني
- مستوى الخبرة
- نوع الصناعة
- متطلبات السوق

استخدم التنسيق المطلوب بدقة واجعل إجاباتك مفيدة وعملية.';
        } elseif ($language === 'fr') {
            return 'Vous êtes un expert en conception de CV et un conseiller professionnel spécialisé. Votre mission est d\'analyser le profil professionnel de l\'utilisateur et de fournir des recommandations précises pour les meilleurs modèles de CV qui conviennent à son domaine professionnel et à son niveau d\'expérience.

Analysez les informations fournies et fournissez:
1. Une analyse brève du profil professionnel
2. Les 3 meilleures recommandations de modèles avec des raisons détaillées
3. Des conseils pratiques pour choisir le bon modèle

Vos recommandations doivent être basées sur:
- La nature du domaine professionnel
- Le niveau d\'expérience
- Le type d\'industrie
- Les exigences du marché

Utilisez le format requis avec précision et rendez vos réponses utiles et pratiques.';
        } else {
            return 'You are an expert in CV design and a specialized career counselor. Your mission is to analyze the user\'s professional profile and provide accurate recommendations for the best CV templates that suit their professional field and experience level.

Analyze the provided information and provide:
1. Brief analysis of the professional profile
2. Top 3 template recommendations with detailed reasons
3. Practical tips for choosing the right template

Your recommendations should be based on:
- Nature of the professional field
- Experience level
- Industry type
- Market requirements

Use the required format accurately and make your answers useful and practical.';
        }
    }

    /**
     * Extract personality type from analysis
     */
    private function extractPersonalityType(string $analysis): string
    {
        // Simple extraction - can be enhanced with more sophisticated parsing
        if (preg_match('/نوع الشخصية[:\s]*([^\n]+)/i', $analysis, $matches)) {
            return trim($matches[1]);
        }
        return 'غير محدد';
    }

    /**
     * Extract strengths from analysis
     */
    private function extractStrengths(string $analysis): array
    {
        // Simple extraction - can be enhanced
        if (preg_match('/نقاط القوة[:\s]*([^\n]+)/i', $analysis, $matches)) {
            return array_map('trim', explode('،', $matches[1]));
        }
        return [];
    }

    /**
     * Extract recommendations from analysis
     */
    private function extractRecommendations(string $analysis): array
    {
        // Simple extraction - can be enhanced
        if (preg_match('/توصيات[:\s]*([^\n]+)/i', $analysis, $matches)) {
            return array_map('trim', explode('،', $matches[1]));
        }
        return [];
    }

    /**
     * Parse CV sections
     */
    private function parseCVSections(string $cvContent): array
    {
        $sections = [];

        // Extract summary
        if (preg_match('/(?:ملخص مهني|الملخص المهني|نبذة)[:\s]*\n([^#\n]+(?:\n[^#\n]+)*)/i', $cvContent, $matches)) {
            $sections['summary'] = trim($matches[1]);
        }

        // Extract skills
        if (preg_match('/(?:المهارات|مهارات)[:\s]*\n([^#\n]+(?:\n[^#\n]+)*)/i', $cvContent, $matches)) {
            $sections['skills'] = trim($matches[1]);
        }

        // Extract experience suggestions
        if (preg_match('/(?:الخبرات|خبرة|الخبرة العملية)[:\s]*\n([^#\n]+(?:\n[^#\n]+)*)/i', $cvContent, $matches)) {
            $sections['experience'] = trim($matches[1]);
        }

        // Extract education suggestions
        if (preg_match('/(?:التعليم|المؤهلات|التعليم والمؤهلات)[:\s]*\n([^#\n]+(?:\n[^#\n]+)*)/i', $cvContent, $matches)) {
            $sections['education'] = trim($matches[1]);
        }

        return $sections;
    }

    /**
     * Parse job recommendations
     */
    private function parseJobRecommendations(string $recommendations): array
    {
        // Parse job recommendations - can be enhanced
        $jobs = [];
        $lines = explode("\n", $recommendations);
        
        foreach ($lines as $line) {
            if (preg_match('/^\d+\.?\s*(.+)/', $line, $matches)) {
                $jobs[] = trim($matches[1]);
            }
        }
        
        return $jobs;
    }

    /**
     * Extract suggestions from chatbot response
     */
    private function extractSuggestions(string $response): array
    {
        // Extract actionable suggestions
        $suggestions = [];
        if (preg_match_all('/[•\-\*]\s*([^\n]+)/i', $response, $matches)) {
            $suggestions = array_map('trim', $matches[1]);
        }
        return $suggestions;
    }

    /**
     * Extract section from CV content
     */
    private function extractSection(string $content, string $sectionName): string
    {
        if (preg_match('/' . $sectionName . '[:\s]*([^\n]+)/i', $content, $matches)) {
            return trim($matches[1]);
        }
        return '';
    }

    /**
     * Improve CV content using AI
     */
    public function improveCVContent(string $cvContent, string $improvementType, string $language = 'ar'): array
    {
        try {
            $prompt = $this->buildCVImprovementPrompt($cvContent, $improvementType, $language);

            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('cv_improvement', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => $this->maxTokens,
                'temperature' => 0.3, // Lower temperature for more focused improvements
            ]);

            $improvedContent = $response->choices[0]->message->content;

            return [
                'success' => true,
                'improved_content' => $improvedContent,
                'suggestions' => $this->extractImprovementSuggestions($improvedContent),
            ];

        } catch (Exception $e) {
            Log::error('AI CV Improvement Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في تحسين السيرة الذاتية. يرجى المحاولة مرة أخرى.',
            ];
        }
    }

    /**
     * Get AI suggestions for specific CV sections
     */
    public function getCVSuggestions(string $section, string $currentContent, string $jobTitle, string $industry, string $language = 'ar'): array
    {
        try {
            $prompt = $this->buildSectionSuggestionPrompt($section, $currentContent, $jobTitle, $industry, $language);

            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('cv_suggestions', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => 800,
                'temperature' => 0.4,
            ]);

            $suggestions = $response->choices[0]->message->content;

            return [
                'success' => true,
                'suggestions' => $this->parseSuggestions($suggestions),
            ];

        } catch (Exception $e) {
            Log::error('AI CV Suggestions Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في الحصول على الاقتراحات. يرجى المحاولة مرة أخرى.',
            ];
        }
    }

    /**
     * Build CV improvement prompt
     */
    private function buildCVImprovementPrompt(string $cvContent, string $improvementType, string $language): string
    {
        $improvementTypes = [
            'summary' => 'تحسين الملخص المهني',
            'skills' => 'تحسين قسم المهارات',
            'experience' => 'تحسين قسم الخبرات',
            'overall' => 'تحسين شامل للسيرة الذاتية'
        ];

        $improvementName = $improvementTypes[$improvementType] ?? 'تحسين عام';

        return "يرجى {$improvementName} للسيرة الذاتية التالية:\n\n{$cvContent}\n\n" .
               "قم بتقديم نسخة محسنة مع التركيز على:\n" .
               "1. الوضوح والدقة\n" .
               "2. استخدام كلمات مفتاحية مهنية\n" .
               "3. تنظيم أفضل للمعلومات\n" .
               "4. إبراز نقاط القوة والإنجازات";
    }

    /**
     * Build section suggestion prompt
     */
    private function buildSectionSuggestionPrompt(string $section, string $currentContent, string $jobTitle, string $industry, string $language): string
    {
        $sectionNames = [
            'summary' => 'الملخص المهني',
            'skills' => 'المهارات',
            'experience' => 'الخبرات',
            'education' => 'التعليم'
        ];

        $sectionName = $sectionNames[$section] ?? $section;

        $prompt = "قم بتقديم اقتراحات لتحسين قسم {$sectionName} في السيرة الذاتية.\n\n";

        if ($currentContent) {
            $prompt .= "المحتوى الحالي:\n{$currentContent}\n\n";
        }

        if ($jobTitle) {
            $prompt .= "الوظيفة المستهدفة: {$jobTitle}\n";
        }

        if ($industry) {
            $prompt .= "المجال: {$industry}\n";
        }

        $prompt .= "\nيرجى تقديم:\n" .
                   "1. اقتراحات محددة للتحسين\n" .
                   "2. أمثلة على محتوى مناسب\n" .
                   "3. كلمات مفتاحية مهمة\n" .
                   "4. نصائح لإبراز نقاط القوة";

        return $prompt;
    }

    /**
     * Extract improvement suggestions
     */
    private function extractImprovementSuggestions(string $content): array
    {
        $suggestions = [];
        if (preg_match_all('/(?:اقتراح|نصيحة|توصية)[:\s]*([^\n]+)/i', $content, $matches)) {
            $suggestions = array_map('trim', $matches[1]);
        }
        return $suggestions;
    }

    /**
     * Parse suggestions from AI response
     */
    private function parseSuggestions(string $suggestions): array
    {
        $parsed = [];
        $lines = explode("\n", $suggestions);

        foreach ($lines as $line) {
            $line = trim($line);
            if (preg_match('/^[\d\-\*•]\s*(.+)/', $line, $matches)) {
                $parsed[] = trim($matches[1]);
            } elseif (!empty($line) && !preg_match('/^(اقتراحات|نصائح|توصيات)/', $line)) {
                $parsed[] = $line;
            }
        }

        return array_filter($parsed);
    }

    /**
     * Generate interview question based on type and context
     */
    public function generateInterviewQuestion($interviewType, $jobPosition, $previousQuestions = [], $previousAnswers = [])
    {
        try {
            $questionCount = count($previousQuestions);
            $prompt = $this->buildInterviewQuestionPrompt($interviewType, $jobPosition, $questionCount, $previousQuestions, $previousAnswers);

            // For demo, generate mock questions
            $questions = $this->getMockInterviewQuestions($interviewType, $jobPosition, $questionCount);

            if (isset($questions[$questionCount])) {
                return [
                    'success' => true,
                    'question' => $questions[$questionCount],
                    'type' => $interviewType
                ];
            }

            return [
                'success' => false,
                'error' => 'لا توجد أسئلة إضافية'
            ];

        } catch (Exception $e) {
            Log::error('AI Interview Question Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في توليد السؤال'
            ];
        }
    }

    /**
     * Evaluate interview answer
     */
    public function evaluateInterviewAnswer($question, $answer, $jobPosition, $interviewType)
    {
        try {
            // For demo, provide mock evaluation
            $score = rand(60, 95);
            $feedback = $this->generateMockFeedback($answer, $score);

            return [
                'success' => true,
                'feedback' => $feedback,
                'score' => $score
            ];

        } catch (Exception $e) {
            Log::error('AI Interview Evaluation Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في تقييم الإجابة'
            ];
        }
    }

    /**
     * Generate interview summary
     */
    public function generateInterviewSummary($questions, $answers, $jobPosition, $interviewType)
    {
        try {
            $totalScore = 0;
            $answerCount = count($answers);

            foreach ($answers as $answer) {
                $totalScore += $answer['score'] ?? 70;
            }

            $overallScore = $answerCount > 0 ? round($totalScore / $answerCount) : 0;

            return [
                'success' => true,
                'overall_score' => $overallScore,
                'scores_breakdown' => [
                    'communication' => rand(60, 90),
                    'technical_knowledge' => rand(65, 95),
                    'problem_solving' => rand(70, 90),
                    'cultural_fit' => rand(75, 95)
                ],
                'summary' => $this->generateMockSummary($overallScore, $answerCount),
                'recommendations' => $this->generateMockRecommendations($overallScore)
            ];

        } catch (Exception $e) {
            Log::error('AI Interview Summary Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في إنشاء الملخص'
            ];
        }
    }

    /**
     * Get mock interview questions
     */
    private function getMockInterviewQuestions($type, $jobPosition, $questionIndex)
    {
        $questions = [
            'general' => [
                'أخبرني عن نفسك وخبراتك المهنية.',
                'ما هي نقاط قوتك الرئيسية؟',
                'كيف تتعامل مع ضغط العمل والمواعيد النهائية؟',
                'أين ترى نفسك خلال 5 سنوات؟',
                'لماذا تريد العمل في هذه الشركة؟'
            ],
            'technical' => [
                'اشرح لي تقنية أو أداة تستخدمها في عملك.',
                'كيف تحل مشكلة تقنية معقدة؟',
                'ما هي أفضل الممارسات في مجال تخصصك؟',
                'اشرح مشروعاً تقنياً عملت عليه مؤخراً.',
                'كيف تبقى محدثاً بأحدث التقنيات؟'
            ],
            'behavioral' => [
                'اشرح موقفاً واجهت فيه تحدياً في العمل وكيف تعاملت معه.',
                'كيف تتعامل مع زميل صعب المراس؟',
                'اذكر مثالاً على وقت أخطأت فيه وماذا تعلمت.',
                'كيف تحفز نفسك وفريقك؟',
                'اشرح موقفاً أظهرت فيه قيادة.'
            ]
        ];

        return $questions[$type] ?? $questions['general'];
    }

    /**
     * Generate mock feedback
     */
    private function generateMockFeedback($answer, $score)
    {
        if ($score >= 85) {
            return 'إجابة ممتازة! أظهرت فهماً عميقاً وثقة في التعبير. استمر على هذا المستوى.';
        } elseif ($score >= 70) {
            return 'إجابة جيدة، لكن يمكن تحسينها بإضافة المزيد من التفاصيل والأمثلة العملية.';
        } else {
            return 'إجابة مقبولة، لكن تحتاج إلى المزيد من التطوير. حاول أن تكون أكثر تحديداً ووضوحاً.';
        }
    }

    /**
     * Generate mock summary
     */
    private function generateMockSummary($overallScore, $answerCount)
    {
        if ($overallScore >= 85) {
            return "أداء ممتاز في المقابلة! أجبت على {$answerCount} أسئلة بمستوى عالٍ من الاحترافية والثقة. تظهر مهارات تواصل قوية ومعرفة تقنية جيدة.";
        } elseif ($overallScore >= 70) {
            return "أداء جيد في المقابلة. أجبت على {$answerCount} أسئلة بشكل مناسب مع بعض النقاط التي يمكن تحسينها. تظهر إمكانيات واعدة.";
        } else {
            return "أداء مقبول في المقابلة. أجبت على {$answerCount} أسئلة لكن هناك مجال كبير للتحسين في التحضير والتعبير.";
        }
    }

    /**
     * Generate mock recommendations
     */
    private function generateMockRecommendations($overallScore)
    {
        if ($overallScore >= 85) {
            return 'استمر في تطوير مهاراتك وابق واثقاً من قدراتك. أنت مستعد لمقابلات العمل الحقيقية.';
        } elseif ($overallScore >= 70) {
            return 'ركز على تحسين مهارات التواصل وإضافة المزيد من الأمثلة العملية في إجاباتك. تدرب أكثر على الأسئلة الشائعة.';
        } else {
            return 'يُنصح بالمزيد من التحضير والتدريب. ادرس الأسئلة الشائعة وحضر أمثلة من خبراتك السابقة.';
        }
    }

    /**
     * Build interview question prompt
     */
    private function buildInterviewQuestionPrompt($type, $jobPosition, $questionCount, $previousQuestions, $previousAnswers)
    {
        $prompt = "أنت مُحاور خبير في مقابلات العمل. ";
        $prompt .= "نوع المقابلة: {$type}. ";

        if ($jobPosition) {
            $prompt .= "المنصب: {$jobPosition}. ";
        }

        $prompt .= "رقم السؤال: " . ($questionCount + 1) . ". ";
        $prompt .= "اطرح سؤالاً مناسباً ومهنياً باللغة العربية.";

        return $prompt;
    }

    /**
     * Customer service AI response
     */
    public function getCustomerServiceResponse($message, $language = 'ar')
    {
        try {
            // For demo, provide intelligent responses based on keywords
            $responses = $this->getCustomerServiceResponses($language);

            $message = strtolower($message);

            // Check for exact matches first
            foreach ($responses['exact'] as $keyword => $response) {
                if (strpos($message, $keyword) !== false) {
                    return [
                        'success' => true,
                        'response' => $response
                    ];
                }
            }

            // Check for category matches
            foreach ($responses['categories'] as $category => $data) {
                foreach ($data['keywords'] as $keyword) {
                    if (strpos($message, $keyword) !== false) {
                        return [
                            'success' => true,
                            'response' => $data['response']
                        ];
                    }
                }
            }

            // Default response
            return [
                'success' => true,
                'response' => $responses['default']
            ];

        } catch (Exception $e) {
            Log::error('Customer Service AI Error: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => 'حدث خطأ في الخدمة'
            ];
        }
    }

    /**
     * Get customer service responses by language
     */
    private function getCustomerServiceResponses($language)
    {
        if ($language === 'ar') {
            return [
                'exact' => [
                    'مرحبا' => 'مرحباً بك في MonOri AI! كيف يمكنني مساعدتك اليوم؟',
                    'شكرا' => 'العفو! سعيد لمساعدتك. هل تحتاج لأي شيء آخر؟',
                    'وداعا' => 'وداعاً! نتطلع لرؤيتك مرة أخرى. استمتع بخدماتنا!',
                ],
                'categories' => [
                    'services' => [
                        'keywords' => ['خدمات', 'ماذا تقدمون', 'ما هي خدماتكم', 'خدماتكم'],
                        'response' => 'نحن نقدم خدمات متنوعة في MonOri AI:\n• إنشاء السير الذاتية بالذكاء الاصطناعي\n• اختبارات تحليل الشخصية\n• مقابلات العمل التفاعلية\n• توصيات الوظائف المخصصة\n• تحسين الملف المهني'
                    ],
                    'cv' => [
                        'keywords' => ['سيرة ذاتية', 'cv', 'انشاء', 'كيف انشئ'],
                        'response' => 'إنشاء السيرة الذاتية سهل جداً! يمكنك:\n1. الذهاب إلى قسم "إنشاء السيرة الذاتية"\n2. ملء بياناتك الأساسية\n3. استخدام الذكاء الاصطناعي للحصول على محتوى احترافي\n4. اختيار التصميم المناسب\n5. تحميل السيرة الذاتية بصيغة PDF'
                    ],
                    'pricing' => [
                        'keywords' => ['اسعار', 'تكلفة', 'سعر', 'كم يكلف', 'مجاني'],
                        'response' => 'لدينا خطط متنوعة تناسب جميع الاحتياجات:\n• الخطة الأساسية: 19 درهم/شهر\n• الخطة الذهبية: 49 درهم/شهر\n• الخطة المميزة: 99 درهم/شهر\n\nكما نقدم خدمات منفردة مثل إنشاء السيرة الذاتية (30-80 درهم) والتدريب على المقابلات (70 درهم).'
                    ],
                    'personality' => [
                        'keywords' => ['شخصية', 'اختبار', 'تحليل شخصية'],
                        'response' => 'اختبار تحليل الشخصية يساعدك على:\n• فهم نقاط قوتك وضعفك\n• اكتشاف المهن المناسبة لشخصيتك\n• تطوير مهاراتك المهنية\n• الحصول على توصيات مخصصة\n\nالاختبار يستغرق 10-15 دقيقة ويقدم تحليلاً مفصلاً لشخصيتك المهنية.'
                    ],
                    'interview' => [
                        'keywords' => ['مقابلة', 'مقابلات عمل', 'تدريب'],
                        'response' => 'مقابلات العمل التفاعلية تتيح لك:\n• التدرب مع روبوت ذكي\n• الحصول على تقييم فوري لإجاباتك\n• تحسين مهارات التواصل\n• التحضير للمقابلات الحقيقية\n• اختيار نوع المقابلة (عامة، تقنية، سلوكية)'
                    ],
                    'account' => [
                        'keywords' => ['حساب', 'تسجيل', 'دخول', 'كلمة مرور'],
                        'response' => 'لإدارة حسابك:\n• يمكنك التسجيل مجاناً\n• تسجيل الدخول بالإيميل وكلمة المرور\n• تحديث بياناتك الشخصية\n• مراجعة نشاطاتك وإنجازاتك\n• إدارة اشتراكاتك'
                    ],
                    'support' => [
                        'keywords' => ['مساعدة', 'دعم', 'مشكلة', 'خطأ'],
                        'response' => 'نحن هنا لمساعدتك! يمكنك:\n• التواصل معنا عبر هذا الشات\n• إرسال إيميل لفريق الدعم\n• مراجعة الأسئلة الشائعة\n• مشاهدة الفيديوهات التعليمية\n\nفريقنا متاح 24/7 لحل أي مشكلة تواجهك.'
                    ]
                ],
                'default' => 'شكراً لسؤالك! أنا مساعد MonOri AI الذكي. يمكنني مساعدتك في:\n• معلومات عن خدماتنا\n• كيفية إنشاء السيرة الذاتية\n• أسعار الخدمات\n• اختبارات الشخصية\n• مقابلات العمل التفاعلية\n\nما الذي تود معرفته؟'
            ];
        }

        // English responses
        return [
            'exact' => [
                'hello' => 'Hello! Welcome to MonOri AI! How can I help you today?',
                'thanks' => 'You\'re welcome! Happy to help. Do you need anything else?',
                'goodbye' => 'Goodbye! We look forward to seeing you again. Enjoy our services!',
            ],
            'categories' => [
                'services' => [
                    'keywords' => ['services', 'what do you offer', 'your services'],
                    'response' => 'We offer various services at MonOri AI:\n• AI-powered CV creation\n• Personality analysis tests\n• Interactive job interviews\n• Personalized job recommendations\n• Professional profile enhancement'
                ],
                'cv' => [
                    'keywords' => ['resume', 'cv', 'create', 'how to create'],
                    'response' => 'Creating a CV is very easy! You can:\n1. Go to "CV Builder" section\n2. Fill in your basic information\n3. Use AI for professional content\n4. Choose the right design\n5. Download your CV as PDF'
                ]
            ],
            'default' => 'Thank you for your question! I\'m MonOri AI\'s smart assistant. I can help you with:\n• Information about our services\n• How to create CVs\n• Service pricing\n• Personality tests\n• Interactive job interviews\n\nWhat would you like to know?'
        ];
    }

    /**
     * Get AI template recommendations based on user profile
     */
    public function getTemplateRecommendations(string $jobField, string $experienceLevel, string $industryType, string $language = 'ar'): array
    {
        try {
            $prompt = $this->buildTemplateRecommendationPrompt($jobField, $experienceLevel, $industryType, $language);

            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('template_recommendation', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => 1500,
                'temperature' => 0.6,
            ]);

            $aiResponse = $response->choices[0]->message->content;

            // Parse AI response to extract recommendations
            $recommendations = $this->parseTemplateRecommendations($aiResponse, $language);

            // Generate compatibility scores
            $compatibilityScores = $this->generateCompatibilityScores($jobField, $experienceLevel, $industryType, $recommendations['templates']);

            return [
                'success' => true,
                'recommendations' => $recommendations['recommendations'],
                'recommended_templates' => $recommendations['templates'],
                'compatibility_scores' => $compatibilityScores,
                'analysis' => $recommendations['analysis'],
                'detailed_analysis' => $this->generateDetailedAnalysis($jobField, $experienceLevel, $industryType, $language)
            ];

        } catch (Exception $e) {
            Log::error('AI Template Recommendation Error', [
                'error' => $e->getMessage(),
                'job_field' => $jobField,
                'experience_level' => $experienceLevel,
                'industry_type' => $industryType
            ]);

            return [
                'success' => false,
                'error' => 'خطأ في خدمة الذكاء الاصطناعي: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Build template recommendation prompt
     */
    private function buildTemplateRecommendationPrompt(string $jobField, string $experienceLevel, string $industryType, string $language): string
    {
        $templates = [
            'modern' => [
                'name' => $language === 'ar' ? 'عصري' : ($language === 'fr' ? 'Moderne' : 'Modern'),
                'description' => $language === 'ar' ? 'تصميم نظيف وعصري مناسب لجميع المجالات' :
                               ($language === 'fr' ? 'Design propre et moderne adapté à tous les domaines' : 'Clean and modern design suitable for all fields'),
                'suitable_for' => $language === 'ar' ? 'التكنولوجيا، التسويق، الإدارة' :
                                 ($language === 'fr' ? 'Technologie, Marketing, Gestion' : 'Technology, Marketing, Management')
            ],
            'professional' => [
                'name' => $language === 'ar' ? 'مهني' : ($language === 'fr' ? 'Professionnel' : 'Professional'),
                'description' => $language === 'ar' ? 'تصميم مهني كلاسيكي مناسب للشركات' :
                               ($language === 'fr' ? 'Design professionnel classique adapté aux entreprises' : 'Classic professional design suitable for corporations'),
                'suitable_for' => $language === 'ar' ? 'المالية، القانون، الاستشارات' :
                                 ($language === 'fr' ? 'Finance, Droit, Conseil' : 'Finance, Law, Consulting')
            ],
            'creative' => [
                'name' => $language === 'ar' ? 'إبداعي' : ($language === 'fr' ? 'Créatif' : 'Creative'),
                'description' => $language === 'ar' ? 'تصميم إبداعي مناسب للمجالات الفنية والتقنية' :
                               ($language === 'fr' ? 'Design créatif adapté aux domaines artistiques et techniques' : 'Creative design suitable for artistic and technical fields'),
                'suitable_for' => $language === 'ar' ? 'التصميم، الفنون، الإعلام' :
                                 ($language === 'fr' ? 'Design, Arts, Médias' : 'Design, Arts, Media')
            ],
            'minimal' => [
                'name' => $language === 'ar' ? 'بسيط' : ($language === 'fr' ? 'Minimaliste' : 'Minimal'),
                'description' => $language === 'ar' ? 'تصميم بسيط وأنيق يركز على المحتوى' :
                               ($language === 'fr' ? 'Design simple et élégant axé sur le contenu' : 'Simple and elegant design focused on content'),
                'suitable_for' => $language === 'ar' ? 'الأكاديمية، البحث، التعليم' :
                                 ($language === 'fr' ? 'Académique, Recherche, Éducation' : 'Academic, Research, Education')
            ]
        ];

        if ($language === 'ar') {
            return "أنا أحتاج إلى توصيات لأفضل قوالب السيرة الذاتية بناءً على المعلومات التالية:

المجال المهني: {$jobField}
مستوى الخبرة: {$experienceLevel}
نوع الصناعة: {$industryType}

القوالب المتاحة:
" . json_encode($templates, JSON_UNESCAPED_UNICODE) . "

يرجى تقديم:
1. تحليل موجز للملف المهني
2. أفضل 3 توصيات للقوالب مع الأسباب
3. نصائح لاختيار القالب المناسب

تنسيق الإجابة:
ANALYSIS: [تحليل الملف المهني]
RECOMMENDATIONS: [قائمة التوصيات]
TEMPLATES: [أسماء القوالب المقترحة مفصولة بفواصل]";
        } elseif ($language === 'fr') {
            return "J'ai besoin de recommandations pour les meilleurs modèles de CV basés sur les informations suivantes:

Domaine professionnel: {$jobField}
Niveau d'expérience: {$experienceLevel}
Type d'industrie: {$industryType}

Modèles disponibles:
" . json_encode($templates, JSON_UNESCAPED_UNICODE) . "

Veuillez fournir:
1. Une analyse brève du profil professionnel
2. Les 3 meilleures recommandations de modèles avec les raisons
3. Des conseils pour choisir le bon modèle

Format de réponse:
ANALYSIS: [analyse du profil professionnel]
RECOMMENDATIONS: [liste des recommandations]
TEMPLATES: [noms des modèles suggérés séparés par des virgules]";
        } else {
            return "I need recommendations for the best CV templates based on the following information:

Job Field: {$jobField}
Experience Level: {$experienceLevel}
Industry Type: {$industryType}

Available Templates:
" . json_encode($templates, JSON_UNESCAPED_UNICODE) . "

Please provide:
1. Brief analysis of the professional profile
2. Top 3 template recommendations with reasons
3. Tips for choosing the right template

Response format:
ANALYSIS: [professional profile analysis]
RECOMMENDATIONS: [list of recommendations]
TEMPLATES: [suggested template names separated by commas]";
        }
    }

    /**
     * Parse AI template recommendations response
     */
    private function parseTemplateRecommendations(string $aiResponse, string $language): array
    {
        $recommendations = [];
        $templates = [];
        $analysis = '';

        // Extract analysis
        if (preg_match('/ANALYSIS:\s*(.+?)(?=RECOMMENDATIONS:|$)/s', $aiResponse, $matches)) {
            $analysis = trim($matches[1]);
        }

        // Extract recommendations
        if (preg_match('/RECOMMENDATIONS:\s*(.+?)(?=TEMPLATES:|$)/s', $aiResponse, $matches)) {
            $recommendationsText = trim($matches[1]);
            $lines = explode("\n", $recommendationsText);

            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line) && (strpos($line, '1.') !== false || strpos($line, '2.') !== false || strpos($line, '3.') !== false || strpos($line, '-') !== false)) {
                    $recommendations[] = [
                        'title' => $this->extractRecommendationTitle($line),
                        'description' => $this->extractRecommendationDescription($line),
                        'template' => $this->extractTemplateFromRecommendation($line)
                    ];
                }
            }
        }

        // Extract template names
        if (preg_match('/TEMPLATES:\s*(.+?)$/s', $aiResponse, $matches)) {
            $templatesText = trim($matches[1]);
            $templates = array_map('trim', explode(',', $templatesText));
            $templates = array_filter($templates, function($template) {
                return in_array(strtolower($template), ['modern', 'professional', 'creative', 'minimal']);
            });
        }

        // Fallback recommendations if parsing fails
        if (empty($recommendations)) {
            $recommendations = $this->getFallbackRecommendations($language);
        }

        if (empty($templates)) {
            $templates = ['modern', 'professional'];
        }

        return [
            'recommendations' => $recommendations,
            'templates' => $templates,
            'analysis' => $analysis ?: $this->getFallbackAnalysis($language)
        ];
    }

    /**
     * Extract recommendation title from line
     */
    private function extractRecommendationTitle(string $line): string
    {
        // Remove numbering and extract title
        $line = preg_replace('/^\d+\.\s*/', '', $line);
        $line = preg_replace('/^-\s*/', '', $line);

        // Get first sentence or up to colon
        if (strpos($line, ':') !== false) {
            return trim(explode(':', $line)[0]);
        }

        $sentences = explode('.', $line);
        return trim($sentences[0]);
    }

    /**
     * Extract recommendation description from line
     */
    private function extractRecommendationDescription(string $line): string
    {
        if (strpos($line, ':') !== false) {
            $parts = explode(':', $line, 2);
            return trim($parts[1]);
        }

        return $line;
    }

    /**
     * Extract template name from recommendation
     */
    private function extractTemplateFromRecommendation(string $line): ?string
    {
        $templates = ['modern', 'professional', 'creative', 'minimal'];
        $line = strtolower($line);

        foreach ($templates as $template) {
            if (strpos($line, $template) !== false) {
                return $template;
            }
        }

        return null;
    }

    /**
     * Get fallback recommendations
     */
    private function getFallbackRecommendations(string $language): array
    {
        if ($language === 'ar') {
            return [
                [
                    'title' => 'القالب العصري',
                    'description' => 'مناسب للمجالات التقنية والحديثة',
                    'template' => 'modern'
                ],
                [
                    'title' => 'القالب المهني',
                    'description' => 'مثالي للوظائف الإدارية والمالية',
                    'template' => 'professional'
                ],
                [
                    'title' => 'القالب الإبداعي',
                    'description' => 'رائع للمجالات الفنية والتصميم',
                    'template' => 'creative'
                ]
            ];
        } elseif ($language === 'fr') {
            return [
                [
                    'title' => 'Modèle Moderne',
                    'description' => 'Adapté aux domaines techniques et modernes',
                    'template' => 'modern'
                ],
                [
                    'title' => 'Modèle Professionnel',
                    'description' => 'Idéal pour les postes administratifs et financiers',
                    'template' => 'professional'
                ],
                [
                    'title' => 'Modèle Créatif',
                    'description' => 'Parfait pour les domaines artistiques et le design',
                    'template' => 'creative'
                ]
            ];
        } else {
            return [
                [
                    'title' => 'Modern Template',
                    'description' => 'Suitable for technical and modern fields',
                    'template' => 'modern'
                ],
                [
                    'title' => 'Professional Template',
                    'description' => 'Ideal for administrative and financial positions',
                    'template' => 'professional'
                ],
                [
                    'title' => 'Creative Template',
                    'description' => 'Great for artistic fields and design',
                    'template' => 'creative'
                ]
            ];
        }
    }

    /**
     * Get fallback analysis
     */
    private function getFallbackAnalysis(string $language): string
    {
        if ($language === 'ar') {
            return 'بناءً على المعلومات المقدمة، يمكننا اقتراح قوالب مناسبة لملفك المهني تساعدك في إبراز خبراتك ومهاراتك بأفضل شكل ممكن.';
        } elseif ($language === 'fr') {
            return 'Basé sur les informations fournies, nous pouvons suggérer des modèles adaptés à votre profil professionnel pour mettre en valeur vos expériences et compétences de la meilleure façon possible.';
        } else {
            return 'Based on the provided information, we can suggest suitable templates for your professional profile to showcase your experiences and skills in the best possible way.';
        }
    }

    /**
     * Generate compatibility scores for templates
     */
    private function generateCompatibilityScores(string $jobField, string $experienceLevel, string $industryType, array $recommendedTemplates): array
    {
        $scores = [];
        $baseScore = 95;

        // Define template-industry compatibility matrix
        $compatibilityMatrix = [
            'modern' => [
                'technology' => 95,
                'marketing' => 90,
                'engineering' => 85,
                'creative_arts' => 80,
                'default' => 75
            ],
            'professional' => [
                'finance' => 95,
                'healthcare' => 90,
                'education' => 85,
                'sales' => 80,
                'default' => 85
            ],
            'creative' => [
                'creative_arts' => 95,
                'marketing' => 90,
                'technology' => 85,
                'engineering' => 75,
                'default' => 70
            ],
            'minimal' => [
                'education' => 95,
                'healthcare' => 90,
                'finance' => 85,
                'technology' => 80,
                'default' => 80
            ]
        ];

        // Experience level modifiers
        $experienceModifiers = [
            'entry' => ['modern' => 5, 'creative' => 3, 'professional' => -2, 'minimal' => 0],
            'mid' => ['modern' => 3, 'creative' => 2, 'professional' => 2, 'minimal' => 1],
            'senior' => ['modern' => 0, 'creative' => -2, 'professional' => 5, 'minimal' => 3],
            'executive' => ['modern' => -3, 'creative' => -5, 'professional' => 8, 'minimal' => 5]
        ];

        foreach ($recommendedTemplates as $index => $template) {
            $score = $baseScore - ($index * 5); // Decrease score for lower priority templates

            // Apply industry compatibility
            if (isset($compatibilityMatrix[$template][$industryType])) {
                $score = $compatibilityMatrix[$template][$industryType];
            } elseif (isset($compatibilityMatrix[$template]['default'])) {
                $score = $compatibilityMatrix[$template]['default'];
            }

            // Apply experience level modifier
            if (isset($experienceModifiers[$experienceLevel][$template])) {
                $score += $experienceModifiers[$experienceLevel][$template];
            }

            // Ensure score is within valid range
            $score = max(60, min(100, $score));
            $scores[$template] = $score;
        }

        return $scores;
    }

    /**
     * Generate detailed analysis
     */
    private function generateDetailedAnalysis(string $jobField, string $experienceLevel, string $industryType, string $language): array
    {
        $analysis = [];

        if ($language === 'ar') {
            $analysis = [
                'profile_summary' => $this->generateProfileSummary($jobField, $experienceLevel, $industryType, $language),
                'template_reasoning' => $this->generateTemplateReasoning($jobField, $experienceLevel, $industryType, $language),
                'design_tips' => $this->generateDesignTips($jobField, $experienceLevel, $industryType, $language),
                'industry_insights' => $this->generateIndustryInsights($industryType, $language)
            ];
        } elseif ($language === 'fr') {
            $analysis = [
                'profile_summary' => $this->generateProfileSummary($jobField, $experienceLevel, $industryType, $language),
                'template_reasoning' => $this->generateTemplateReasoning($jobField, $experienceLevel, $industryType, $language),
                'design_tips' => $this->generateDesignTips($jobField, $experienceLevel, $industryType, $language),
                'industry_insights' => $this->generateIndustryInsights($industryType, $language)
            ];
        } else {
            $analysis = [
                'profile_summary' => $this->generateProfileSummary($jobField, $experienceLevel, $industryType, $language),
                'template_reasoning' => $this->generateTemplateReasoning($jobField, $experienceLevel, $industryType, $language),
                'design_tips' => $this->generateDesignTips($jobField, $experienceLevel, $industryType, $language),
                'industry_insights' => $this->generateIndustryInsights($industryType, $language)
            ];
        }

        return $analysis;
    }

    /**
     * Generate profile summary
     */
    private function generateProfileSummary(string $jobField, string $experienceLevel, string $industryType, string $language): string
    {
        if ($language === 'ar') {
            return "ملفك المهني في مجال {$jobField} بمستوى خبرة {$experienceLevel} في صناعة {$industryType} يتطلب قالب سيرة ذاتية يبرز خبراتك ومهاراتك بشكل احترافي.";
        } elseif ($language === 'fr') {
            return "Votre profil professionnel dans le domaine {$jobField} avec un niveau d'expérience {$experienceLevel} dans l'industrie {$industryType} nécessite un modèle de CV qui met en valeur vos expériences et compétences de manière professionnelle.";
        } else {
            return "Your professional profile in {$jobField} with {$experienceLevel} experience level in {$industryType} industry requires a CV template that showcases your experience and skills professionally.";
        }
    }

    /**
     * Generate template reasoning
     */
    private function generateTemplateReasoning(string $jobField, string $experienceLevel, string $industryType, string $language): string
    {
        // This would contain logic to explain why certain templates are recommended
        if ($language === 'ar') {
            return "القوالب المقترحة تم اختيارها بناءً على تحليل متطلبات مجالك المهني ومستوى خبرتك لضمان أفضل انطباع لدى أصحاب العمل.";
        } elseif ($language === 'fr') {
            return "Les modèles suggérés ont été choisis en fonction de l'analyse des exigences de votre domaine professionnel et de votre niveau d'expérience pour garantir la meilleure impression auprès des employeurs.";
        } else {
            return "The suggested templates were chosen based on analysis of your professional field requirements and experience level to ensure the best impression with employers.";
        }
    }

    /**
     * Generate design tips
     */
    private function generateDesignTips(string $jobField, string $experienceLevel, string $industryType, string $language): array
    {
        if ($language === 'ar') {
            return [
                'استخدم ألوان مناسبة لمجالك المهني',
                'اجعل المعلومات الأهم في المقدمة',
                'استخدم خطوط واضحة وسهلة القراءة',
                'نظم المحتوى بشكل منطقي ومتسلسل'
            ];
        } elseif ($language === 'fr') {
            return [
                'Utilisez des couleurs appropriées à votre domaine professionnel',
                'Mettez les informations les plus importantes en avant',
                'Utilisez des polices claires et faciles à lire',
                'Organisez le contenu de manière logique et séquentielle'
            ];
        } else {
            return [
                'Use colors appropriate for your professional field',
                'Put the most important information first',
                'Use clear and easy-to-read fonts',
                'Organize content logically and sequentially'
            ];
        }
    }

    /**
     * Generate industry insights
     */
    private function generateIndustryInsights(string $industryType, string $language): string
    {
        $insights = [
            'technology' => [
                'ar' => 'في مجال التكنولوجيا، يفضل أصحاب العمل السير الذاتية العصرية التي تظهر الإبداع والتقنية.',
                'fr' => 'Dans le domaine de la technologie, les employeurs préfèrent les CV modernes qui montrent la créativité et la technique.',
                'en' => 'In the technology field, employers prefer modern CVs that show creativity and technical skills.'
            ],
            'finance' => [
                'ar' => 'في القطاع المالي، تُقدر السير الذاتية المهنية والمحافظة التي تعكس الجدية والثقة.',
                'fr' => 'Dans le secteur financier, les CV professionnels et conservateurs qui reflètent le sérieux et la confiance sont appréciés.',
                'en' => 'In the financial sector, professional and conservative CVs that reflect seriousness and trust are valued.'
            ],
            'creative_arts' => [
                'ar' => 'في المجالات الإبداعية، السير الذاتية الملونة والمبتكرة تساعد في إظهار الشخصية الفنية.',
                'fr' => 'Dans les domaines créatifs, les CV colorés et innovants aident à montrer la personnalité artistique.',
                'en' => 'In creative fields, colorful and innovative CVs help showcase artistic personality.'
            ]
        ];

        $langKey = $language === 'ar' ? 'ar' : ($language === 'fr' ? 'fr' : 'en');
        return $insights[$industryType][$langKey] ?? $insights['technology'][$langKey];
    }

    /**
     * Generate job description using AI
     */
    public function generateJobDescription(string $jobTitle, string $company, string $language = 'ar'): string
    {
        try {
            $prompt = $this->buildJobDescriptionPrompt($jobTitle, $company, $language);

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('services.openai.api_key'),
                'Content-Type' => 'application/json',
            ])->timeout(30)->post('https://api.openai.com/v1/chat/completions', [
                'model' => 'gpt-3.5-turbo',
                'messages' => [
                    [
                        'role' => 'system',
                        'content' => 'You are a professional CV writing assistant. Generate concise, professional job descriptions.'
                    ],
                    [
                        'role' => 'user',
                        'content' => $prompt
                    ]
                ],
                'max_tokens' => 300,
                'temperature' => 0.7,
            ]);

            if ($response->successful()) {
                $data = $response->json();
                return trim($data['choices'][0]['message']['content'] ?? '');
            }

            throw new \Exception('OpenAI API request failed');

        } catch (\Exception $e) {
            \Log::error('AI Job Description Generation Error: ' . $e->getMessage());
            return $this->getFallbackJobDescription($jobTitle, $company, $language);
        }
    }

    /**
     * Build job description prompt
     */
    private function buildJobDescriptionPrompt(string $jobTitle, string $company, string $language): string
    {
        if ($language === 'ar') {
            return "اكتب وصف وظيفي مهني ومختصر لمنصب '{$jobTitle}' في شركة '{$company}'. يجب أن يتضمن المسؤوليات الرئيسية والإنجازات المحتملة. اكتب باللغة العربية في 3-4 نقاط.";
        } elseif ($language === 'fr') {
            return "Rédigez une description de poste professionnelle et concise pour le poste de '{$jobTitle}' chez '{$company}'. Elle doit inclure les principales responsabilités et réalisations potentielles. Écrivez en français en 3-4 points.";
        } else {
            return "Write a professional and concise job description for the position of '{$jobTitle}' at '{$company}'. It should include key responsibilities and potential achievements. Write in English in 3-4 bullet points.";
        }
    }

    /**
     * Get fallback job description
     */
    private function getFallbackJobDescription(string $jobTitle, string $company, string $language): string
    {
        if ($language === 'ar') {
            return "• العمل كـ {$jobTitle} في {$company}\n• تنفيذ المهام والمسؤوليات المطلوبة بكفاءة عالية\n• التعاون مع الفريق لتحقيق أهداف الشركة\n• المساهمة في تطوير وتحسين العمليات";
        } elseif ($language === 'fr') {
            return "• Travailler en tant que {$jobTitle} chez {$company}\n• Exécuter efficacement les tâches et responsabilités requises\n• Collaborer avec l'équipe pour atteindre les objectifs de l'entreprise\n• Contribuer au développement et à l'amélioration des processus";
        } else {
            return "• Work as {$jobTitle} at {$company}\n• Execute required tasks and responsibilities efficiently\n• Collaborate with team to achieve company objectives\n• Contribute to development and improvement of processes";
        }
    }

    /**
     * Get AI suggestions for specific CV sections
     */
    public function getSectionSuggestions(array $context): array
    {
        try {
            $section = $context['section'];
            $language = $context['language'] ?? 'ar';

            $prompt = $this->buildSectionSuggestionsPrompt($context, $language);

            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('section_suggestions', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => 1000,
                'temperature' => 0.8,
            ]);

            $suggestions = $response->choices[0]->message->content;

            // Parse suggestions into array
            return $this->parseSuggestions($suggestions);

        } catch (Exception $e) {
            Log::error('AI Section Suggestions Error: ' . $e->getMessage());
            return $this->getFallbackSuggestions($context['section'], $context['language'] ?? 'ar');
        }
    }

    /**
     * Review complete CV with AI
     */
    public function reviewCV(array $cvData, string $language = 'ar'): array
    {
        try {
            $prompt = $this->buildCVReviewPrompt($cvData, $language);

            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('cv_review', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => 1500,
                'temperature' => 0.6,
            ]);

            $review = $response->choices[0]->message->content;

            return $this->parseReview($review);

        } catch (Exception $e) {
            Log::error('AI CV Review Error: ' . $e->getMessage());
            return $this->getFallbackReview($language);
        }
    }

    /**
     * Optimize CV with AI suggestions
     */
    public function optimizeCV(array $cvData, string $language = 'ar'): array
    {
        try {
            $prompt = $this->buildCVOptimizationPrompt($cvData, $language);

            $response = OpenAI::chat()->create([
                'model' => $this->defaultModel,
                'messages' => [
                    ['role' => 'system', 'content' => $this->getSystemPrompt('cv_optimization', $language)],
                    ['role' => 'user', 'content' => $prompt]
                ],
                'max_tokens' => 2000,
                'temperature' => 0.7,
            ]);

            $optimizations = $response->choices[0]->message->content;

            return $this->parseOptimizations($optimizations);

        } catch (Exception $e) {
            Log::error('AI CV Optimization Error: ' . $e->getMessage());
            return $this->getFallbackOptimizations($language);
        }
    }

    /**
     * Build section suggestions prompt
     */
    private function buildSectionSuggestionsPrompt(array $context, string $language): string
    {
        $section = $context['section'];
        $currentContent = $context['current_content'] ?? '';
        $jobTitle = $context['job_title'] ?? '';
        $industry = $context['industry'] ?? '';

        if ($language === 'ar') {
            return "أحتاج اقتراحات لتحسين قسم '{$section}' في السيرة الذاتية.\n\nالمحتوى الحالي: {$currentContent}\nالمسمى الوظيفي: {$jobTitle}\nالمجال: {$industry}\n\nقدم 3-5 اقتراحات محددة وعملية لتحسين هذا القسم.";
        } elseif ($language === 'fr') {
            return "J'ai besoin de suggestions pour améliorer la section '{$section}' du CV.\n\nContenu actuel: {$currentContent}\nTitre du poste: {$jobTitle}\nDomaine: {$industry}\n\nFournissez 3-5 suggestions spécifiques et pratiques pour améliorer cette section.";
        } else {
            return "I need suggestions to improve the '{$section}' section of the CV.\n\nCurrent content: {$currentContent}\nJob title: {$jobTitle}\nIndustry: {$industry}\n\nProvide 3-5 specific and practical suggestions to improve this section.";
        }
    }

    /**
     * Build CV review prompt
     */
    private function buildCVReviewPrompt(array $cvData, string $language): string
    {
        $cvText = $this->formatCVDataForReview($cvData);

        if ($language === 'ar') {
            return "قم بمراجعة هذه السيرة الذاتية وقدم تحليلاً شاملاً:\n\n{$cvText}\n\nأريد تحليلاً يتضمن:\n1. نقاط القوة\n2. المجالات التي تحتاج تحسين\n3. نقاط عامة من 100";
        } elseif ($language === 'fr') {
            return "Veuillez examiner ce CV et fournir une analyse complète:\n\n{$cvText}\n\nJe veux une analyse qui inclut:\n1. Points forts\n2. Domaines nécessitant des améliorations\n3. Score global sur 100";
        } else {
            return "Please review this CV and provide a comprehensive analysis:\n\n{$cvText}\n\nI want an analysis that includes:\n1. Strengths\n2. Areas needing improvement\n3. Overall score out of 100";
        }
    }

    /**
     * Build CV optimization prompt
     */
    private function buildCVOptimizationPrompt(array $cvData, string $language): string
    {
        $cvText = $this->formatCVDataForReview($cvData);

        if ($language === 'ar') {
            return "قم بتحسين هذه السيرة الذاتية وقدم اقتراحات محددة:\n\n{$cvText}\n\nقدم تحسينات محددة لكل قسم مع النص المحسن.";
        } elseif ($language === 'fr') {
            return "Optimisez ce CV et fournissez des suggestions spécifiques:\n\n{$cvText}\n\nFournissez des améliorations spécifiques pour chaque section avec le texte amélioré.";
        } else {
            return "Optimize this CV and provide specific suggestions:\n\n{$cvText}\n\nProvide specific improvements for each section with the improved text.";
        }
    }



    /**
     * Parse AI review into structured format
     */
    private function parseReview(string $review): array
    {
        // Simple parsing - in production, you might want more sophisticated parsing
        return [
            'strengths' => $this->extractReviewSection($review, ['نقاط القوة', 'Strengths', 'Points forts']),
            'improvements' => $this->extractReviewSection($review, ['التحسينات', 'Improvements', 'Améliorations']),
            'score' => $this->extractScore($review)
        ];
    }

    /**
     * Parse AI optimizations into structured format
     */
    private function parseOptimizations(string $optimizations): array
    {
        // Simple parsing - return sections with optimized content
        return [
            'summary' => $this->extractOptimizedSection($optimizations, 'summary'),
            'experience' => $this->extractOptimizedSection($optimizations, 'experience'),
            'skills' => $this->extractOptimizedSection($optimizations, 'skills'),
            'education' => $this->extractOptimizedSection($optimizations, 'education')
        ];
    }

    /**
     * Extract section from review text
     */
    private function extractReviewSection(string $text, array $headers): string
    {
        foreach ($headers as $header) {
            if (stripos($text, $header) !== false) {
                $start = stripos($text, $header);
                $end = strpos($text, "\n\n", $start);
                if ($end === false) $end = strlen($text);

                return trim(substr($text, $start + strlen($header), $end - $start - strlen($header)));
            }
        }

        return 'لم يتم العثور على معلومات محددة';
    }

    /**
     * Extract score from review text
     */
    private function extractScore(string $text): int
    {
        if (preg_match('/(\d+)\/100|\b(\d+)\s*%/', $text, $matches)) {
            return (int)($matches[1] ?? $matches[2] ?? 75);
        }

        return 75; // Default score
    }

    /**
     * Extract optimized section
     */
    private function extractOptimizedSection(string $text, string $section): string
    {
        // Simple extraction - in production, implement more sophisticated parsing
        return "محتوى محسن لقسم {$section}";
    }

    /**
     * Format CV data for review
     */
    private function formatCVDataForReview(array $cvData): string
    {
        $formatted = "";

        if (isset($cvData['personal_info'])) {
            $formatted .= "المعلومات الشخصية:\n";
            foreach ($cvData['personal_info'] as $key => $value) {
                $formatted .= "- {$key}: {$value}\n";
            }
            $formatted .= "\n";
        }

        if (isset($cvData['summary'])) {
            $formatted .= "الملخص: {$cvData['summary']}\n\n";
        }

        return $formatted;
    }

    /**
     * Get fallback suggestions
     */
    private function getFallbackSuggestions(string $section, string $language): array
    {
        $suggestions = [
            'ar' => [
                'summary' => ['اكتب ملخصاً مهنياً قوياً', 'أضف كلمات مفتاحية متعلقة بمجالك', 'اذكر سنوات الخبرة'],
                'experience' => ['استخدم أرقام ونتائج قابلة للقياس', 'اذكر الإنجازات وليس المهام فقط', 'استخدم أفعال قوية'],
                'skills' => ['أضف مهارات تقنية حديثة', 'اذكر مستوى إتقانك لكل مهارة', 'أضف مهارات ناعمة مهمة'],
                'education' => ['اذكر المعدل إذا كان جيداً', 'أضف الدورات والشهادات', 'اذكر المشاريع الأكاديمية']
            ],
            'en' => [
                'summary' => ['Write a strong professional summary', 'Add keywords related to your field', 'Mention years of experience'],
                'experience' => ['Use measurable numbers and results', 'Mention achievements not just tasks', 'Use strong action verbs'],
                'skills' => ['Add modern technical skills', 'Mention your proficiency level', 'Add important soft skills'],
                'education' => ['Mention GPA if good', 'Add courses and certifications', 'Mention academic projects']
            ]
        ];

        return $suggestions[$language][$section] ?? $suggestions['en'][$section] ?? [];
    }

    /**
     * Get fallback review
     */
    private function getFallbackReview(string $language): array
    {
        if ($language === 'ar') {
            return [
                'strengths' => 'سيرة ذاتية منظمة مع معلومات أساسية واضحة',
                'improvements' => 'يمكن تحسين الملخص المهني وإضافة المزيد من التفاصيل',
                'score' => 75
            ];
        }

        return [
            'strengths' => 'Well-organized CV with clear basic information',
            'improvements' => 'Professional summary can be improved and more details added',
            'score' => 75
        ];
    }

    /**
     * Get fallback optimizations
     */
    private function getFallbackOptimizations(string $language): array
    {
        if ($language === 'ar') {
            return [
                'summary' => 'ملخص مهني محسن يبرز خبراتك ومهاراتك الأساسية',
                'experience' => 'وصف محسن للخبرات مع التركيز على الإنجازات',
                'skills' => 'قائمة محسنة بالمهارات مع تصنيفها حسب الأهمية',
                'education' => 'معلومات تعليمية محسنة مع إضافة التفاصيل المهمة'
            ];
        }

        return [
            'summary' => 'Enhanced professional summary highlighting your key experience and skills',
            'experience' => 'Improved experience descriptions focusing on achievements',
            'skills' => 'Enhanced skills list categorized by importance',
            'education' => 'Improved educational information with important details'
        ];
    }
}
