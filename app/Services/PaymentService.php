<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaidService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class PaymentService
{
    protected $stripeService;
    protected $paypalService;

    public function __construct()
    {
        // Initialize payment gateways when needed
    }

    /**
     * Create a subscription payment
     */
    public function createSubscriptionPayment(User $user, SubscriptionPlan $plan, string $paymentMethod = 'stripe', string $billingCycle = 'monthly'): array
    {
        try {
            $amount = $billingCycle === 'yearly' ? $plan->price_yearly : $plan->price_monthly;
            
            // Create subscription record
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'pending',
                'payment_method' => $paymentMethod,
                'amount' => $amount,
                'currency' => 'MAD',
                'billing_cycle' => $billingCycle,
                'starts_at' => now(),
                'ends_at' => $billingCycle === 'yearly' ? now()->addYear() : now()->addMonth(),
            ]);

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'payable_type' => Subscription::class,
                'payable_id' => $subscription->id,
                'payment_id' => 'pending_' . Str::random(16),
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'amount' => $amount,
                'currency' => 'MAD',
                'description' => "اشتراك {$plan->name} - {$billingCycle}",
            ]);

            // Process payment based on method
            if ($paymentMethod === 'stripe') {
                return $this->processStripePayment($payment, $subscription);
            } elseif ($paymentMethod === 'paypal') {
                return $this->processPayPalPayment($payment, $subscription);
            }

            return [
                'success' => false,
                'message' => 'طريقة دفع غير مدعومة'
            ];

        } catch (\Exception $e) {
            Log::error('Payment creation failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'حدث خطأ في إنشاء الدفعة'
            ];
        }
    }

    /**
     * Create a service payment
     */
    public function createServicePayment(User $user, PaidService $service, string $paymentMethod = 'stripe'): array
    {
        try {
            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'payable_type' => PaidService::class,
                'payable_id' => $service->id,
                'payment_id' => 'pending_' . Str::random(16),
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'amount' => $service->price,
                'currency' => 'MAD',
                'description' => "خدمة {$service->name}",
            ]);

            // Process payment based on method
            if ($paymentMethod === 'stripe') {
                return $this->processStripeServicePayment($payment, $service);
            } elseif ($paymentMethod === 'paypal') {
                return $this->processPayPalServicePayment($payment, $service);
            }

            return [
                'success' => false,
                'message' => 'طريقة دفع غير مدعومة'
            ];

        } catch (\Exception $e) {
            Log::error('Service payment creation failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'حدث خطأ في إنشاء الدفعة'
            ];
        }
    }

    /**
     * Process Stripe payment for subscription
     */
    protected function processStripePayment(Payment $payment, Subscription $subscription): array
    {
        // For now, return mock response - will implement actual Stripe integration
        return [
            'success' => true,
            'payment_url' => route('payment.stripe.checkout', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Process PayPal payment for subscription
     */
    protected function processPayPalPayment(Payment $payment, Subscription $subscription): array
    {
        // For now, return mock response - will implement actual PayPal integration
        return [
            'success' => true,
            'payment_url' => route('payment.paypal.checkout', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Process Stripe payment for service
     */
    protected function processStripeServicePayment(Payment $payment, PaidService $service): array
    {
        return [
            'success' => true,
            'payment_url' => route('payment.stripe.service', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Process PayPal payment for service
     */
    protected function processPayPalServicePayment(Payment $payment, PaidService $service): array
    {
        return [
            'success' => true,
            'payment_url' => route('payment.paypal.service', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Handle successful payment
     */
    public function handleSuccessfulPayment(Payment $payment, array $gatewayResponse = []): bool
    {
        try {
            // Update payment status
            $payment->update([
                'status' => 'completed',
                'paid_at' => now(),
                'gateway_response' => $gatewayResponse
            ]);

            // If it's a subscription payment, activate the subscription
            if ($payment->payable_type === Subscription::class) {
                $subscription = $payment->payable;
                $subscription->update([
                    'status' => 'active'
                ]);
            }

            Log::info('Payment completed successfully', [
                'payment_id' => $payment->id,
                'user_id' => $payment->user_id,
                'amount' => $payment->amount
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to handle successful payment: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle failed payment
     */
    public function handleFailedPayment(Payment $payment, string $reason = null, array $gatewayResponse = []): bool
    {
        try {
            $payment->update([
                'status' => 'failed',
                'failed_at' => now(),
                'failure_reason' => $reason,
                'gateway_response' => $gatewayResponse
            ]);

            Log::warning('Payment failed', [
                'payment_id' => $payment->id,
                'user_id' => $payment->user_id,
                'reason' => $reason
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to handle payment failure: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user's active subscription
     */
    public function getUserActiveSubscription(User $user): ?Subscription
    {
        return $user->subscriptions()
                   ->active()
                   ->with('subscriptionPlan')
                   ->first();
    }

    /**
     * Cancel user subscription
     */
    public function cancelSubscription(Subscription $subscription): bool
    {
        try {
            // Cancel at payment gateway if needed
            if ($subscription->stripe_subscription_id) {
                // Cancel Stripe subscription
            } elseif ($subscription->paypal_subscription_id) {
                // Cancel PayPal subscription
            }

            // Update local subscription
            $subscription->cancel();

            Log::info('Subscription cancelled', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription: ' . $e->getMessage());
            return false;
        }
    }
}
