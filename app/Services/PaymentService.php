<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Subscription;
use App\Models\User;
use App\Models\SubscriptionPlan;
use App\Models\PaidService;
use App\Models\PaymentTransaction;
use App\Models\AdminNotification;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PaymentService
{
    protected $stripeService;
    protected $paypalService;

    public function __construct()
    {
        // Initialize payment gateways when needed
    }

    /**
     * Create a subscription payment
     */
    public function createSubscriptionPayment(User $user, SubscriptionPlan $plan, string $paymentMethod = 'stripe', string $billingCycle = 'monthly'): array
    {
        try {
            $amount = $billingCycle === 'yearly' ? $plan->price_yearly : $plan->price_monthly;
            
            // Create subscription record
            $subscription = Subscription::create([
                'user_id' => $user->id,
                'subscription_plan_id' => $plan->id,
                'status' => 'pending',
                'payment_method' => $paymentMethod,
                'amount' => $amount,
                'currency' => 'MAD',
                'billing_cycle' => $billingCycle,
                'starts_at' => now(),
                'ends_at' => $billingCycle === 'yearly' ? now()->addYear() : now()->addMonth(),
            ]);

            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'payable_type' => Subscription::class,
                'payable_id' => $subscription->id,
                'payment_id' => 'pending_' . Str::random(16),
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'amount' => $amount,
                'currency' => 'MAD',
                'description' => "اشتراك {$plan->name} - {$billingCycle}",
            ]);

            // Process payment based on method
            if ($paymentMethod === 'stripe') {
                return $this->processStripePayment($payment, $subscription);
            } elseif ($paymentMethod === 'paypal') {
                return $this->processPayPalPayment($payment, $subscription);
            }

            return [
                'success' => false,
                'message' => 'طريقة دفع غير مدعومة'
            ];

        } catch (\Exception $e) {
            Log::error('Payment creation failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'حدث خطأ في إنشاء الدفعة'
            ];
        }
    }

    /**
     * Create a service payment
     */
    public function createServicePayment(User $user, PaidService $service, string $paymentMethod = 'stripe'): array
    {
        try {
            // Create payment record
            $payment = Payment::create([
                'user_id' => $user->id,
                'payable_type' => PaidService::class,
                'payable_id' => $service->id,
                'payment_id' => 'pending_' . Str::random(16),
                'payment_method' => $paymentMethod,
                'status' => 'pending',
                'amount' => $service->price,
                'currency' => 'MAD',
                'description' => "خدمة {$service->name}",
            ]);

            // Process payment based on method
            if ($paymentMethod === 'stripe') {
                return $this->processStripeServicePayment($payment, $service);
            } elseif ($paymentMethod === 'paypal') {
                return $this->processPayPalServicePayment($payment, $service);
            }

            return [
                'success' => false,
                'message' => 'طريقة دفع غير مدعومة'
            ];

        } catch (\Exception $e) {
            Log::error('Service payment creation failed: ' . $e->getMessage());
            
            return [
                'success' => false,
                'message' => 'حدث خطأ في إنشاء الدفعة'
            ];
        }
    }

    /**
     * Process Stripe payment for subscription
     */
    protected function processStripePayment(Payment $payment, Subscription $subscription): array
    {
        // For now, return mock response - will implement actual Stripe integration
        return [
            'success' => true,
            'payment_url' => route('payment.stripe.checkout', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Process PayPal payment for subscription
     */
    protected function processPayPalPayment(Payment $payment, Subscription $subscription): array
    {
        // For now, return mock response - will implement actual PayPal integration
        return [
            'success' => true,
            'payment_url' => route('payment.paypal.checkout', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Process Stripe payment for service
     */
    protected function processStripeServicePayment(Payment $payment, PaidService $service): array
    {
        return [
            'success' => true,
            'payment_url' => route('payment.stripe.service', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Process PayPal payment for service
     */
    protected function processPayPalServicePayment(Payment $payment, PaidService $service): array
    {
        return [
            'success' => true,
            'payment_url' => route('payment.paypal.service', ['payment' => $payment->id]),
            'payment_id' => $payment->id,
            'message' => 'تم إنشاء رابط الدفع بنجاح'
        ];
    }

    /**
     * Handle successful payment
     */
    public function handleSuccessfulPayment(Payment $payment, array $gatewayResponse = []): bool
    {
        try {
            // Update payment status
            $payment->update([
                'status' => 'completed',
                'paid_at' => now(),
                'gateway_response' => $gatewayResponse
            ]);

            // If it's a subscription payment, activate the subscription
            if ($payment->payable_type === Subscription::class) {
                $subscription = $payment->payable;
                $subscription->update([
                    'status' => 'active'
                ]);
            }

            Log::info('Payment completed successfully', [
                'payment_id' => $payment->id,
                'user_id' => $payment->user_id,
                'amount' => $payment->amount
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to handle successful payment: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Handle failed payment
     */
    public function handleFailedPayment(Payment $payment, string $reason = null, array $gatewayResponse = []): bool
    {
        try {
            $payment->update([
                'status' => 'failed',
                'failed_at' => now(),
                'failure_reason' => $reason,
                'gateway_response' => $gatewayResponse
            ]);

            Log::warning('Payment failed', [
                'payment_id' => $payment->id,
                'user_id' => $payment->user_id,
                'reason' => $reason
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to handle payment failure: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Get user's active subscription
     */
    public function getUserActiveSubscription(User $user): ?Subscription
    {
        return $user->subscriptions()
                   ->active()
                   ->with('subscriptionPlan')
                   ->first();
    }

    /**
     * Cancel user subscription
     */
    public function cancelSubscription(Subscription $subscription): bool
    {
        try {
            // Cancel at payment gateway if needed
            if ($subscription->stripe_subscription_id) {
                // Cancel Stripe subscription
            } elseif ($subscription->paypal_subscription_id) {
                // Cancel PayPal subscription
            }

            // Update local subscription
            $subscription->cancel();

            Log::info('Subscription cancelled', [
                'subscription_id' => $subscription->id,
                'user_id' => $subscription->user_id
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('Failed to cancel subscription: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * Create dynamic payment transaction
     */
    public function createTransaction(array $data): PaymentTransaction
    {
        return DB::transaction(function () use ($data) {
            $transaction = PaymentTransaction::create([
                'transaction_id' => $this->generateTransactionId(),
                'user_id' => $data['user_id'],
                'paid_service_id' => $data['service_id'],
                'payment_method' => $data['payment_method'],
                'amount' => $data['amount'],
                'currency' => $data['currency'] ?? 'MAD',
                'customer_name' => $data['customer_name'],
                'customer_email' => $data['customer_email'],
                'customer_phone' => $data['customer_phone'] ?? null,
                'status' => 'pending',
            ]);

            Log::info('Payment transaction created', [
                'transaction_id' => $transaction->transaction_id,
                'user_id' => $transaction->user_id,
                'amount' => $transaction->amount,
                'payment_method' => $transaction->payment_method,
            ]);

            return $transaction;
        });
    }

    /**
     * Process Stripe card payment
     */
    public function processStripeCardPayment(PaymentTransaction $transaction, array $paymentData): bool
    {
        try {
            $transaction->update([
                'status' => 'processing',
                'payment_details' => [
                    'card_last_four' => substr($paymentData['card_number'], -4),
                    'card_brand' => $this->detectCardBrand($paymentData['card_number']),
                    'cardholder_name' => $paymentData['cardholder_name'],
                ],
            ]);

            // Simulate payment success (90% success rate)
            $success = rand(1, 10) <= 9;

            if ($success) {
                $transaction->update([
                    'status' => 'completed',
                    'gateway_transaction_id' => 'stripe_' . Str::random(20),
                    'paid_at' => now(),
                    'gateway_response' => [
                        'status' => 'succeeded',
                        'payment_intent_id' => 'pi_' . Str::random(24),
                        'charge_id' => 'ch_' . Str::random(24),
                    ],
                ]);

                $this->createAdminNotification($transaction);
                return true;
            } else {
                $transaction->update([
                    'status' => 'failed',
                    'failed_at' => now(),
                    'failure_reason' => 'Card declined by issuer',
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('Stripe payment failed', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage(),
            ]);

            $transaction->update([
                'status' => 'failed',
                'failed_at' => now(),
                'failure_reason' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Process PayPal transaction payment
     */
    public function processPayPalTransactionPayment(PaymentTransaction $transaction, array $paymentData): bool
    {
        try {
            $transaction->update([
                'status' => 'processing',
                'payment_details' => [
                    'paypal_email' => $paymentData['paypal_email'],
                    'payer_id' => $paymentData['payer_id'] ?? null,
                ],
            ]);

            // Simulate PayPal payment success (95% success rate)
            $success = rand(1, 20) <= 19;

            if ($success) {
                $transaction->update([
                    'status' => 'completed',
                    'gateway_transaction_id' => 'paypal_' . Str::random(17),
                    'paid_at' => now(),
                    'gateway_response' => [
                        'status' => 'COMPLETED',
                        'payment_id' => 'PAYID-' . Str::upper(Str::random(26)),
                        'payer_id' => $paymentData['payer_id'] ?? 'PAYER' . Str::random(13),
                    ],
                ]);

                $this->createAdminNotification($transaction);
                return true;
            } else {
                $transaction->update([
                    'status' => 'failed',
                    'failed_at' => now(),
                    'failure_reason' => 'PayPal payment was declined',
                ]);
                return false;
            }
        } catch (\Exception $e) {
            Log::error('PayPal payment failed', [
                'transaction_id' => $transaction->transaction_id,
                'error' => $e->getMessage(),
            ]);

            $transaction->update([
                'status' => 'failed',
                'failed_at' => now(),
                'failure_reason' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Generate unique transaction ID
     */
    private function generateTransactionId(): string
    {
        return 'TXN_' . strtoupper(Str::random(12)) . '_' . time();
    }

    /**
     * Detect card brand from number
     */
    private function detectCardBrand(string $cardNumber): string
    {
        $cardNumber = preg_replace('/\D/', '', $cardNumber);

        if (preg_match('/^4/', $cardNumber)) return 'visa';
        if (preg_match('/^5[1-5]/', $cardNumber)) return 'mastercard';
        if (preg_match('/^3[47]/', $cardNumber)) return 'amex';
        if (preg_match('/^6(?:011|5)/', $cardNumber)) return 'discover';

        return 'unknown';
    }

    /**
     * Create admin notification for payment
     */
    private function createAdminNotification(PaymentTransaction $transaction): void
    {
        AdminNotification::createPaymentNotification($transaction);
    }

    /**
     * Get transactions by user
     */
    public function getTransactionsByUser(User $user)
    {
        return PaymentTransaction::where('user_id', $user->id)
            ->with('paidService')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get all transactions for admin
     */
    public function getAllTransactions()
    {
        return PaymentTransaction::with(['user', 'paidService'])
            ->orderBy('created_at', 'desc')
            ->paginate(20);
    }
}
