<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceResult extends Model
{
    protected $fillable = [
        'user_id',
        'service_type',
        'title',
        'description',
        'results',
        'metadata',
        'status',
        'score'
    ];

    protected $casts = [
        'results' => 'array',
        'metadata' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    // Helper methods
    public function getFormattedScoreAttribute(): string
    {
        if (!$this->score) return 'N/A';

        if ($this->score >= 90) return 'ممتاز';
        if ($this->score >= 80) return 'جيد جداً';
        if ($this->score >= 70) return 'جيد';
        if ($this->score >= 60) return 'مقبول';
        return 'يحتاج تحسين';
    }

    public function getStatusBadgeAttribute(): string
    {
        return match($this->status) {
            'completed' => 'green',
            'in_progress' => 'yellow',
            'failed' => 'red',
            default => 'gray'
        };
    }
}
