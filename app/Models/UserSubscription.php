<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserSubscription extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id', 'subscription_plan_id', 'billing_cycle',
        'amount_paid', 'currency', 'status', 'starts_at', 'ends_at',
        'cancelled_at', 'payment_method', 'transaction_id',
        'usage_limits', 'usage_current'
    ];

    protected $casts = [
        'amount_paid' => 'decimal:2',
        'starts_at' => 'datetime',
        'ends_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'usage_limits' => 'array',
        'usage_current' => 'array',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class);
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active')
                    ->where('ends_at', '>', now());
    }

    public function scopeExpired($query)
    {
        return $query->where('ends_at', '<=', now());
    }

    // Accessors
    public function getIsActiveAttribute()
    {
        return $this->status === 'active' && $this->ends_at > now();
    }

    public function getDaysRemainingAttribute()
    {
        if ($this->ends_at) {
            return max(0, now()->diffInDays($this->ends_at, false));
        }
        return 0;
    }

    public function getUsagePercentageAttribute()
    {
        $usage = $this->usage_current ?? [];
        $limits = $this->usage_limits ?? [];

        $percentages = [];
        foreach ($limits as $key => $limit) {
            if ($limit > 0) {
                $used = $usage[$key] ?? 0;
                $percentages[$key] = min(100, ($used / $limit) * 100);
            }
        }

        return $percentages;
    }

    // Methods
    public function canUseFeature($feature)
    {
        if (!$this->is_active) {
            return false;
        }

        $limits = $this->usage_limits ?? [];
        $current = $this->usage_current ?? [];

        // Check boolean features
        if (in_array($feature, ['pdf_export', 'voice_interviews', 'personal_coaching', 'priority_support'])) {
            return $limits[$feature] ?? false;
        }

        // Check usage-based features
        if (isset($limits[$feature]) && isset($current[$feature])) {
            return $current[$feature] < $limits[$feature];
        }

        return false;
    }

    public function incrementUsage($feature, $amount = 1)
    {
        $current = $this->usage_current ?? [];
        $current[$feature] = ($current[$feature] ?? 0) + $amount;

        $this->update(['usage_current' => $current]);

        return $this;
    }
}
