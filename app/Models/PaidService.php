<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class PaidService extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'name_en', 'name_ar', 'name_fr',
        'description', 'description_en', 'description_ar', 'description_fr',
        'type', 'price', 'currency', 'delivery_time_hours',
        'features', 'features_en', 'features_ar', 'features_fr',
        'requires_ai', 'requires_human_review',
        'is_active', 'sort_order'
    ];

    protected $casts = [
        'features' => 'array',
        'features_en' => 'array',
        'features_ar' => 'array',
        'features_fr' => 'array',
        'price' => 'decimal:2',
        'requires_ai' => 'boolean',
        'requires_human_review' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Accessors
    public function getLocalizedFeaturesAttribute()
    {
        $locale = app()->getLocale();
        return $this->{"features_$locale"} ?? $this->features ?? [];
    }
}
