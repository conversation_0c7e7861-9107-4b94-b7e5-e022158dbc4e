<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;
use Carbon\Carbon;

class BlogPost extends Model
{
    protected $fillable = [
        'title',
        'title_ar',
        'title_en',
        'title_fr',
        'slug',
        'excerpt',
        'excerpt_ar',
        'excerpt_en',
        'excerpt_fr',
        'content',
        'content_ar',
        'content_en',
        'content_fr',
        'featured_image',
        'status',
        'category',
        'tags',
        'seo_meta',
        'views_count',
        'reading_time',
        'author_id',
        'published_at'
    ];

    protected $casts = [
        'tags' => 'array',
        'seo_meta' => 'array',
        'published_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();

        static::creating(function ($post) {
            if (empty($post->slug)) {
                $post->slug = Str::slug($post->title);
            }

            if (empty($post->reading_time)) {
                $post->reading_time = self::calculateReadingTime($post->content);
            }
        });
    }

    public function author(): BelongsTo
    {
        return $this->belongsTo(User::class, 'author_id');
    }

    // Scopes
    public function scopePublished($query)
    {
        return $query->where('status', 'published')
                    ->where('published_at', '<=', now());
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeRecent($query, $limit = 5)
    {
        return $query->orderBy('published_at', 'desc')->limit($limit);
    }

    public function scopePopular($query, $limit = 5)
    {
        return $query->orderBy('views_count', 'desc')->limit($limit);
    }

    // Helper methods
    public function isPublished(): bool
    {
        return $this->status === 'published' &&
               $this->published_at &&
               $this->published_at <= now();
    }

    public function getLocalizedTitle(): string
    {
        $locale = app()->getLocale();
        return $this->{"title_$locale"} ?? $this->title;
    }

    public function getLocalizedExcerpt(): string
    {
        $locale = app()->getLocale();
        return $this->{"excerpt_$locale"} ?? $this->excerpt;
    }

    public function getLocalizedContent(): string
    {
        $locale = app()->getLocale();
        return $this->{"content_$locale"} ?? $this->content;
    }

    public function incrementViews(): void
    {
        $this->increment('views_count');
    }

    public function getFormattedPublishedDate(): string
    {
        return $this->published_at ? $this->published_at->format('Y-m-d') : '';
    }

    public function getCategoryLabel(): string
    {
        $categories = [
            'career_guidance' => 'التوجيه المهني',
            'interview_tips' => 'نصائح المقابلات',
            'job_market' => 'سوق العمل',
            'success_stories' => 'قصص النجاح',
            'ai_insights' => 'رؤى الذكاء الاصطناعي'
        ];

        return $categories[$this->category] ?? $this->category;
    }

    public static function calculateReadingTime(string $content): int
    {
        $wordCount = str_word_count(strip_tags($content));
        $wordsPerMinute = 200; // Average reading speed
        return max(1, ceil($wordCount / $wordsPerMinute));
    }

    public function getRouteKeyName(): string
    {
        return 'slug';
    }
}
