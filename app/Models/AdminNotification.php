<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AdminNotification extends Model
{
    use HasFactory;

    protected $fillable = [
        'type',
        'title',
        'message',
        'data',
        'is_read',
        'read_at',
    ];

    protected $casts = [
        'data' => 'array',
        'is_read' => 'boolean',
        'read_at' => 'datetime',
    ];

    public function markAsRead(): void
    {
        $this->update([
            'is_read' => true,
            'read_at' => now(),
        ]);
    }

    public function scopeUnread($query)
    {
        return $query->where('is_read', false);
    }

    public function scopeRead($query)
    {
        return $query->where('is_read', true);
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    public static function createPaymentNotification(PaymentTransaction $transaction): self
    {
        return self::create([
            'type' => 'payment_received',
            'title' => 'New Payment Received',
            'message' => "Payment of {$transaction->formatted_amount} received from {$transaction->customer_name} for {$transaction->paidService->name}",
            'data' => [
                'transaction_id' => $transaction->id,
                'user_id' => $transaction->user_id,
                'service_id' => $transaction->paid_service_id,
                'amount' => $transaction->amount,
                'payment_method' => $transaction->payment_method,
            ],
        ]);
    }
}
