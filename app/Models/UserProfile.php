<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserProfile extends Model
{
    protected $fillable = [
        'user_id',
        'phone',
        'birth_date',
        'gender',
        'city',
        'country',
        'bio',
        'education_level',
        'field_of_study',
        'current_job',
        'experience_years',
        'skills',
        'interests',
        'linkedin_url',
        'github_url',
        'portfolio_url',
        'avatar',
    ];

    protected $casts = [
        'birth_date' => 'date',
        'skills' => 'array',
        'interests' => 'array',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getAgeAttribute()
    {
        return $this->birth_date ? $this->birth_date->age : null;
    }

    public function getSkillsListAttribute()
    {
        return $this->skills ? implode(', ', $this->skills) : '';
    }

    public function getInterestsListAttribute()
    {
        return $this->interests ? implode(', ', $this->interests) : '';
    }
}
