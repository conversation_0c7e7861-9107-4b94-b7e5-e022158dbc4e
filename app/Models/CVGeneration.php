<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CVGeneration extends Model
{
    use HasFactory;

    protected $table = 'cv_generations';

    protected $fillable = [
        'user_id',
        'original_data',
        'enhanced_data',
        'cv_language',
        'status',
        'pdf_path'
    ];

    protected $casts = [
        'original_data' => 'array',
        'enhanced_data' => 'array',
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function getFormattedCreatedAtAttribute()
    {
        return $this->created_at->format('Y-m-d H:i:s');
    }

    public function getLanguageNameAttribute()
    {
        $languages = [
            'en' => 'English',
            'ar' => 'العربية',
            'fr' => 'Français'
        ];

        return $languages[$this->cv_language] ?? 'Unknown';
    }
}
