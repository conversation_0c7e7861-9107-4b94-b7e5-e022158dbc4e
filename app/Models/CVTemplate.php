<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class CVTemplate extends Model
{
    protected $table = 'cv_templates';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'preview_image',
        'style',
        'layout',
        'color_scheme',
        'is_premium',
        'is_active',
        'sort_order'
    ];

    protected $casts = [
        'color_scheme' => 'array',
        'is_premium' => 'boolean',
        'is_active' => 'boolean'
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeFree($query)
    {
        return $query->where('is_premium', false);
    }

    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }
}
