<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ServiceOrder extends Model
{
    use HasFactory;

    protected $fillable = [
        'order_number', 'user_id', 'paid_service_id', 'amount', 'currency',
        'status', 'payment_status', 'payment_method', 'transaction_id',
        'service_data', 'delivery_files', 'customer_notes', 'admin_notes',
        'paid_at', 'started_at', 'completed_at', 'due_date'
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'service_data' => 'array',
        'delivery_files' => 'array',
        'paid_at' => 'datetime',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'due_date' => 'datetime',
    ];

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function paidService(): BelongsTo
    {
        return $this->belongsTo(PaidService::class);
    }

    // Scopes
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('payment_status', 'paid');
    }

    public function scopeCompleted($query)
    {
        return $query->where('status', 'completed');
    }

    // Accessors
    public function getStatusBadgeAttribute()
    {
        $badges = [
            'pending' => ['class' => 'bg-yellow-100 text-yellow-800', 'text' => 'في الانتظار'],
            'paid' => ['class' => 'bg-blue-100 text-blue-800', 'text' => 'مدفوع'],
            'processing' => ['class' => 'bg-purple-100 text-purple-800', 'text' => 'قيد التنفيذ'],
            'completed' => ['class' => 'bg-green-100 text-green-800', 'text' => 'مكتمل'],
            'cancelled' => ['class' => 'bg-red-100 text-red-800', 'text' => 'ملغي'],
            'refunded' => ['class' => 'bg-gray-100 text-gray-800', 'text' => 'مسترد'],
        ];

        return $badges[$this->status] ?? $badges['pending'];
    }

    public function getIsOverdueAttribute()
    {
        return $this->due_date && $this->due_date < now() && !in_array($this->status, ['completed', 'cancelled']);
    }
}
