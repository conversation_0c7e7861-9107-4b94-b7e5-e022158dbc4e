<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'is_admin',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_admin' => 'boolean',
        ];
    }

    /**
     * Get the user's profile.
     */
    public function profile(): HasOne
    {
        return $this->hasOne(UserProfile::class);
    }

    /**
     * Check if user is admin.
     */
    public function isAdmin(): bool
    {
        return $this->is_admin === true;
    }

    /**
     * Make user admin.
     */
    public function makeAdmin(): void
    {
        $this->update(['is_admin' => true]);
    }

    /**
     * Remove admin privileges.
     */
    public function removeAdmin(): void
    {
        $this->update(['is_admin' => false]);
    }

    /**
     * Get user actions.
     */
    public function actions(): HasMany
    {
        return $this->hasMany(UserAction::class);
    }

    public function interviews()
    {
        return $this->hasMany(Interview::class);
    }

    /**
     * Get recent actions.
     */
    public function recentActions($limit = 5): HasMany
    {
        return $this->actions()->latest()->take($limit);
    }

    /**
     * Get the user's activities.
     */
    public function activities(): HasMany
    {
        return $this->hasMany(UserActivity::class);
    }

    /**
     * Get the user's assessments.
     */
    public function assessments(): HasMany
    {
        return $this->hasMany(UserAssessment::class);
    }

    /**
     * Get the user's service results.
     */
    public function serviceResults(): HasMany
    {
        return $this->hasMany(ServiceResult::class);
    }

    /**
     * Get the user's payment transactions.
     */
    public function paymentTransactions(): HasMany
    {
        return $this->hasMany(PaymentTransaction::class);
    }

    /**
     * Get the user's subscriptions.
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(Subscription::class);
    }

    /**
     * Get completed activities.
     */
    public function completedActivities(): HasMany
    {
        return $this->activities()->completed();
    }

    /**
     * Get in-progress activities.
     */
    public function inProgressActivities(): HasMany
    {
        return $this->activities()->inProgress();
    }

    /**
     * Get completed assessments.
     */
    public function completedAssessments(): HasMany
    {
        return $this->assessments()->completed();
    }

    /**
     * Get user's progress statistics.
     */
    public function getProgressStatsAttribute()
    {
        return [
            'total_activities' => $this->activities()->count(),
            'completed_activities' => $this->completedActivities()->count(),
            'in_progress_activities' => $this->inProgressActivities()->count(),
            'total_assessments' => $this->assessments()->count(),
            'completed_assessments' => $this->completedAssessments()->count(),
            'overall_progress' => $this->calculateOverallProgress(),
        ];
    }

    /**
     * Calculate overall progress percentage.
     */
    public function calculateOverallProgress()
    {
        $totalActivities = $this->activities()->count();
        if ($totalActivities === 0) return 0;

        $completedActivities = $this->completedActivities()->count();
        return round(($completedActivities / $totalActivities) * 100);
    }
}
