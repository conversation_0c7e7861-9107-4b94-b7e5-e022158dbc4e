<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class UserAssessment extends Model
{
    protected $fillable = [
        'user_id',
        'type',
        'title',
        'questions',
        'answers',
        'results',
        'score',
        'recommendations',
        'completed_at',
    ];

    protected $casts = [
        'questions' => 'array',
        'answers' => 'array',
        'results' => 'array',
        'completed_at' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeCompleted($query)
    {
        return $query->whereNotNull('completed_at');
    }

    public function scopeByType($query, $type)
    {
        return $query->where('type', $type);
    }

    public function getIsCompletedAttribute()
    {
        return !is_null($this->completed_at);
    }

    public function getScorePercentageAttribute()
    {
        return $this->score ? min(100, max(0, $this->score)) : 0;
    }

    public function getScoreBadgeAttribute()
    {
        if (!$this->score) return 'secondary';

        return match(true) {
            $this->score >= 90 => 'success',
            $this->score >= 70 => 'warning',
            $this->score >= 50 => 'info',
            default => 'danger'
        };
    }
}
