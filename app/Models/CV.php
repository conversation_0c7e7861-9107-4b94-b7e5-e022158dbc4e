<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class CV extends Model
{
    protected $fillable = [
        'user_id',
        'title',
        'template',
        'personal_info',
        'summary',
        'experience',
        'education',
        'skills',
        'languages',
        'certifications',
        'data',
        'status',
        'last_accessed_at',
        'download_count'
    ];

    protected $casts = [
        'personal_info' => 'array',
        'experience' => 'array',
        'education' => 'array',
        'skills' => 'array',
        'data' => 'array',
        'languages' => 'array',
        'certifications' => 'array',
        'last_accessed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * Get the user that owns the CV
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope for published CVs
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope for draft CVs
     */
    public function scopeDraft($query)
    {
        return $query->where('status', 'draft');
    }

    /**
     * Get available templates
     */
    public static function getAvailableTemplates()
    {
        return [
            'modern' => [
                'name' => __('messages.template_modern'),
                'description' => __('messages.template_modern_desc'),
                'preview' => '/images/templates/modern-preview.jpg',
                'type' => 'modern',
                'suitable_for' => __('messages.technology') . ', ' . __('messages.marketing') . ', ' . __('messages.engineering')
            ],
            'professional' => [
                'name' => __('messages.template_professional'),
                'description' => __('messages.template_professional_desc'),
                'preview' => '/images/templates/professional-preview.jpg',
                'type' => 'professional',
                'suitable_for' => __('messages.finance') . ', ' . __('messages.healthcare') . ', ' . __('messages.education')
            ],
            'creative' => [
                'name' => __('messages.template_creative'),
                'description' => __('messages.template_creative_desc'),
                'preview' => '/images/templates/creative-preview.jpg',
                'type' => 'creative',
                'suitable_for' => __('messages.creative_arts') . ', ' . __('messages.marketing') . ', ' . __('messages.technology')
            ],
            'minimal' => [
                'name' => __('messages.template_minimal'),
                'description' => __('messages.template_minimal_desc'),
                'preview' => '/images/templates/minimal-preview.jpg',
                'type' => 'minimal',
                'suitable_for' => __('messages.education') . ', ' . __('messages.healthcare') . ', ' . __('messages.finance')
            ]
        ];
    }

    /**
     * Increment download count
     */
    public function incrementDownloadCount()
    {
        $this->increment('download_count');
        $this->update(['last_accessed_at' => now()]);
    }

    /**
     * Get full name from personal info
     */
    public function getFullNameAttribute()
    {
        return $this->personal_info['full_name'] ?? __('messages.not_specified');
    }

    /**
     * Get email from personal info
     */
    public function getEmailAttribute()
    {
        return $this->personal_info['email'] ?? null;
    }

    /**
     * Check if CV is complete
     */
    public function isComplete()
    {
        return !empty($this->personal_info['full_name']) &&
               !empty($this->personal_info['email']) &&
               !empty($this->personal_info['phone']);
    }
}
