<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;

class SubscriptionPlan extends Model
{
    use HasFactory;

    protected $fillable = [
        'name', 'name_en', 'name_ar', 'name_fr',
        'description', 'description_en', 'description_ar', 'description_fr',
        'price_monthly', 'price_yearly', 'currency',
        'features', 'features_en', 'features_ar', 'features_fr',
        'ai_requests_limit', 'cv_generations_limit',
        'pdf_export', 'voice_interviews', 'personal_coaching', 'priority_support',
        'is_active', 'sort_order'
    ];

    protected $casts = [
        'features' => 'array',
        'features_en' => 'array',
        'features_ar' => 'array',
        'features_fr' => 'array',
        'price_monthly' => 'decimal:2',
        'price_yearly' => 'decimal:2',
        'pdf_export' => 'boolean',
        'voice_interviews' => 'boolean',
        'personal_coaching' => 'boolean',
        'priority_support' => 'boolean',
        'is_active' => 'boolean',
    ];

    // Relationships
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class);
    }

    public function activeSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class)->where('status', 'active');
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    // Accessors
    public function getLocalizedNameAttribute()
    {
        $locale = app()->getLocale();
        return $this->{"name_$locale"} ?? $this->name;
    }

    public function getLocalizedDescriptionAttribute()
    {
        $locale = app()->getLocale();
        return $this->{"description_$locale"} ?? $this->description;
    }

    public function getLocalizedFeaturesAttribute()
    {
        $locale = app()->getLocale();
        return $this->{"features_$locale"} ?? $this->features ?? [];
    }

    public function getMonthlyPriceFormattedAttribute()
    {
        return number_format($this->price_monthly, 2) . ' ' . $this->currency;
    }

    public function getYearlyPriceFormattedAttribute()
    {
        return number_format($this->price_yearly, 2) . ' ' . $this->currency;
    }

    public function getYearlySavingsAttribute()
    {
        $monthlyTotal = $this->price_monthly * 12;
        return $monthlyTotal - $this->price_yearly;
    }

    public function getYearlySavingsPercentageAttribute()
    {
        $monthlyTotal = $this->price_monthly * 12;
        if ($monthlyTotal > 0) {
            return round((($monthlyTotal - $this->price_yearly) / $monthlyTotal) * 100);
        }
        return 0;
    }
}
