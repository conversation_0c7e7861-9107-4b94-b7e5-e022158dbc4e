<?php

return [
    /*
    |--------------------------------------------------------------------------
    | AI Service Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for AI services used in MonOri AI platform
    |
    */

    'default_model' => env('AI_DEFAULT_MODEL', 'gpt-3.5-turbo'),
    'max_tokens' => env('AI_MAX_TOKENS', 2000),
    'temperature' => env('AI_TEMPERATURE', 0.7),

    /*
    |--------------------------------------------------------------------------
    | OpenAI Configuration
    |--------------------------------------------------------------------------
    */
    'openai' => [
        'api_key' => env('OPENAI_API_KEY'),
        'organization' => env('OPENAI_ORGANIZATION'),
        'models' => [
            'chat' => env('OPENAI_CHAT_MODEL', 'gpt-3.5-turbo'),
            'completion' => env('OPENAI_COMPLETION_MODEL', 'text-davinci-003'),
            'embedding' => env('OPENAI_EMBEDDING_MODEL', 'text-embedding-ada-002'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Personality Analysis Settings
    |--------------------------------------------------------------------------
    */
    'personality' => [
        'max_questions' => 20,
        'analysis_depth' => 'detailed', // basic, detailed, comprehensive
        'include_career_suggestions' => true,
        'include_strengths_weaknesses' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | CV Generation Settings
    |--------------------------------------------------------------------------
    */
    'cv' => [
        'templates' => ['modern', 'classic', 'creative'],
        'default_template' => 'modern',
        'max_sections' => 10,
        'include_ai_suggestions' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Job Recommendation Settings
    |--------------------------------------------------------------------------
    */
    'jobs' => [
        'max_recommendations' => 10,
        'include_salary_estimates' => true,
        'include_skill_gaps' => true,
        'market_data_source' => 'ai_analysis', // ai_analysis, external_api
    ],

    /*
    |--------------------------------------------------------------------------
    | ChatBot Settings
    |--------------------------------------------------------------------------
    */
    'chatbot' => [
        'max_conversation_length' => 50,
        'response_style' => 'professional', // casual, professional, friendly
        'include_suggestions' => true,
        'context_memory' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting
    |--------------------------------------------------------------------------
    */
    'rate_limits' => [
        'personality_analysis' => [
            'per_user_per_day' => 5,
            'per_user_per_hour' => 2,
        ],
        'cv_generation' => [
            'per_user_per_day' => 10,
            'per_user_per_hour' => 3,
        ],
        'job_recommendations' => [
            'per_user_per_day' => 20,
            'per_user_per_hour' => 5,
        ],
        'chatbot' => [
            'per_user_per_day' => 100,
            'per_user_per_hour' => 20,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Caching Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'enabled' => true,
        'ttl' => [
            'personality_analysis' => 3600, // 1 hour
            'cv_generation' => 1800, // 30 minutes
            'job_recommendations' => 7200, // 2 hours
            'chatbot_context' => 1800, // 30 minutes
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Fallback Settings
    |--------------------------------------------------------------------------
    */
    'fallback' => [
        'enabled' => true,
        'use_cached_responses' => true,
        'default_responses' => [
            'personality_analysis_error' => 'عذراً، حدث خطأ في تحليل الشخصية. يرجى المحاولة مرة أخرى لاحقاً.',
            'cv_generation_error' => 'عذراً، حدث خطأ في إنشاء السيرة الذاتية. يرجى المحاولة مرة أخرى لاحقاً.',
            'job_recommendation_error' => 'عذراً، حدث خطأ في توصيات الوظائف. يرجى المحاولة مرة أخرى لاحقاً.',
            'chatbot_error' => 'عذراً، المساعد الذكي غير متاح حالياً. يرجى المحاولة مرة أخرى لاحقاً.',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Logging Settings
    |--------------------------------------------------------------------------
    */
    'logging' => [
        'enabled' => true,
        'log_requests' => env('AI_LOG_REQUESTS', true),
        'log_responses' => env('AI_LOG_RESPONSES', false), // Be careful with sensitive data
        'log_errors' => true,
        'log_performance' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Content Filtering
    |--------------------------------------------------------------------------
    */
    'content_filter' => [
        'enabled' => true,
        'filter_inappropriate_content' => true,
        'filter_personal_info' => true,
        'max_input_length' => 5000,
    ],

    /*
    |--------------------------------------------------------------------------
    | Multi-language Support
    |--------------------------------------------------------------------------
    */
    'languages' => [
        'supported' => ['ar', 'en', 'fr'],
        'default' => 'ar',
        'auto_detect' => true,
    ],
];
