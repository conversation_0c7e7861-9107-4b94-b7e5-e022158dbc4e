<?php

return [
    // ===== NAVIGATION & MENU =====
    'nav_home' => 'Home',
    'nav_about' => 'About',
    'nav_services' => 'Services',
    'nav_contact' => 'Contact',
    'nav_pricing' => 'Pricing',
    'nav_cv_builder' => 'CV Builder',
    'nav_personality_test' => 'Personality Test',
    'nav_job_recommendations' => 'Job Recommendations',
    'nav_interview_training' => 'Interview Training',
    'nav_blog' => 'Blog',
    'login' => 'Login',
    'register' => 'Register',
    'dashboard' => 'Dashboard',
    'profile' => 'Profile',
    'logout' => 'Logout',
    'settings' => 'Settings',
    'admin_panel' => 'Admin Panel',

    // ===== HERO SECTION =====
    'hero_title' => 'Discover Your Career Future with AI',
    'hero_subtitle' => 'Smart platform to guide students and graduates towards the right career path',
    'hero_description' => 'Harness the power of artificial intelligence to analyze your personality, discover your strengths, and get personalized career guidance that matches your goals and aspirations.',
    'get_started' => 'Get Started',
    'learn_more' => 'Learn More',
    'watch_demo' => 'Watch Demo',
    'start_free_trial' => 'Start Free Trial',
    'view_pricing' => 'View Pricing',

    // ===== THEME & UI =====
    'dark_mode' => 'Dark Mode',
    'light_mode' => 'Light Mode',
    'toggle_theme' => 'Toggle Theme',

    // ===== COMMON BUTTONS & ACTIONS =====
    'loading' => 'Loading...',
    'error' => 'Error',
    'success' => 'Success',
    'warning' => 'Warning',
    'info' => 'Information',
    'close' => 'Close',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'submit' => 'Submit',
    'next' => 'Next',
    'previous' => 'Previous',
    'finish' => 'Finish',
    'continue' => 'Continue',
    'back' => 'Back',
    'edit' => 'Edit',
    'delete' => 'Delete',
    'view' => 'View',
    'download' => 'Download',
    'upload' => 'Upload',
    'search' => 'Search',
    'filter' => 'Filter',
    'sort' => 'Sort',
    'refresh' => 'Refresh',
    'reset' => 'Reset',
    'confirm' => 'Confirm',
    'yes' => 'Yes',
    'no' => 'No',
    'ok' => 'OK',

    // ===== SERVICES & FEATURES =====
    'services_title' => 'Our Premium Services',
    'services_subtitle' => 'Comprehensive solutions for your career development',
    'features_title' => 'Our Services',
    'why_choose_us' => 'Why Choose MonOri AI?',

    // Main Services
    'personality_analysis_title' => 'Personality Analysis',
    'personality_analysis_description' => 'Discover your personality type and strengths through advanced scientific assessments',
    'personality_analysis' => 'Personality Analysis',
    'personality_description' => 'Discover your personality type and strengths through advanced scientific tests',

    'cv_generation_title' => 'CV Generation',
    'cv_generation_description' => 'Create professional CVs tailored to your skills and target positions',
    'cv_generation' => 'CV Generation',
    'cv_description' => 'Create professional CVs tailored to your skills and target positions',

    'job_recommendations_title' => 'Job Recommendations',
    'job_recommendations_description' => 'Get personalized job recommendations based on your profile and preferences',
    'job_recommendations' => 'Job Recommendations',
    'job_description' => 'Get personalized job recommendations based on your profile and preferences',

    'career_guidance' => 'Career Guidance',
    'career_description' => 'Get personalized recommendations for jobs and specializations that match your profile',

    'interview_simulation' => 'Interview Simulation',
    'interview_description' => 'Practice job interviews with advanced AI and receive instant feedback',

    'cv_improvement' => 'CV Improvement',
    'cv_improvement_description' => 'Improve your CV using artificial intelligence for better results',

    'job_matching' => 'Job Matching',
    'job_matching_description' => 'Find jobs that suit you in local and global markets',

    'skill_assessment' => 'Skill Assessment',
    'skill_assessment_description' => 'Evaluate your skills and get recommendations for improvement',
    'skill_description' => 'Assess your current skills and get a personalized development plan',

    // ===== PRICING & PLANS =====
    'pricing_title' => 'Subscription Plans & Pricing',
    'pricing_description' => 'Choose the right plan for you and get the best AI-powered career guidance services',
    'monthly' => 'Monthly',
    'yearly' => 'Yearly',
    'save_up_to' => 'Save up to 20%',
    'most_popular' => 'Most Popular',
    'recommended' => 'Recommended',
    'best_value' => 'Best Value',
    'paid_services_title' => 'Premium Paid Services',
    'paid_services_description' => 'Professional services tailored to your needs',

    // Subscription Plans
    'basic_plan' => 'Basic Plan',
    'gold_plan' => 'Gold Plan',
    'premium_plan' => 'Premium Plan',
    'plan_features' => 'Plan Features',
    'choose_plan' => 'Choose Plan',
    'current_plan' => 'Current Plan',
    'upgrade_plan' => 'Upgrade Plan',
    'per_month' => 'per month',
    'per_year' => 'per year',
    'billed_monthly' => 'Billed Monthly',
    'billed_yearly' => 'Billed Yearly',

    // Plan Features
    'unlimited_tests' => 'Unlimited Personality Tests',
    'basic_cv_builder' => 'Basic CV Builder',
    'job_recommendations' => 'Job Recommendations',
    'email_support' => 'Email Support',
    'advanced_analytics' => 'Advanced Analytics',
    'priority_support' => 'Priority Support',
    'ai_interview_training' => 'AI Interview Training',
    'custom_reports' => 'Custom Reports',
    'api_access' => 'API Access',

    // Paid Services
    'cv_creation_service' => 'Professional CV Creation',
    'cv_improvement_service' => 'CV Improvement & Optimization',
    'personal_coaching_service' => 'Personal Career Coaching',
    'interview_training_service' => 'Interview Training Session',
    'order_now' => 'Order Now',
    'book_session' => 'Book Session',

    // Currency & Time
    'currency' => 'DH',
    'per_month_short' => '/month',
    'per_year_short' => '/year',
    'monthly_billing' => 'Monthly',
    'yearly_billing' => 'Yearly (Save 20%)',
    'billing_cycle' => 'Billing Cycle',
    'delivery_within' => 'Delivery within',
    'hours' => 'hours',
    'ai_requests_monthly' => 'AI requests monthly',
    'cv_monthly' => 'CVs monthly',

    // Personality Test Page
    'personality_test_title' => 'AI-Powered Personality Analysis Test',
    'personality_test_description' => 'Discover your professional personality with advanced AI analysis and get personalized career recommendations',
    'personality_test_subtitle' => 'Discover your professional personality with advanced AI analysis and get personalized career recommendations',
    'previous' => 'Previous',
    'next' => 'Next',
    'question_counter' => 'of',

    // CV Builder Page
    'cv_builder' => 'CV Builder',
    'cv_builder_title' => 'CV Builder',
    'cv_builder_description' => 'Create your professional resume easily with diverse templates and modern designs',
    'create_cv' => 'Create CV',

    // AI Coaching
    'ai_coaching' => 'AI Coaching',
    'ai_coaching_description' => 'Get personalized AI-powered coaching to develop your professional skills',
    'professional' => 'Professional',
    'cv_builder_subtitle' => 'Create your professional resume in minutes using our diverse and modern templates. Design a resume that showcases your skills and experience in the best possible way.',
    'start_now_free' => 'Start Now for Free',
    'learn_features' => 'Learn About Features',

    // AI CV Generation
    'ai_quick_generate' => 'AI Quick Generate',
    'ai_quick_generate_desc' => 'Let AI create your resume in seconds',
    'ai_suggestions' => 'Smart Suggestions',
    'ai_cv_generated_successfully' => 'CV generated successfully with AI',
    'ai_cv_generation_failed' => 'Failed to generate CV with AI',
    'ai_cv_improved_successfully' => 'CV improved successfully with AI',
    'ai_cv_improvement_failed' => 'Failed to improve CV with AI',
    'ai_suggestions_failed' => 'Failed to get AI suggestions',
    'ai_service_error' => 'AI service error',

    // Footer
    'all_rights_reserved' => 'All rights reserved',
    'copyright' => '© 2024 MonOri AI. All rights reserved.',

    // Language Switcher
    'language' => 'Language',
    'arabic' => 'Arabic',
    'english' => 'English',
    'french' => 'French',

    // Contact Page
    'contact' => 'Contact Us',
    'contact_description' => 'We are here to help you! Contact us and we will be happy to answer all your questions about MonOri AI platform and our services.',
    'contact_form_title' => 'Get in Touch',
    'contact_form_description' => 'Have questions about MonOri AI? We\'d love to hear from you. Send us a message and we\'ll respond as soon as possible.',
    'send_message' => 'Send us a message',
    'full_name' => 'Full Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'subject' => 'Subject',
    'message' => 'Message',
    'send_message_btn' => 'Send Message',
    'choose_subject' => 'Choose Subject',
    'general_inquiry' => 'General Inquiry',
    'technical_support' => 'Technical Support',
    'partnership' => 'Partnership',
    'feedback' => 'Feedback & Suggestions',
    'other' => 'Other',
    'enter_full_name' => 'Enter your full name',
    'write_message' => 'Write your message here...',
    'address' => 'Address',
    'business_center' => 'Technology Business Center',
    'casablanca_morocco' => 'Casablanca, Morocco',
    'working_hours' => 'Monday to Friday, 9:00 - 18:00',
    'faq' => 'Frequently Asked Questions',
    'how_to_start' => 'How can I start using the platform?',
    'how_to_start_answer' => 'You can create a free account and start the personality analysis test immediately.',
    'are_services_free' => 'Are your services free?',
    'services_free_answer' => 'We provide basic free services, with paid plans for advanced features.',
    'analysis_time' => 'How long does the analysis take?',
    'analysis_time_answer' => 'Personality analysis takes about 15-20 minutes, and results are instant.',
    'sending' => 'Sending...',
    'message_sent' => 'Your message has been sent successfully! We will contact you soon.',

    // ===== AUTHENTICATION =====
    'login' => 'Login',
    'register' => 'Register',
    'sign_in' => 'Sign In',
    'sign_up' => 'Sign Up',
    'create_account' => 'Create Account',
    'welcome_back' => 'Welcome Back',
    'join_monori' => 'Join MonOri AI',
    'join_monori_subtitle' => 'Join MonOri AI and start your career journey',
    'login_subtitle' => 'Sign in to access your account',
    'password' => 'Password',
    'confirm_password' => 'Confirm Password',
    'remember_me' => 'Remember Me',
    'forgot_password' => 'Forgot Password?',
    'reset_password' => 'Reset Password',
    'send_reset_link' => 'Send Reset Link',
    'already_have_account' => 'Already have an account?',
    'dont_have_account' => 'Don\'t have an account?',
    'no_account' => 'Don\'t have an account?',
    'sign_in_here' => 'Sign in here',
    'sign_up_here' => 'Sign up here',
    'email' => 'Email Address',
    'full_name' => 'Full Name',
    'phone' => 'Phone Number',
    'agree_terms' => 'I agree to the',
    'terms_service' => 'Terms of Service',
    'privacy_policy_link' => 'Privacy Policy',
    'and' => 'and',
    'or' => 'or',

    // Additional Authentication Texts
    'create_new_account' => 'Create New Account',
    'enter_email' => 'Enter your email address',
    'enter_password' => 'Enter your password',
    'enter_full_name' => 'Enter your full name',
    'password_requirements' => 'Password must be at least 8 characters long',
    'show_password' => 'Show password',
    'hide_password' => 'Hide password',
    'password_strength' => 'Password strength',
    'weak' => 'Weak',
    'medium' => 'Medium',
    'strong' => 'Strong',
    'passwords_match' => 'Passwords match',
    'passwords_dont_match' => 'Passwords don\'t match',

    // About Page
    'about' => 'About Us',
    'about_description' => 'We believe that everyone deserves to find the right career path. Our platform combines the power of artificial intelligence with expert knowledge to provide personalized and effective career guidance.',
    'our_mission' => 'Our Mission',
    'mission_description' => 'Empowering students and graduates to make informed career decisions by providing smart tools and accurate personal analysis that help them discover their true potential and guide them towards the most suitable career path.',
    'our_vision' => 'Our Vision',
    'vision_description' => 'To be the leading platform for smart career guidance in Morocco and the Arab world, where every young person finds the necessary support and guidance to build a successful and fulfilling professional future.',
    'our_story' => 'Our Story',
    'how_we_started' => 'How MonOri AI Started?',
    'story_p1' => 'The idea of MonOri AI started from a simple observation: many students and graduates in Morocco and the Arab world face difficulty in choosing the right career path for them.',
    'story_p2' => 'We saw that traditional career guidance does not keep pace with rapid developments in the job market, and does not take into account the unique personality of each individual.',
    'story_p3' => 'So we decided to combine the latest artificial intelligence technologies with the expertise of career guidance specialists to create a comprehensive and smart platform.',
    'research_development' => 'Research & Development',
    'research_desc' => 'Market needs study and algorithm development',
    'beta_launch' => 'Beta Launch',
    'beta_desc' => 'Testing the platform with a group of students',
    'official_launch' => 'Official Launch',
    'official_desc' => 'Making the platform available to the public with all features',
    'our_values' => 'Our Values',
    'values_description' => 'The values that guide our work and define our identity',
    'innovation' => 'Innovation',
    'innovation_desc' => 'We always strive to develop innovative solutions that keep pace with technological developments',
    'quality' => 'Quality',
    'quality_desc' => 'We are committed to providing the highest levels of quality in all our services',
    'accessibility' => 'Accessibility',
    'accessibility_desc' => 'We make our services available to everyone regardless of their background',
    'trust' => 'Trust',
    'trust_desc' => 'We maintain the privacy of your data and build relationships based on trust',

    // Features
    'features_title' => 'Why Choose MonOri AI?',
    'ai_powered' => 'AI-Powered',
    'ai_powered_desc' => 'Advanced technologies for accurate analysis and personalized recommendations',

    'multilingual' => 'Multilingual',
    'multilingual_desc' => 'Full support for Arabic, English, and French',

    'personalized' => 'Personalized',
    'personalized_desc' => 'Unique experience that adapts to your needs and goals',

    'expert_backed' => 'Expert-Backed',
    'expert_backed_desc' => 'Content approved by career guidance and HR experts',

    // Call to Action
    'cta_title' => 'Start Your Career Journey Today',
    'cta_description' => 'Join thousands of students and graduates who found their career path with us',
    'start_free_trial' => 'Start Free Trial',
    'view_pricing' => 'View Pricing',

    // Footer
    'footer_description' => 'Leading platform in smart career guidance for students and graduates in Morocco and the Arab world',
    'quick_links' => 'Quick Links',
    'support' => 'Support',
    'legal' => 'Legal',
    'privacy_policy' => 'Privacy Policy',
    'terms_of_service' => 'Terms of Service',
    'help_center' => 'Help Center',
    'faq' => 'FAQ',
    'all_rights_reserved' => 'All rights reserved',

    // Language Switcher
    'language' => 'Language',
    'arabic' => 'العربية',
    'english' => 'English',
    'french' => 'Français',

    // Common
    'loading' => 'Loading...',
    'error' => 'Error',
    'success' => 'Success',
    'warning' => 'Warning',
    'info' => 'Information',
    'close' => 'Close',
    'save' => 'Save',
    'cancel' => 'Cancel',
    'submit' => 'Submit',
    'next' => 'Next',
    'previous' => 'Previous',
    'finish' => 'Finish',

    // Authentication
    'welcome_back' => 'Welcome Back',
    'login_subtitle' => 'Sign in to access your account',
    'email' => 'Email Address',
    'password' => 'Password',
    'remember_me' => 'Remember me',
    'forgot_password' => 'Forgot your password?',
    'sign_in' => 'Sign In',
    'or' => 'or',
    'no_account' => 'Don\'t have an account?',
    'create_account' => 'Create New Account',
    'full_name' => 'Full Name',
    'confirm_password' => 'Confirm Password',
    'agree_terms' => 'I agree to the',
    'terms_service' => 'Terms of Service',
    'privacy_policy_link' => 'Privacy Policy',
    'already_have_account' => 'Already have an account?',
    'join_monori' => 'Join MonOri AI and start your career journey',

    // ===== DASHBOARD =====
    'welcome_user' => 'Welcome, :name',
    'progress_summary' => 'Here\'s your career progress summary today',
    'last_visit' => 'Last visit',
    'days_ago' => ':count days ago',
    'hours_ago' => ':count hours ago',
    'minutes_ago' => ':count minutes ago',
    'just_now' => 'Just now',
    'tests_completed' => 'Tests Completed',
    'cv_improvements' => 'CV Improvements',
    'interview_practice' => 'Interview Practice',
    'career_score' => 'Career Score',
    'this_week' => 'this week',
    'this_month' => 'this month',
    'today' => 'today',
    'yesterday' => 'yesterday',
    'average_score' => 'Average Score',
    'excellent' => 'Excellent',
    'good' => 'Good',
    'fair' => 'Fair',
    'needs_improvement' => 'Needs Improvement',
    'recent_activity' => 'Recent Activity',
    'monthly_progress' => 'Your Monthly Progress',
    'weekly_progress' => 'Weekly Progress',
    'quick_actions' => 'Quick Actions',
    'achievements' => 'Achievements',
    'view_all_activities' => 'View All Activities',
    'view_all' => 'View All',
    'no_activities' => 'No activities yet',
    'start_first_test' => 'Start your first test',
    'dashboard_overview' => 'Dashboard Overview',
    'statistics' => 'Statistics',
    'performance' => 'Performance',

    // Personality Analysis
    'discover_personality' => 'Discover your unique personality type and strengths through advanced scientific tests powered by artificial intelligence',
    'start_test_now' => 'Start Test Now',
    'show_sample_results' => 'Show Sample Results',
    'available_tests' => 'Available Test Types',
    'comprehensive_tests' => 'We provide a comprehensive set of approved scientific tests to analyze your personality accurately',
    'mbti_test' => 'MBTI Test',
    'mbti_description' => 'Myers-Briggs Type Indicator - identifies your type among 16 different personality types',
    'bigfive_test' => 'Big Five Test',
    'bigfive_description' => 'Big Five personality traits model - measures five fundamental dimensions of personality',
    'ai_enhanced_analysis' => 'AI-Enhanced Analysis',
    'ai_analysis_description' => 'Comprehensive analysis combining traditional results with artificial intelligence for deeper understanding',
    'minutes' => 'minutes',
    'start_now' => 'Start Now',

    // Career Guidance
    'get_personalized_recommendations' => 'Get personalized career recommendations that match your personality, skills, and future goals',
    'start_career_assessment' => 'Start Comprehensive Career Assessment',
    'explore_career_paths' => 'Explore Career Paths',
    'comprehensive_assessment' => 'Comprehensive Career Assessment',
    'answer_questions_guidance' => 'Answer the following questions to get personalized career recommendations',
    'personal_information' => 'Personal Information',
    'age' => 'Age',
    'education_level' => 'Education Level',
    'interests_inclinations' => 'Interests & Inclinations',
    'select_interests' => 'Select fields that interest you (you can choose multiple fields)',
    'technology' => 'Technology',
    'healthcare' => 'Healthcare',
    'education' => 'Education',
    'business' => 'Business',
    'arts' => 'Arts',
    'science' => 'Science',
    'skills_abilities' => 'Skills & Abilities',
    'rate_skills' => 'Rate your skill level in the following areas',
    'communication_presentation' => 'Communication & Presentation',
    'analytical_thinking' => 'Analytical Thinking',
    'creativity_innovation' => 'Creativity & Innovation',
    'leadership_management' => 'Leadership & Team Management',
    'weak' => 'Weak',
    'excellent_skill' => 'Excellent',
    'career_goals' => 'Career Goals',
    'main_career_goal' => 'What is your main career goal?',
    'work_priorities' => 'What are your work priorities?',
    'high_salary' => 'High Salary',
    'work_life_balance' => 'Work-Life Balance',
    'career_growth' => 'Career Growth',
    'social_impact' => 'Social Impact',
    'get_recommendations' => 'Get Your Career Recommendations',

    // CV Improvement
    'improve_cv_ai' => 'Improve your CV using advanced artificial intelligence and get comprehensive analysis and personalized recommendations',
    'upload_cv' => 'Upload Your CV',
    'create_new_cv' => 'Create New CV',
    'upload_for_analysis' => 'Upload your CV for analysis',
    'supported_formats' => 'Supported files: PDF, DOC, DOCX',
    'drag_drop_cv' => 'Drag and drop your CV file here',
    'click_to_select' => 'or click to select from your device',
    'choose_file' => 'Choose File',
    'analyze_cv_ai' => 'Analyze CV with AI',
    'cv_analysis_results' => 'Your CV Analysis Results',
    'comprehensive_ai_analysis' => 'Comprehensive AI-powered analysis',
    'overall_score' => 'Overall Score',
    'good_cv_improvements' => 'Good CV with potential for improvement',
    'strengths' => 'Strengths',
    'areas_improvement' => 'Areas for Improvement',
    'clear_organized_format' => 'Clear and organized format',
    'diverse_experience' => 'Diverse work experience',
    'updated_technical_skills' => 'Updated technical skills',
    'complete_contact_info' => 'Complete contact information',
    'add_strong_summary' => 'Add strong professional summary',
    'improve_achievements' => 'Improve achievement descriptions',
    'add_keywords' => 'Add field-specific keywords',
    'improve_sections_order' => 'Improve sections order',
    'generate_improved_cv' => 'Generate Improved Version',
    'download_analysis_report' => 'Download Analysis Report',
    'analyze_another_cv' => 'Analyze Another CV',

    // Interview Simulation
    'practice_interviews_ai' => 'Practice job interviews with advanced artificial intelligence and get instant performance feedback',
    'start_mock_interview' => 'Start Mock Interview',
    'interview_types' => 'Interview Types',
    'available_interview_types' => 'Available Interview Types',
    'choose_interview_type' => 'Choose the type of interview you want to practice',
    'technical_interview' => 'Technical Interview',
    'technical_interview_desc' => 'Specialized technical questions in programming and software development',
    'hr_interview' => 'HR Interview',
    'hr_interview_desc' => 'General questions about personality, experience, and motivation',
    'behavioral_interview' => 'Behavioral Interview',
    'behavioral_interview_desc' => 'Questions about situations, past experiences, and dealing with challenges',
    'interview_setup' => 'Interview Setup',
    'choose_interview_settings' => 'Choose interview settings that suit you',
    'target_position' => 'Target Position',
    'experience_level' => 'Experience Level',
    'interview_duration' => 'Interview Duration',
    'interview_language' => 'Interview Language',
    'start_interview_now' => 'Start Interview Now',

    // ===== ADMIN PANEL =====
    'admin_dashboard' => 'Admin Dashboard',
    'admin_overview' => 'Admin Overview',
    'total_users' => 'Total Users',
    'active_users' => 'Active Users',
    'total_tests' => 'Total Tests',
    'total_cvs' => 'Total CVs',
    'revenue' => 'Revenue',
    'subscriptions' => 'Subscriptions',
    'user_management' => 'User Management',
    'content_management' => 'Content Management',
    'system_settings' => 'System Settings',
    'reports' => 'Reports',
    'analytics' => 'Analytics',
    'manage_users' => 'Manage Users',
    'manage_content' => 'Manage Content',
    'manage_subscriptions' => 'Manage Subscriptions',
    'manage_payments' => 'Manage Payments',
    'view_reports' => 'View Reports',
    'system_logs' => 'System Logs',
    'backup_restore' => 'Backup & Restore',
    'email_templates' => 'Email Templates',
    'notification_settings' => 'Notification Settings',
    'api_settings' => 'API Settings',
    'security_settings' => 'Security Settings',
    'maintenance_mode' => 'Maintenance Mode',
    'cache_management' => 'Cache Management',
    'database_management' => 'Database Management',

    // ===== USER PROFILE =====
    'my_profile' => 'My Profile',
    'edit_profile' => 'Edit Profile',
    'profile_settings' => 'Profile Settings',
    'account_settings' => 'Account Settings',
    'privacy_settings' => 'Privacy Settings',
    'notification_preferences' => 'Notification Preferences',
    'change_password' => 'Change Password',
    'current_password' => 'Current Password',
    'new_password' => 'New Password',
    'confirm_new_password' => 'Confirm New Password',
    'update_profile' => 'Update Profile',
    'profile_updated' => 'Profile updated successfully',
    'password_changed' => 'Password changed successfully',
    'delete_account' => 'Delete Account',
    'account_deleted' => 'Account deleted successfully',
    'are_you_sure' => 'Are you sure?',
    'this_action_cannot_be_undone' => 'This action cannot be undone',

    // ===== NOTIFICATIONS =====
    'notifications' => 'Notifications',
    'mark_as_read' => 'Mark as Read',
    'mark_all_read' => 'Mark All as Read',
    'no_notifications' => 'No notifications',
    'new_notification' => 'New notification',
    'email_notifications' => 'Email Notifications',
    'push_notifications' => 'Push Notifications',
    'sms_notifications' => 'SMS Notifications',

    // ===== LANGUAGE & LOCALIZATION =====
    'language' => 'Language',
    'arabic' => 'العربية',
    'english' => 'English',
    'french' => 'Français',
    'change_language' => 'Change Language',
    'language_changed' => 'Language changed successfully',

    // ===== FOOTER =====
    'footer_description' => 'Leading platform in smart career guidance for students and graduates in Morocco and the Arab world',
    'quick_links' => 'Quick Links',
    'support' => 'Support',
    'legal' => 'Legal',
    'privacy_policy' => 'Privacy Policy',
    'terms_of_service' => 'Terms of Service',
    'help_center' => 'Help Center',
    'faq' => 'FAQ',
    'all_rights_reserved' => 'All rights reserved',
    'copyright' => '© 2024 MonOri AI. All rights reserved.',
    'follow_us' => 'Follow Us',
    'social_media' => 'Social Media',
    'home' => 'Home',
    'services' => 'Services',
    'about' => 'About',
    'contact' => 'Contact',

    // ===== ERROR MESSAGES =====
    'page_not_found' => 'Page Not Found',
    'access_denied' => 'Access Denied',
    'server_error' => 'Server Error',
    'maintenance' => 'Under Maintenance',
    'coming_soon' => 'Coming Soon',
    'try_again' => 'Try Again',
    'go_home' => 'Go Home',
    'contact_support' => 'Contact Support',

    // ===== CONTACT PAGE =====
    'contact' => 'Contact Us',
    'contact_description' => 'We\'re here to help! Reach out to us for any questions or support you need',
    'send_message' => 'Send Message',
    'send_message_btn' => 'Send Message',
    'subject' => 'Subject',
    'message' => 'Message',
    'write_message' => 'Write your message here...',
    'choose_subject' => 'Choose Subject',
    'general_inquiry' => 'General Inquiry',
    'technical_support' => 'Technical Support',
    'partnership' => 'Partnership',
    'feedback' => 'Feedback',
    'other' => 'Other',

    // Contact Information
    'contact_info' => 'Contact Information',
    'email_address' => 'Email Address',
    'phone_number' => 'Phone Number',
    'address' => 'Address',
    'business_hours' => 'Business Hours',
    'monday_to_friday' => 'Monday to Friday, 9:00 AM - 6:00 PM',
    'casablanca_morocco' => 'Casablanca, Morocco',
    'tech_business_center' => 'Technology Business Center',

    // FAQ
    'frequently_asked_questions' => 'Frequently Asked Questions',
    'faq_question_1' => 'How can I start using the platform?',
    'faq_answer_1' => 'You can create a free account and start with personality analysis test immediately.',
    'faq_question_2' => 'Are your services free?',
    'faq_answer_2' => 'We offer basic services for free, with paid plans for advanced features.',
    'faq_question_3' => 'How long does the analysis take?',
    'faq_answer_3' => 'Personality analysis takes about 15-20 minutes, with instant results.',

    // Form Messages
    'sending' => 'Sending...',
    'message_sent_success' => 'Your message has been sent successfully! We\'ll get back to you soon.',
    'message_send_error' => 'An error occurred while sending the message. Please try again.',
    'required_field' => 'This field is required',
    'invalid_email' => 'Please enter a valid email address',
    'message_too_short' => 'Message is too short',
    'email_placeholder' => '<EMAIL>',
    'phone_placeholder' => '+212 6XX XXX XXX',

    // ===== CV BUILDER =====
    // General Texts
    'ready_to_create_cv' => 'Ready to create your professional CV?',
    'start_now_get_cv' => 'Start now and get a professional CV that helps you land your dream job',
    'next' => 'Next',
    'previous' => 'Previous',
    'create_cv' => 'Create CV',
    'creating' => 'Creating...',
    'cancel' => 'Cancel',
    'edit' => 'Edit',
    'download' => 'Download',
    'preview' => 'Preview',

    // Preview Page
    'cv_preview' => 'CV Preview',
    'review_before_download' => 'Review your CV before downloading',
    'download_pdf' => 'Download PDF',
    'print_cv' => 'Print',

    // CV Sections
    'personal_information' => 'Personal Information',
    'professional_summary' => 'Professional Summary',
    'work_experience' => 'Work Experience',
    'education' => 'Education',
    'skills' => 'Skills',
    'languages' => 'Languages',
    'certifications' => 'Certifications',
    'projects' => 'Projects',
    'references' => 'References',

    // Form Fields
    'job_title' => 'Job Title',
    'company_name' => 'Company Name',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'current_job' => 'Current Job',
    'job_description' => 'Job Description',
    'degree' => 'Degree',
    'institution' => 'Institution',
    'graduation_year' => 'Graduation Year',
    'skill_name' => 'Skill Name',
    'skill_level' => 'Skill Level',
    'language_name' => 'Language Name',
    'language_level' => 'Language Level',

    // Skill & Language Levels
    'beginner' => 'Beginner',
    'intermediate' => 'Intermediate',
    'advanced' => 'Advanced',
    'expert' => 'Expert',
    'native' => 'Native',
    'fluent' => 'Fluent',
    'conversational' => 'Conversational',
    'basic' => 'Basic',

    // Add/Remove Buttons
    'add_experience' => 'Add Experience',
    'add_education' => 'Add Education',
    'add_skill' => 'Add Skill',
    'add_language' => 'Add Language',
    'add_certification' => 'Add Certification',
    'add_project' => 'Add Project',
    'remove' => 'Remove',
    'remove_item' => 'Remove Item',

    // Dynamic Elements
    'work_experience_item' => 'Work Experience',
    'education_item' => 'Education',
    'skill_item' => 'Skill',
    'language_item' => 'Language',

    // Success & Error Messages
    'cv_created_successfully' => 'CV created successfully! You can now review and edit the content.',
    'cv_creation_error' => 'An error occurred while creating the CV',
    'pdf_creation_error' => 'An error occurred while creating the PDF file',
    'cv_updated_successfully' => 'CV updated successfully',
    'cv_deleted_successfully' => 'CV deleted successfully',
    'data_cleared_successfully' => 'Data cleared successfully',

    // Additional page texts
    'create_cv_title' => 'Create CV',
    'create_cv_description' => 'Create your professional CV step by step',
    'follow_steps_create_cv' => 'Follow these steps to create your professional CV',
    'ai_quick_create' => 'AI Quick Create',
    'ai_quick_description' => 'Let AI create your CV in seconds',
    'choose_template' => 'Choose Template',
    'choose_design_description' => 'Choose the design that suits your professional field',
    'enter_basic_info' => 'Enter your basic personal information',
    'experience_education' => 'Experience & Education',
    'add_experience_education' => 'Add your work experience and educational qualifications',
    'skills_languages' => 'Skills & Languages',
    'add_skills_languages' => 'Add your skills and languages you speak',
    'certifications_courses' => 'Certifications & Courses',
    'work_experiences' => 'Work Experience',
    'add_qualification' => 'Add Qualification',
    'please_choose_template' => 'Please choose a CV template',
    'company_name_field' => 'Company Name',
    'position' => 'Position',
    'task_description' => 'Job Description',
    'institution_name' => 'Institution Name',
    'specialization' => 'Specialization',
    'skill_name_placeholder' => 'Skill Name',
    'language_number' => 'Language',
    'language_name_placeholder' => 'Language Name',
    'proficiency_level' => 'Proficiency Level',
    'excellent' => 'Excellent',
    'mother_tongue' => 'Native',
    'certificate_number' => 'Certificate',
    'certificate_name' => 'Certificate Name',
    'issuing_authority' => 'Issuing Authority',
    'ai_quick_create_full' => 'AI Quick Create',
    'enter_basic_info_ai' => 'Enter some basic information and we\'ll create a complete CV for you',
    'desired_job_title' => 'Desired Job Title',
    'field_industry' => 'Field/Industry',
    'experience_level' => 'Experience Level',
    'beginner_0_2_years' => 'Beginner (0-2 years)',
    'intermediate_2_5_years' => 'Intermediate (2-5 years)',
    'advanced_5_10_years' => 'Advanced (5-10 years)',
    'expert_10_plus_years' => 'Expert (10+ years)',
    'main_skills_comma_separated' => 'Main skills (comma separated)',
    'error_getting_suggestions' => 'Error getting suggestions',
    'ai_suggestions' => 'AI Suggestions',
    'choose_suggestion_or_reference' => 'Choose the appropriate suggestion or use as reference',
    'close' => 'Close',
    'suggestion_applied_successfully' => 'Suggestion applied successfully',
    'smart_suggestions' => 'Smart Suggestions',
    'brief_about_yourself' => 'Write a brief summary about yourself and your career goals...',
    'website' => 'Website',
    'brief_summary' => 'Brief Summary',
    'now' => 'Present',

    // CV Templates
    'template_modern' => 'Modern',
    'template_modern_desc' => 'Clean and modern design suitable for all fields',
    'template_professional' => 'Professional',
    'template_professional_desc' => 'Classic professional design suitable for corporations',
    'template_creative' => 'Creative',
    'template_creative_desc' => 'Creative design suitable for artistic and technical fields',
    'template_minimal' => 'Minimal',
    'template_minimal_desc' => 'Simple and elegant design focused on content',
    'not_specified' => 'Not Specified',
    'cv_title' => 'Resume',

    // CV Builder Main Page
    'why_choose_cv_builder' => 'Why Choose Our CV Builder?',
    'diverse_templates' => 'Diverse Templates',
    'diverse_templates_desc' => 'Choose from a wide range of professional templates designed specifically for different fields and specializations',
    'fast_easy' => 'Fast & Easy',
    'fast_easy_desc' => 'Create your resume in minutes with an easy-to-use interface and smart system for organizing information',
    'instant_download' => 'Instant Download',
    'instant_download_desc' => 'Get your resume in high-quality PDF format ready for printing and sending as soon as you finish',
    'secure_protected' => 'Secure & Protected',
    'secure_protected_desc' => 'Your data is protected with the highest security standards and we do not retain any personal information after creating the resume',
    'full_customization' => 'Full Customization',
    'full_customization_desc' => 'Fully customize your resume from colors and fonts to section arrangement to match your professional personality',
    'multilingual_support' => 'Multilingual Support',
    'multilingual_support_desc' => 'Create your resume in Arabic, English, or French with full support for all languages',
    'start_creating_cv_now' => 'Start Creating Your Resume Now',

    // Pricing Page - Additional Translations
    'save_yearly' => 'Save :percentage% Yearly',
    'included_features' => 'Included Features:',
    'current_plan' => 'Your Current Plan',
    'choose_plan' => 'Choose :plan',
    'order_service' => 'Order Service',
    'faq_title' => 'Frequently Asked Questions',
    'faq_cancel_question' => 'Can I cancel my subscription at any time?',
    'faq_cancel_answer' => 'Yes, you can cancel your subscription at any time without any additional fees.',
    'faq_payment_question' => 'What payment methods are available?',
    'faq_payment_answer' => 'We accept all credit cards, bank transfers, and electronic payments.',
    'faq_refund_question' => 'Is there a money-back guarantee?',
    'faq_refund_answer' => 'Yes, we provide a money-back guarantee within 30 days of subscription.',
    'per_year_short' => '/year',
    'choose_payment_method' => 'Choose Payment Method',
    'billing_cycle' => 'Billing Cycle',

    // AI Template Recommendations
    'ai_template_recommendation' => 'AI Template Recommendations',
    'ai_template_description' => 'Get smart recommendations for the best CV templates suitable for your professional field',
    'job_field_placeholder' => 'e.g.: Software Developer, Engineer, Designer...',
    'entry_level' => 'Entry Level',
    'mid_level' => 'Mid Level',
    'senior_level' => 'Senior Level',
    'executive_level' => 'Executive Level',
    'industry_type' => 'Industry Type',
    'technology' => 'Technology',
    'healthcare' => 'Healthcare',
    'finance' => 'Finance',
    'education' => 'Education',
    'marketing' => 'Marketing',
    'creative_arts' => 'Creative Arts',
    'engineering' => 'Engineering',
    'sales' => 'Sales',
    'other' => 'Other',
    'get_ai_recommendations' => 'Get AI Recommendations',
    'analyzing' => 'Analyzing',
    'ai_recommendations_title' => 'AI Recommendations',
    'ai_recommended' => 'AI Recommended',
    'ai_optimize_profile' => 'AI Optimize Profile',
    'ai_enhance_experience' => 'AI Enhance Experience',
    'ai_optimize_education' => 'AI Optimize Education',
    'ai_suggest_skills' => 'AI Suggest Skills',
    'ai_optimize_languages' => 'AI Optimize Languages',
    'ai_final_review' => 'AI Final Review',
    'ai_optimize_cv' => 'AI Optimize CV',
    'ai_reviewing_cv' => 'AI is reviewing your CV...',
    'ai_optimizing_cv' => 'AI is optimizing your CV...',
    'error_ai_review' => 'Error occurred during AI review',
    'error_ai_optimization' => 'Error occurred during AI optimization',
    'ai_review_results' => 'AI Review Results',
    'ai_analysis_feedback' => 'AI analysis and feedback on your CV',
    'strengths' => 'Strengths',
    'improvements' => 'Suggested Improvements',
    'overall_score' => 'Overall Score',
    'no_strengths_found' => 'No specific strengths found',
    'no_improvements_needed' => 'No improvements needed at this time',
    'ai_optimization_results' => 'AI Optimization Results',
    'apply_optimizations' => 'You can apply the suggested optimizations below',
    'apply' => 'Apply',
    'applied' => 'Applied',
    'apply_all' => 'Apply All',
    'optimization_applied' => 'Optimization applied successfully',
    'all_optimizations_applied' => 'All optimizations applied successfully',
    'best_for' => 'Best for',

    // New CV Builder Messages
    'create_professional_cv' => 'Create Professional CV',
    'ai_powered_cv_creation' => 'AI-Powered CV Creation',
    'ai_quick_create' => 'AI Quick Create',
    'let_ai_create_cv_seconds' => 'Let AI create your CV in seconds',
    'create_with_ai' => 'Create with AI',
    'choose_template' => 'Choose Template',
    'select_perfect_template' => 'Select the perfect template for your CV',
    'ai_template_assistant' => 'AI Template Assistant',
    'ai_will_recommend_best_template' => 'AI will recommend the best template for you',
    'get_ai_recommendations' => 'Get AI Recommendations',
    'ai_profile_optimizer' => 'AI Profile Optimizer',
    'ai_will_optimize_profile' => 'AI will optimize your profile',
    'optimize_with_ai' => 'Optimize with AI',
    'ai_experience_enhancer' => 'AI Experience Enhancer',
    'ai_will_enhance_experience' => 'AI will enhance your experience descriptions',
    'enhance_experience' => 'Enhance Experience',
    'enhance_education' => 'Enhance Education',
    'ai_skills_analyzer' => 'AI Skills Analyzer',
    'ai_will_suggest_skills' => 'AI will suggest relevant skills',
    'suggest_skills' => 'Suggest Skills',
    'suggest_languages' => 'Suggest Languages',
    'ai_final_reviewer' => 'AI Final Reviewer',
    'ai_will_review_optimize' => 'AI will review and optimize your CV',
    'ai_review' => 'AI Review',
    'ai_optimize' => 'AI Optimize',
    'modern_template' => 'Modern Template',
    'modern_template_desc' => 'Modern and attractive design suitable for tech fields',
    'professional_template' => 'Professional Template',
    'professional_template_desc' => 'Classic professional design suitable for all fields',
    'creative_template' => 'Creative Template',
    'creative_template_desc' => 'Creative design suitable for artistic and design fields',
    'minimal_template' => 'Minimal Template',
    'minimal_template_desc' => 'Simple and elegant design focused on content',
    'please_choose_template' => 'Please choose a template',
    'experience_will_appear_here' => 'Experience will appear here',
    'skills_will_appear_here' => 'Skills will appear here',
    'ai_analyzing_preferences' => 'AI is analyzing your preferences...',
    'ai_recommends_modern_template' => 'AI recommends the modern template',
    'ai_optimizing_profile' => 'AI is optimizing your profile...',
    'ai_enhancing_experience' => 'AI is enhancing your experience...',
    'ai_enhancing_education' => 'AI is enhancing your education...',
    'ai_suggesting_skills' => 'AI is suggesting skills...',
    'ai_suggesting_languages' => 'AI is suggesting languages...',
    'ai_reviewing_cv' => 'AI is reviewing your CV...',
    'ai_optimizing_cv' => 'AI is optimizing your CV...',

    // Template Features
    'gradient_design' => 'Gradient Design',
    'tech_friendly' => 'Tech Friendly',
    'modern_layout' => 'Modern Layout',
    'classic_design' => 'Classic Design',
    'business_ready' => 'Business Ready',
    'clean_layout' => 'Clean Layout',
    'artistic_design' => 'Artistic Design',
    'colorful_layout' => 'Colorful Layout',
    'creative_friendly' => 'Creative Friendly',
    'simple_design' => 'Simple Design',
    'content_focused' => 'Content Focused',
    'elegant_style' => 'Elegant Style',

    // Form Fields
    'template_selected' => 'Template Selected',
    'company' => 'Company',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'describe_responsibilities' => 'Describe your responsibilities and achievements',
    'enhance_with_ai' => 'Enhance with AI',
    'degree' => 'Degree',
    'institution' => 'Institution',
    'graduation_year' => 'Graduation Year',
    'gpa' => 'GPA',
    'optional' => 'Optional',
    'skill_name' => 'Skill Name',
    'beginner' => 'Beginner',
    'intermediate' => 'Intermediate',
    'advanced' => 'Advanced',
    'expert' => 'Expert',
    'language_name' => 'Language Name',
    'basic' => 'Basic',
    'conversational' => 'Conversational',
    'fluent' => 'Fluent',
    'native' => 'Native',
    'ai_enhancing_description' => 'AI is enhancing description...',
    'personal_info' => 'Personal Info',
    'experience' => 'Experience',
    'review' => 'Review',
    'complete_previous_steps' => 'Please complete previous steps first',
    'creating_cv' => 'Creating CV...',
    'cv_created_successfully' => 'CV created successfully',
    'error_creating_cv' => 'Error creating CV',
    'auto_saved' => 'Auto-saved',
    'ai_template_recommended' => 'Template recommended by AI',
    'ai_recommendation_error' => 'AI recommendation error',
    'profile_optimized_successfully' => 'Profile optimized successfully',
    'ai_optimization_failed' => 'AI optimization failed',
    'skills_suggested_successfully' => 'Skills suggested successfully',
    'ai_suggestion_failed' => 'AI suggestion failed',
    'enter_job_title' => 'Enter job title',
    'experience_level' => 'Experience Level',
    'entry_level' => 'Entry Level',
    'mid_level' => 'Mid Level',
    'senior_level' => 'Senior Level',
    'expert_level' => 'Expert Level',
    'industry' => 'Industry',
    'enter_industry' => 'Enter industry',
    'cancel' => 'Cancel',
    'generate' => 'Generate',
    'please_enter_job_title' => 'Please enter job title',
    'ai_generating_cv' => 'AI is generating CV...',
    'ai_creating_cv' => 'AI is creating your CV',
    'please_wait_ai_working' => 'Please wait, AI is working...',
    'ai_generation_failed' => 'AI CV generation failed',
    'all_fields' => 'All Fields',
    'select_this_template' => 'Select This Template',
    'recommendations_generated_successfully' => 'Recommendations generated successfully',
    'error_getting_recommendations' => 'Error getting recommendations',
    'template_selected_successfully' => 'Template selected successfully',
    'please_fill_at_least_one_field' => 'Please fill at least one field',
    'ai_recommendations_failed' => 'Failed to get AI recommendations',

    // Template Preview and Features
    'preview' => 'Preview',
    'template_preview' => 'Template Preview',
    'template_features' => 'Template Features',
    'ai_compatibility' => 'AI Compatibility',
    'modern_gradient_header' => 'Modern gradient header',
    'clean_sections' => 'Clean and organized sections',
    'skill_tags' => 'Skill tags',
    'perfect_for_tech' => 'Perfect for tech fields',
    'classic_layout' => 'Classic layout',
    'formal_typography' => 'Formal typography',
    'structured_sections' => 'Structured sections',
    'corporate_friendly' => 'Corporate friendly',
    'colorful_design' => 'Colorful design',
    'creative_elements' => 'Creative elements',
    'visual_appeal' => 'Visual appeal',
    'artistic_fields' => 'For artistic fields',
    'clean_simple' => 'Clean and simple',
    'focus_content' => 'Focus on content',
    'academic_style' => 'Academic style',
    'professional_minimal' => 'Professional minimal',

    // Detailed Analysis
    'detailed_analysis' => 'Detailed Analysis',
    'profile_summary' => 'Profile Summary',
    'template_reasoning' => 'Template Reasoning',
    'design_tips' => 'Design Tips',
    'industry_insights' => 'Industry Insights',
    'monthly' => 'Monthly',
    'yearly_save' => 'Yearly (Save 20%)',
    'pay_with_card' => 'Pay with Card (Stripe)',
    'pay_with_paypal' => 'Pay with PayPal',
    'cancel' => 'Cancel',
    'payment_error' => 'An error occurred while creating the payment',
    'connection_error' => 'A connection error occurred',
    'login_to_order' => 'Login to Order',
    'year' => 'year',
    'month' => 'month',

    // Subscription Dashboard
    'subscription_dashboard' => 'Subscription Dashboard',
    'manage_subscription_usage' => 'Manage your subscription and track usage',
    'your_current_subscription' => 'Your Current Subscription',
    'active' => 'Active',
    'subscription_dates' => 'Subscription Dates',
    'subscription_start' => 'Subscription Start',
    'subscription_end' => 'Subscription End',
    'days_remaining' => 'Days Remaining',
    'days' => 'days',

    // Plan Features
    'high_quality_pdf_export' => 'High-quality PDF export',
    'interactive_voice_interviews' => 'Interactive voice interviews',
    'personal_coaching_sessions' => 'Personal coaching sessions',
    'priority_support_24_7' => '24/7 priority support',

    // Profile Page
    'profile_title' => 'Profile - MonOri AI',
    'profile_description' => 'Your profile and professional achievements',
    'monori_user' => 'MonOri AI User',
    'not_specified' => 'Not specified',
    'achievements' => 'achievements',
    'member_since' => 'Member since',
    'edit_profile' => 'Edit Profile',
    'share_profile' => 'Share Profile',
    'my_professional_stats' => 'My Professional Statistics',
    'comprehensive_overview' => 'Comprehensive overview of your progress and achievements',
    'completed_tests' => 'Completed Tests',
    'excellent' => 'Excellent!',
    'start_now' => 'Start Now',
    'cv_improvements' => 'CV Improvements',
    'improved' => 'Improved',
    'ready_to_improve' => 'Ready to Improve',
    'interview_practice' => 'Interview Practice',
    'trained' => 'Trained',
    'start_training' => 'Start Training',
    'career_score' => 'Career Score',
    'excellent_score' => 'Excellent',
    'good_score' => 'Good',
    'average_score' => 'Average',
    'about_me' => 'About Me',
    'no_bio_added' => 'No personal bio added yet',
    'add_bio' => 'Add Personal Bio',
    'skills_expertise' => 'Skills & Expertise',
    'recent_activity' => 'Recent Activity',
    'completed_status' => 'Completed',
    'in_progress_status' => 'In Progress',
    'pending_status' => 'Pending',
    'no_activities_yet' => 'No activities yet',
    'start_career_journey' => 'Start your career journey now!',
    'view_all_activities' => 'View All Activities & Operations',
    'recent_achievements' => 'Recent Achievements',
    'no_achievements_yet' => 'No achievements yet',
    'start_career_journey_now' => 'Start your career journey now',
    'my_statistics' => 'My Statistics',
    'completed_activities' => 'Completed Activities',
    'completed_assessments' => 'Completed Assessments',
    'average_results' => 'Average Results',
    'completion_rate' => 'Completion Rate',
    'monthly_progress' => 'Monthly Progress',
    'personality_tests' => 'Personality Tests',
    'cv_improvement' => 'CV Improvement',
    'interview_training' => 'Interview Training',
    'quick_actions' => 'Quick Actions',
    'pending_action' => 'Pending',
    'new_personality_test' => 'Take New Personality Test',
    'cv_analysis' => 'CV Analysis',
    'training_interview' => 'Training Interview',
    'contact_info' => 'Contact Information',
    'share_profile_modal_title' => 'Share Profile',
    'share_profile_modal_desc' => 'Share your profile with others',
    'share_link' => 'Share Link',
    'copy' => 'Copy',
    'whatsapp' => 'WhatsApp',
    'twitter' => 'Twitter',
    'linkedin' => 'LinkedIn',
    'close' => 'Close',
    'share_error' => 'Error creating share link',
    'copied' => 'Copied!',
    'share_text' => 'Check out my profile on MonOri AI',

    // Edit Profile Page
    'edit_profile_title' => 'Edit Profile - MonOri AI',
    'edit_profile_description' => 'Edit and update your personal and professional information',
    'edit_profile_header' => 'Edit Profile',
    'update_personal_info' => 'Update your personal and professional information',
    'correct_errors' => 'Please correct the following errors:',
    'basic_information' => 'Basic Information',
    'full_name' => 'Full Name',
    'email_address' => 'Email Address',
    'phone_number' => 'Phone Number',
    'birth_date' => 'Birth Date',
    'gender' => 'Gender',
    'male' => 'Male',
    'female' => 'Female',
    'city' => 'City',
    'country' => 'Country',
    'professional_information' => 'Professional Information',
    'education_level' => 'Education Level',
    'field_of_study' => 'Field of Study',
    'current_job' => 'Current Job',
    'years_of_experience' => 'Years of Experience',
    'personal_information' => 'Personal Information',
    'bio' => 'Bio',
    'bio_placeholder' => 'Write a brief description about yourself...',
    'skills' => 'Skills',
    'skills_placeholder' => 'Example: Programming, Design, Project Management (separate with commas)',
    'interests' => 'Interests',
    'interests_placeholder' => 'Example: Technology, Sports, Reading (separate with commas)',
    'social_links' => 'Social Links',
    'linkedin_url' => 'LinkedIn URL',
    'github_url' => 'GitHub URL',
    'portfolio_url' => 'Portfolio URL',
    'cancel' => 'Cancel',
    'save_changes' => 'Save Changes',

    // AI Services
    'analysis_completed' => 'Analysis completed successfully!',
    'analysis_failed' => 'Analysis failed. Please try again.',
    'improvement_completed' => 'CV improvement completed successfully!',
    'improvement_failed' => 'CV improvement failed. Please try again.',
    'simulation_completed' => 'Interview simulation completed successfully!',
    'simulation_failed' => 'Interview simulation failed. Please try again.',
    'service_error' => 'Service error occurred. Please try again later.',
    'personality_analysis_title' => 'Personality Analysis',
    'personality_analysis_description' => 'Comprehensive analysis of your personality type and strengths',
    'cv_improvement_title' => 'CV Improvement',
    'cv_improvement_description' => 'AI-powered improvements and recommendations for your CV',
    'interview_simulation_title' => 'Interview Simulation',
    'interview_simulation_description' => 'Practice job interviews with performance evaluation',
    'service_results' => 'Service Results',
    'service_history' => 'Service History',
    'view_result' => 'View Result',
    'service_date' => 'Service Date',
    'service_score' => 'Score',
    'analysis_results' => 'Analysis Results',
    'personality_type' => 'Personality Type',
    'strengths' => 'Strengths',
    'recommendations' => 'Recommendations',
    'career_suggestions' => 'Career Suggestions',
    'summary' => 'Summary',
    'improvement_results' => 'Improvement Results',
    'overall_score' => 'Overall Score',
    'improvements' => 'Improvements',
    'missing_sections' => 'Missing Sections',
    'keyword_suggestions' => 'Keyword Suggestions',
    'interview_results' => 'Interview Results',
    'overall_performance' => 'Overall Performance',
    'communication_skills' => 'Communication Skills',
    'technical_knowledge' => 'Technical Knowledge',
    'confidence_level' => 'Confidence Level',
    'areas_for_improvement' => 'Areas for Improvement',
    'back_to_profile' => 'Back to Profile',
    'view_history' => 'View History',
    'start_now' => 'Start Now',

    // Personality Analysis Questions
    'personality_questions' => 'Personality Analysis Questions',
    'question_1' => 'How would you describe yourself in a work environment?',
    'question_2' => 'What are your main strengths?',
    'question_3' => 'How do you handle challenges and pressure?',
    'question_4' => 'What is your communication style with others?',
    'question_5' => 'What are your future career goals?',
    'answer_placeholder' => 'Write your answer here...',
    'analyze_personality' => 'Analyze Personality',
    'about_analysis' => 'About the Analysis',
    'analysis_info' => 'Personality analysis uses artificial intelligence to understand your personality type and provide personalized career recommendations.',
    'analysis_benefit_1' => 'Understand your personality type and strengths',
    'analysis_benefit_2' => 'Personalized career recommendations',
    'analysis_benefit_3' => 'Personal development plan',
    'analysis_benefit_4' => 'Tips to improve professional performance',
    'ai_service_results' => 'AI Service Results',
    'no_ai_results_yet' => 'No AI service results yet',
    'try_ai_services' => 'Try AI Services',
    'view_all_results' => 'View All Results',
    'service_history_description' => 'Browse the history of all AI services you have used',
    'score' => 'Score',
    'completed' => 'Completed',
    'in_progress' => 'In Progress',
    'failed' => 'Failed',
    'view_details' => 'View Details',
    'no_service_history' => 'No Service History',
    'no_service_history_desc' => 'You haven\'t used any AI services yet. Start now and discover your potential!',

    // Service Levels
    'service_levels' => 'Service Levels',
    'basic_level' => 'Basic Level',
    'premium_level' => 'Premium Level',
    'professional_level' => 'Professional Level',
    'choose_level' => 'Choose Level',
    'level_features' => 'Level Features',
    'get_started' => 'Get Started',
    'most_popular' => 'Most Popular',
    'recommended' => 'Recommended',
    'select_this_level' => 'Select This Level',

    // Personality Analysis Levels
    'personality_basic' => 'Basic Analysis',
    'personality_basic_desc' => 'Basic personality analysis with key insights',
    'personality_basic_price' => '19 DH',
    'personality_basic_duration' => '15-20 minutes',

    'personality_premium' => 'Premium Analysis',
    'personality_premium_desc' => 'Comprehensive personality analysis with detailed insights',
    'personality_premium_price' => '39 DH',
    'personality_premium_duration' => '25-30 minutes',

    'personality_professional' => 'Professional Analysis',
    'personality_professional_desc' => 'Professional personality analysis with personal consultation',
    'personality_professional_price' => '69 DH',
    'personality_professional_duration' => '35-40 minutes',

    // CV Improvement Levels
    'cv_basic' => 'Basic Improvement',
    'cv_basic_desc' => 'Basic CV improvement with general tips',
    'cv_basic_price' => '25 DH',
    'cv_basic_duration' => '10-15 minutes',

    'cv_premium' => 'Premium Improvement',
    'cv_premium_desc' => 'Comprehensive CV improvement with detailed analysis',
    'cv_premium_price' => '49 DH',
    'cv_premium_duration' => '20-25 minutes',

    'cv_professional' => 'Professional Improvement',
    'cv_professional_desc' => 'Professional improvement with complete rewrite',
    'cv_professional_price' => '79 DH',
    'cv_professional_duration' => '30-35 minutes',

    // Interview Simulation Levels
    'interview_basic' => 'Basic Simulation',
    'interview_basic_desc' => 'Basic interview simulation with general questions',
    'interview_basic_price' => '29 DH',
    'interview_basic_duration' => '20-25 minutes',

    'interview_premium' => 'Premium Simulation',
    'interview_premium_desc' => 'Comprehensive interview simulation with detailed feedback',
    'interview_premium_price' => '55 DH',
    'interview_premium_duration' => '30-35 minutes',

    'interview_professional' => 'Professional Simulation',
    'interview_professional_desc' => 'Professional interview simulation with personal coaching',
    'interview_professional_price' => '89 DH',
    'interview_professional_duration' => '40-45 minutes',

    // Payment Modal
    'payment_modal_title' => 'Complete Payment',
    'service_details' => 'Service Details',
    'selected_service' => 'Selected Service',
    'selected_level' => 'Selected Level',
    'price' => 'Price',
    'duration' => 'Duration',
    'payment_method' => 'Payment Method',
    'pay_with_card' => 'Pay with Card',
    'pay_with_paypal' => 'Pay with PayPal',
    'proceed_payment' => 'Proceed to Payment',
    'cancel_payment' => 'Cancel',
    'processing_payment' => 'Processing payment...',
    'payment_success' => 'Payment successful!',
    'payment_failed' => 'Payment failed. Please try again.',
    'login_required' => 'Login required',
    'login_to_continue' => 'Please login to continue with payment',

    // Test Types
    'available_test_types' => 'Available Test Types',
    'comprehensive_scientific_tests' => 'We provide a comprehensive range of certified scientific tests to analyze your personality accurately',
    'discover_personality' => 'Discover your personality type and strengths through advanced scientific tests',
    'start_test_now' => 'Start Test Now',
    'show_sample_results' => 'Show Sample Results',
    'try_demo' => 'Try Demo',

    // CV Improvement
    'cv_improvement_hero_description' => 'Improve your CV using advanced artificial intelligence and get comprehensive analysis and personalized recommendations',
    'upload_cv' => 'Upload Your CV',
    'create_new_cv' => 'Create New CV',
    'upload_cv_for_analysis' => 'Upload Your CV for Analysis',
    'supported_file_formats' => 'Supported files: PDF, DOC, DOCX',

    // Interview Simulation
    'interview_simulation_description' => 'Practice job interviews with advanced AI and receive instant feedback',
    'interview_simulation_hero_description' => 'Practice job interviews with advanced AI and get instant evaluation of your performance',
    'start_trial_interview' => 'Start Trial Interview',
    'interview_types' => 'Interview Types',
    'available_interview_types' => 'Available Interview Types',
    'choose_interview_type_to_practice' => 'Choose the type of interview you want to practice',

    // Service Levels
    'basic_level' => 'Basic Level',
    'professional_level' => 'Professional Level',
    'free' => 'Free',
    'recommended' => 'Recommended',
    'start_free' => 'Start Free',
    'get_professional' => 'Get Professional',

    // Basic Service Descriptions
    'basic_personality_desc' => 'Simple personality analysis with basic results',
    'basic_cv_desc' => 'Create simple CV with standard templates',
    'basic_interview_desc' => 'Basic interview simulation with general questions',

    // Professional Service Descriptions
    'professional_personality_desc' => 'Advanced AI-powered personality analysis with detailed report and personal consultation',
    'professional_cv_desc' => 'Professional AI-powered CV creation with advanced analysis and personalized recommendations',
    'professional_interview_desc' => 'Advanced AI-powered interview simulation with comprehensive evaluation and personal coaching',

    // Professional Prices
    'personality_pro_price' => '79 DH',
    'personality_pro_duration' => '45-60 minutes',
    'cv_pro_price' => '89 DH',
    'cv_pro_duration' => '30-45 minutes',
    'interview_pro_price' => '99 DH',
    'interview_pro_duration' => '60-90 minutes',

    // Free Service Pages
    'upgrade_to_professional' => 'Upgrade to Professional',
    'create_cv_now' => 'Create CV Now',
    'start_interview' => 'Start Interview',
    'upgrade_for_more' => 'Upgrade for More',
    'professional_features_include' => 'Professional features include: Advanced AI analysis, detailed reports, personal consultation',
    'upgrade_now' => 'Upgrade Now',
    'retake_test' => 'Retake Test',
    'explore_other_services' => 'Explore Other Services',
    'test_completed' => 'Test Completed',
    'basic_results_ready' => 'Basic results are ready',
    'personality_type' => 'Personality Type',
    'main_traits' => 'Main Traits',
    'analyzing' => 'Analyzing',
    'sample_personality_type' => 'Extroverted Personality',
    'trait_1' => 'Social and interactive',
    'trait_2' => 'Optimistic and energetic',
    'trait_3' => 'Enjoys teamwork',

    // CV Builder
    'basic_cv_builder' => 'Basic CV Builder',
    'fill_basic_info' => 'Fill in basic information to create your CV',
    'personal_information' => 'Personal Information',
    'full_name' => 'Full Name',
    'email' => 'Email',
    'phone' => 'Phone',
    'location' => 'Location',
    'professional_summary' => 'Professional Summary',
    'summary_placeholder' => 'Write a brief professional summary of your experience and skills',
    'work_experience' => 'Work Experience',
    'job_title' => 'Job Title',
    'company' => 'Company',
    'start_date' => 'Start Date',
    'end_date' => 'End Date',
    'job_description' => 'Job Description',
    'add_experience' => 'Add Experience',
    'remove' => 'Remove',
    'education' => 'Education',
    'degree' => 'Degree',
    'institution' => 'Institution',
    'graduation_year' => 'Graduation Year',
    'skills' => 'Skills',
    'skills_placeholder' => 'List your skills separated by commas',
    'generate_cv' => 'Generate CV',
    'cv_preview' => 'CV Preview',
    'edit' => 'Edit',
    'download' => 'Download',
    'upgrade_for_better_cv' => 'Upgrade for Better CV',
    'professional_cv_features' => 'Professional features: Advanced templates, AI analysis, custom optimizations',
    'download_feature_coming_soon' => 'Download feature coming soon!',

    // Interview Simulation
    'interview_setup' => 'Interview Setup',
    'interview_type' => 'Interview Type',
    'general_interview' => 'General Interview',
    'behavioral_interview' => 'Behavioral Interview',
    'technical_interview' => 'Technical Interview',
    'job_position' => 'Job Position',
    'job_position_placeholder' => 'e.g., Software Developer',
    'experience_level' => 'Experience Level',
    'entry_level' => 'Entry Level',
    'mid_level' => 'Mid Level',
    'senior_level' => 'Senior Level',
    'interview_in_progress' => 'Interview in Progress',
    'interviewer' => 'Interviewer',
    'your_answer' => 'Your Answer',
    'type_your_answer' => 'Type your answer here...',
    'words' => 'words',
    'skip' => 'Skip',
    'next_question' => 'Next Question',
    'interview_completed' => 'Interview Completed',
    'basic_feedback_ready' => 'Basic feedback is ready',
    'overall_performance' => 'Overall Performance',
    'completion_time' => 'Completion Time',
    'basic_feedback' => 'Basic Feedback',
    'feedback_1' => 'You answered most questions clearly',
    'feedback_2' => 'You showed good understanding of requirements',
    'feedback_3' => 'Some answers could use more detail',
    'upgrade_for_detailed_feedback' => 'Upgrade for Detailed Feedback',
    'professional_interview_features' => 'Professional features: Advanced analysis, voice evaluation, personal coaching',
    'retake_interview' => 'Retake Interview',

    // Interview Questions
    'interview_question_1' => 'Tell me about yourself and your professional experience',
    'interview_question_2' => 'Why do you want to work in this position?',
    'interview_question_3' => 'What are your main strengths?',
    'interview_question_4' => 'How do you handle work pressure and deadlines?',
    'interview_question_5' => 'Where do you see yourself in 5 years?',

    // Test Questions
    'question_1' => 'I feel comfortable talking to new people',
    'strongly_disagree' => 'Strongly Disagree',
    'disagree' => 'Disagree',
    'neutral' => 'Neutral',
    'agree' => 'Agree',
    'strongly_agree' => 'Strongly Agree',
    'loading_question' => 'Loading question...',
    'previous' => 'Previous',
    'next' => 'Next',
    'finish' => 'Finish',
    'personality_test' => 'Personality Test',

    // Payment System
    'payment_for_service' => 'Payment for Service',
    'secure_payment_process' => 'Secure and protected payment process',
    'complete_payment' => 'Complete Payment',
    'secure_payment_description' => 'Secure and protected payment process with latest security technologies',
    'service_details' => 'Service Details',
    'included_features' => 'Included Features',
    'delivery_time' => 'Delivery Time',
    'hours' => 'hours',
    'total_amount' => 'Total Amount',
    'payment_information' => 'Payment Information',
    'payment_method' => 'Payment Method',
    'pay_with_card' => 'Pay with Card',
    'pay_with_paypal' => 'Pay with PayPal',
    'customer_information' => 'Customer Information',
    'agree_to_terms_1' => 'I agree to the',
    'terms_and_conditions' => 'Terms and Conditions',
    'agree_to_terms_2' => 'and',
    'privacy_policy' => 'Privacy Policy',
    'pay_now' => 'Pay Now',
    'processing_payment' => 'Processing Payment',
    'secure_payment_notice' => 'All payments are secured with 256-bit SSL encryption',
    'please_accept_terms' => 'Please accept the terms and conditions',
    'payment_error' => 'An error occurred during payment. Please try again.',
    'payment_success' => 'Payment successful',

    // Payment Success
    'payment_successful' => 'Payment Successful',
    'payment_completed_successfully' => 'Payment completed successfully',
    'order_details' => 'Order Details',
    'order_number' => 'Order Number',
    'professional_service' => 'Professional Service',
    'amount_paid' => 'Amount Paid',
    'payment_date' => 'Payment Date',
    'status' => 'Status',
    'paid' => 'Paid',
    'what_happens_next' => 'What Happens Next',
    'payment_confirmed' => 'Payment Confirmed',
    'payment_confirmed_desc' => 'Your payment has been received and confirmed successfully',
    'processing_started' => 'Processing Started',
    'processing_started_desc' => 'Our team has started working on your order',
    'delivery_expected' => 'Delivery Expected',
    'delivery_expected_desc' => 'Your order will be delivered by',
    'go_to_dashboard' => 'Go to Dashboard',
    'explore_more_services' => 'Explore More Services',

    // Features
    'feature_ai_analysis' => 'AI Analysis',
    'feature_detailed_report' => 'Detailed Report',
    'feature_personal_consultation' => 'Personal Consultation',
    'feature_career_recommendations' => 'Career Recommendations',
    'feature_strengths_weaknesses' => 'Strengths & Weaknesses',
    'feature_ai_optimization' => 'AI Optimization',
    'feature_multiple_templates' => 'Multiple Templates',
    'feature_ats_optimization' => 'ATS Optimization',
    'feature_keyword_optimization' => 'Keyword Optimization',
    'feature_industry_specific' => 'Industry Specific',
    'feature_ai_interviewer' => 'AI Interviewer',
    'feature_voice_analysis' => 'Voice Analysis',
    'feature_detailed_feedback' => 'Detailed Feedback',
    'feature_performance_metrics' => 'Performance Metrics',
    'feature_improvement_plan' => 'Improvement Plan',
    'service_not_found' => 'Service not found',
    'login_to_continue' => 'Please login to continue',

    // AI Assistant Robot
    'ai_assistant' => 'AI Assistant',
    'robot_greeting' => 'Hello! I\'m your AI assistant at MonOri AI. How can I help you today?',
    'robot_help_message' => 'I can help you with:',
    'robot_help_services' => '• Learn more about our services',
    'robot_help_pricing' => '• Pricing inquiries',
    'robot_help_account' => '• Account management',
    'robot_help_technical' => '• Technical support',
    'robot_help_general' => '• Any general questions about the website',
    'type_message' => 'Type your message here...',
    'send_message' => 'Send',
    'speak_message' => 'Speak',
    'listening' => 'Listening...',
    'robot_thinking' => 'Thinking...',
    'robot_error' => 'Sorry, an error occurred. Please try again.',
    'robot_no_speech' => 'I couldn\'t hear you. Please try again.',
    'close_chat' => 'Close Chat',
    'minimize_chat' => 'Minimize',
    'maximize_chat' => 'Maximize',
    'clear_chat' => 'Clear Chat',
    'robot_welcome_back' => 'Welcome back! How can I help you?',
];
