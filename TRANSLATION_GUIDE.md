# دليل نظام الترجمة - MonOri AI

## نظرة عامة
يدعم موقع MonOri AI ثلاث لغات:
- **العربية (ar)** - اللغة الافتراضية
- **الإنجليزية (en)**
- **الفرنسية (fr)**

## كيفية عمل النظام

### 1. تغيير اللغة
يمكن للمستخدمين تغيير اللغة من خلال:
- النقر على أيقونة اللغة في الـ navbar
- اختيار اللغة المطلوبة من القائمة المنسدلة
- سيتم حفظ اللغة في الـ session وتطبيقها على كامل الموقع

### 2. ملفات الترجمة
تقع ملفات الترجمة في:
```
lang/
├── ar/
│   └── messages.php
├── en/
│   └── messages.php
└── fr/
    └── messages.php
```

### 3. استخدام الترجمة في الكود
```php
// في ملفات Blade
{{ __('messages.welcome_back') }}

// مع متغيرات
{{ __('messages.welcome_user', ['name' => $userName]) }}

// في PHP
__('messages.error')
```

### 4. إضافة ترجمة جديدة

#### الخطوة 1: إضافة النص في ملف العربية
```php
// lang/ar/messages.php
'new_message' => 'النص الجديد باللغة العربية',
```

#### الخطوة 2: إضافة الترجمة الإنجليزية
```php
// lang/en/messages.php
'new_message' => 'New text in English',
```

#### الخطوة 3: إضافة الترجمة الفرنسية
```php
// lang/fr/messages.php
'new_message' => 'Nouveau texte en français',
```

#### الخطوة 4: استخدام الترجمة
```php
{{ __('messages.new_message') }}
```

## الميزات المتقدمة

### 1. دعم RTL للعربية
- يتم تطبيق `dir="rtl"` تلقائياً للغة العربية
- CSS مخصص لدعم التخطيط من اليمين لليسار

### 2. خطوط مخصصة لكل لغة
- العربية: خط Cairo
- الإنجليزية والفرنسية: خط Inter

### 3. حفظ تفضيل اللغة
- يتم حفظ اللغة المختارة في الـ session
- تبقى اللغة محفوظة أثناء تصفح الموقع

## الصفحات المترجمة

### ✅ مكتملة الترجمة:
- الصفحة الرئيسية
- صفحة تحليل الشخصية (جزئياً)
- صفحة تسجيل الدخول (جزئياً)
- لوحة التحكم (جزئياً)
- Navbar و Footer

### 🔄 تحتاج ترجمة إضافية:
- صفحة التوجيه المهني
- صفحة تحسين السيرة الذاتية
- صفحة محاكاة المقابلات
- صفحة التسجيل

## إرشادات للمطورين

### 1. قواعد التسمية
- استخدم أسماء وصفية للمفاتيح
- اجمع المفاتيح المترابطة معاً
- استخدم underscore للفصل بين الكلمات

### 2. تنظيم الملفات
```php
// تجميع حسب الوظيفة
'auth_welcome_back' => 'مرحباً بعودتك',
'auth_login_subtitle' => 'سجل دخولك للوصول إلى حسابك',

// أو حسب الصفحة
'dashboard_welcome_user' => 'مرحباً، :name',
'dashboard_progress_summary' => 'إليك ملخص تقدمك المهني',
```

### 3. التعامل مع المتغيرات
```php
// في ملف الترجمة
'welcome_user' => 'مرحباً، :name',

// في الاستخدام
{{ __('messages.welcome_user', ['name' => $user->name]) }}
```

## اختبار النظام

### 1. اختبار تغيير اللغة
1. افتح الموقع
2. انقر على أيقونة اللغة
3. اختر لغة مختلفة
4. تأكد من تغيير جميع النصوص

### 2. اختبار RTL
1. غير اللغة للعربية
2. تأكد من تغيير اتجاه النص
3. تأكد من صحة التخطيط

### 3. اختبار الخطوط
1. تأكد من ظهور الخط المناسب لكل لغة
2. تأكد من وضوح النص في جميع اللغات

## المشاكل الشائعة وحلولها

### 1. النص لا يتغير عند تغيير اللغة
- تأكد من استخدام `{{ __('messages.key') }}` بدلاً من النص المباشر
- تأكد من وجود المفتاح في جميع ملفات الترجمة

### 2. اتجاه النص خاطئ
- تأكد من إضافة `dir="{{ app()->getLocale() === 'ar' ? 'rtl' : 'ltr' }}"` في HTML

### 3. الخط لا يظهر بشكل صحيح
- تأكد من تحميل الخطوط في CSS
- تأكد من تطبيق الخط المناسب لكل لغة

## خطة التطوير المستقبلية

### المرحلة التالية:
1. إكمال ترجمة جميع الصفحات
2. إضافة ترجمة للرسائل والتنبيهات
3. ترجمة رسائل الأخطاء
4. إضافة دعم لغات إضافية (إسبانية، ألمانية)

### تحسينات مقترحة:
1. نظام إدارة الترجمات من لوحة التحكم
2. كشف اللغة تلقائياً حسب موقع المستخدم
3. ترجمة المحتوى الديناميكي
4. دعم الترجمة الآلية للمحتوى الجديد

---

**ملاحظة:** هذا النظام قابل للتوسع ويمكن إضافة لغات جديدة بسهولة عبر إنشاء مجلد جديد في `lang/` وإضافة ملف `messages.php` مع الترجمات المطلوبة.
