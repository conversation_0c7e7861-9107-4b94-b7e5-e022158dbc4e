<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\AuthController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\PaymentController;
use App\Http\Controllers\LanguageController;

Route::get('/', function () {
    return view('home');
})->name('home');

Route::get('/about', function () {
    return view('about');
})->name('about');

Route::get('/contact', function () {
    return view('contact');
})->name('contact');

// AI Chat Routes
Route::post('/ai-chat', [App\Http\Controllers\AIChatController::class, 'chat'])->name('ai.chat');



Route::get('/pricing', [App\Http\Controllers\SubscriptionController::class, 'pricing'])->name('pricing');

// Services Routes
Route::get('/services', function () {
    return view('services');
})->name('services');

Route::get('/services/personality-analysis', function () {
    return view('services.personality-analysis');
})->name('services.personality-analysis');

Route::get('/services/career-guidance', function () {
    return view('services.career-guidance');
})->name('services.career-guidance');

Route::get('/services/cv-improvement', function () {
    return view('services.cv-improvement');
})->name('services.cv-improvement');

Route::get('/services/interview-simulation', function () {
    return view('services.interview-simulation');
})->name('services.interview-simulation');

// Free Service Routes
Route::get('/services/personality-analysis-free', function () {
    return view('services.free.personality-analysis');
})->name('services.personality-analysis-free');

Route::get('/services/cv-improvement-free', function () {
    return view('services.free.cv-improvement');
})->name('services.cv-improvement-free');

Route::get('/services/interview-simulation-free', function () {
    return view('services.free.interview-simulation');
})->name('services.interview-simulation-free');

// Payment Routes
Route::middleware(['auth'])->group(function () {
    Route::get('/payment/service', [PaymentController::class, 'showServicePayment'])->name('payment.service');
    Route::post('/payment/process-service', [PaymentController::class, 'processServicePayment'])->name('payment.process-service');
    Route::get('/payment/success/{order}', [PaymentController::class, 'showPaymentSuccess'])->name('services.order.success');
});

// Additional service routes
Route::get('/personality-analysis', function () {
    return view('services.personality-analysis');
})->name('personality-analysis');

Route::get('/cv-generator', function () {
    return view('services.cv-improvement');
})->name('cv-generator');

Route::get('/job-recommendations', function () {
    return view('services.career-guidance');
})->name('job-recommendations');

// Public Service Routes - No Authentication Required
Route::get('/services/personality-analysis-demo', [App\Http\Controllers\AIServiceController::class, 'personalityAnalysis'])->name('services.personality-analysis-demo');
Route::get('/services/cv-improvement-demo', [App\Http\Controllers\AIServiceController::class, 'cvImprovement'])->name('services.cv-improvement-demo');
Route::get('/services/interview-simulation-demo', [App\Http\Controllers\AIServiceController::class, 'interviewSimulation'])->name('services.interview-simulation-demo');

// Public demo processing routes
Route::post('/services/demo/personality-analysis', [App\Http\Controllers\AIServiceController::class, 'demoPersonalityAnalysis'])->name('services.demo.personality-analysis');
Route::post('/services/demo/cv-improvement', [App\Http\Controllers\AIServiceController::class, 'demoCvImprovement'])->name('services.demo.cv-improvement');
Route::post('/services/demo/interview-simulation', [App\Http\Controllers\AIServiceController::class, 'demoInterviewSimulation'])->name('services.demo.interview-simulation');

// Authentication Routes
Route::get('/login', [AuthController::class, 'showLoginForm'])->name('login')->middleware('guest');
Route::post('/login', [AuthController::class, 'login'])->middleware('guest');

Route::get('/register', [AuthController::class, 'showRegisterForm'])->name('register')->middleware('guest');
Route::post('/register', [AuthController::class, 'register'])->middleware('guest');

Route::post('/logout', [AuthController::class, 'logout'])->name('logout')->middleware('auth');

// Profile Routes (replacing dashboard)
Route::middleware('auth')->group(function () {
    Route::get('/profile', [ProfileController::class, 'show'])->name('profile');
    Route::get('/profile/activities', [ProfileController::class, 'activities'])->name('profile.activities');
    Route::get('/dashboard', function() { return redirect()->route('profile'); })->name('dashboard'); // Redirect dashboard to profile
    Route::get('/profile/edit', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::put('/profile', [ProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/share', [ProfileController::class, 'share'])->name('profile.share');
});

// Public profile (for sharing)
Route::get('/user/{user}/profile', [ProfileController::class, 'show'])->name('profile.public');

Route::get('/set-language/{locale}', [LanguageController::class, 'setLanguage'])->name('set-language');

// Quick admin login (for development only)
Route::get('/admin-login', function () {
    $admin = App\Models\User::where('is_admin', true)->first();
    if ($admin) {
        Auth::login($admin);
        return redirect()->route('admin.dashboard')->with('success', 'تم تسجيل الدخول بنجاح');
    }
    return redirect()->route('login')->with('error', 'لم يتم العثور على مستخدم admin');
});

// Admin Routes
Route::prefix('admin')->name('admin.')->middleware(['auth', 'admin'])->group(function () {
    Route::get('/dashboard', [App\Http\Controllers\Admin\AdminController::class, 'dashboard'])->name('dashboard');
    Route::get('/users', [App\Http\Controllers\Admin\AdminController::class, 'users'])->name('users');
    Route::get('/analytics', [App\Http\Controllers\Admin\AdminController::class, 'analytics'])->name('analytics');
    Route::get('/settings', [App\Http\Controllers\Admin\AdminController::class, 'settings'])->name('settings');

    // Dynamic Payment Management Routes
    Route::prefix('payments')->name('payments.')->group(function () {
        Route::get('/', [App\Http\Controllers\Admin\PaymentController::class, 'index'])->name('index');
        Route::get('/{transaction}', [App\Http\Controllers\Admin\PaymentController::class, 'show'])->name('show');
        Route::get('/analytics/data', [App\Http\Controllers\Admin\PaymentController::class, 'analytics'])->name('analytics');
        Route::get('/export/csv', [App\Http\Controllers\Admin\PaymentController::class, 'export'])->name('export');
        Route::post('/notifications/read', [App\Http\Controllers\Admin\PaymentController::class, 'markNotificationsRead'])->name('notifications.read');
        Route::get('/notifications/count', [App\Http\Controllers\Admin\PaymentController::class, 'getUnreadNotificationsCount'])->name('notifications.count');
    });
});

// Redirect admin root to dashboard
Route::get('/admin', function () {
    return redirect()->route('admin.dashboard');
})->middleware(['auth', 'admin']);



// AI Routes
Route::prefix('ai')->name('ai.')->middleware(['auth'])->group(function () {
    Route::post('/personality-analysis', [App\Http\Controllers\AIController::class, 'analyzePersonality'])->name('personality.analyze');
    Route::get('/personality-analysis', [App\Http\Controllers\AIController::class, 'getPersonalityAnalysis'])->name('personality.get');
    Route::post('/generate-cv', [App\Http\Controllers\AIController::class, 'generateCV'])->name('cv.generate');
    Route::post('/recommend-jobs', [App\Http\Controllers\AIController::class, 'recommendJobs'])->name('jobs.recommend');
    Route::post('/chat', [App\Http\Controllers\AIController::class, 'chat'])->name('chat');
});

// CV Builder Routes
Route::prefix('cv-builder')->name('cv.')->group(function () {
    Route::get('/', [App\Http\Controllers\CVBuilderController::class, 'index'])->name('index');
    Route::get('/create-old', [App\Http\Controllers\CVBuilderController::class, 'create'])->name('create-old')->middleware('auth');
    Route::get('/create', [App\Http\Controllers\CVBuilderController::class, 'create'])->name('create')->middleware('auth');
    Route::get('/{id}/data', [App\Http\Controllers\CVBuilderController::class, 'getCVData'])->name('data');
    Route::get('/preview', [App\Http\Controllers\CVBuilderController::class, 'preview'])->name('preview');
    Route::get('/download', [App\Http\Controllers\CVBuilderController::class, 'download'])->name('download');

    // AI-powered CV features
    Route::post('/ai-quick-generate', [App\Http\Controllers\CVBuilderController::class, 'aiQuickGenerate'])->name('ai.quick.generate');
    Route::post('/template-recommendations', [App\Http\Controllers\CVBuilderController::class, 'getTemplateRecommendations'])->name('template.recommendations');
    Route::post('/preview', [App\Http\Controllers\CVBuilderController::class, 'generatePreview'])->name('preview');
    Route::post('/store', [App\Http\Controllers\CVBuilderController::class, 'store'])->name('store');
    Route::post('/ai-suggestions', [App\Http\Controllers\CVBuilderController::class, 'getAISuggestions'])->name('suggestions');
    Route::post('/ai-review', [App\Http\Controllers\CVBuilderController::class, 'performAIReview'])->name('ai-review');
    Route::post('/ai-optimize', [App\Http\Controllers\CVBuilderController::class, 'optimizeWithAI'])->name('ai-optimize');
    Route::post('/generate-ai', [App\Http\Controllers\CVBuilderController::class, 'generateWithAI'])->name('generate.ai')->middleware('auth');
    Route::post('/improve-ai', [App\Http\Controllers\CVBuilderController::class, 'improveWithAI'])->name('improve.ai')->middleware('auth');
    Route::post('/ai-job-description', [App\Http\Controllers\CVBuilderController::class, 'generateJobDescription'])->name('ai-job-description');
    Route::post('/generate-preview', [App\Http\Controllers\CVBuilderController::class, 'generatePreview'])->name('generate-preview');
    Route::get('/templates', [App\Http\Controllers\CVBuilderController::class, 'getTemplates'])->name('templates');
    Route::post('/clear', [App\Http\Controllers\CVBuilderController::class, 'clear'])->name('clear');

    // Authenticated routes
    Route::middleware('auth')->group(function () {
        Route::get('/my-cvs', [App\Http\Controllers\CVBuilderController::class, 'myCvs'])->name('my-cvs');
        Route::get('/{id}/edit', [App\Http\Controllers\CVBuilderController::class, 'edit'])->name('edit');
        Route::put('/{id}', [App\Http\Controllers\CVBuilderController::class, 'update'])->name('update');
        Route::delete('/{id}', [App\Http\Controllers\CVBuilderController::class, 'destroy'])->name('destroy');
    });
});

// Subscription Routes
Route::prefix('subscription')->name('subscription.')->middleware(['auth'])->group(function () {
    Route::post('/subscribe/{plan}', [App\Http\Controllers\SubscriptionController::class, 'subscribe'])->name('subscribe');
    Route::get('/status', [App\Http\Controllers\SubscriptionController::class, 'getSubscriptionStatus'])->name('status');
    Route::post('/cancel', [App\Http\Controllers\SubscriptionController::class, 'cancel'])->name('cancel');
});

// Service Order Routes
Route::prefix('services')->name('services.')->middleware(['auth'])->group(function () {
    Route::post('/order/{service}', [App\Http\Controllers\SubscriptionController::class, 'orderService'])->name('order');
    Route::get('/orders', [App\Http\Controllers\SubscriptionController::class, 'userOrders'])->name('orders');
});

// Payment Routes
Route::prefix('payment')->name('payment.')->group(function () {
    // Subscription payments
    Route::post('/subscribe', [App\Http\Controllers\PaymentController::class, 'subscribe'])->name('subscribe')->middleware('auth');
    Route::post('/service/order', [App\Http\Controllers\PaymentController::class, 'orderService'])->name('service.order')->middleware('auth');

    // Checkout pages
    Route::get('/stripe/checkout/{payment}', [App\Http\Controllers\PaymentController::class, 'stripeCheckout'])->name('stripe.checkout')->middleware('auth');
    Route::get('/paypal/checkout/{payment}', [App\Http\Controllers\PaymentController::class, 'paypalCheckout'])->name('paypal.checkout')->middleware('auth');

    // Service checkout
    Route::get('/stripe/service/{payment}', [App\Http\Controllers\PaymentController::class, 'stripeCheckout'])->name('stripe.service')->middleware('auth');
    Route::get('/paypal/service/{payment}', [App\Http\Controllers\PaymentController::class, 'paypalCheckout'])->name('paypal.service')->middleware('auth');

    // Webhooks (no auth needed)
    Route::post('/stripe/webhook', [App\Http\Controllers\PaymentController::class, 'stripeWebhook'])->name('stripe.webhook');
    Route::post('/paypal/webhook', [App\Http\Controllers\PaymentController::class, 'paypalWebhook'])->name('paypal.webhook');

    // Success/Cancel pages
    Route::get('/success', [App\Http\Controllers\PaymentController::class, 'success'])->name('success')->middleware('auth');
    Route::get('/cancel', [App\Http\Controllers\PaymentController::class, 'cancel'])->name('cancel')->middleware('auth');

    // Payment history
    Route::get('/history', [App\Http\Controllers\PaymentController::class, 'history'])->name('history')->middleware('auth');

    // Cancel subscription
    Route::post('/subscription/cancel', [App\Http\Controllers\PaymentController::class, 'cancelSubscription'])->name('subscription.cancel')->middleware('auth');
});

// Test AI Route
Route::get('/test-ai', function () {
    return view('test-ai');
})->name('test.ai');

// Test Services Route
Route::get('/test-services', function () {
    $services = App\Models\PaidService::select('id', 'name_en', 'type', 'price')->get();
    return response()->json($services);
});

// Test AI Generate Route (without auth for testing)
Route::post('/test-ai/generate', [App\Http\Controllers\CVBuilderController::class, 'generateWithAI'])->name('test.ai.generate');

// Quick Login Route
Route::get('/quick-login', function () {
    return view('quick-login');
})->name('quick.login');



// Customer Service Robot Routes
Route::prefix('customer-service')->name('customer-service.')->group(function () {
    Route::post('/chat', [App\Http\Controllers\CustomerServiceController::class, 'chat'])->name('chat');
    Route::post('/quick-action', [App\Http\Controllers\CustomerServiceController::class, 'quickAction'])->name('quick-action');
});

// Dynamic Payment Routes
Route::middleware('auth')->prefix('payment')->name('payment.')->group(function () {
    Route::get('/service/{service}', [App\Http\Controllers\DynamicPaymentController::class, 'show'])->name('show');
    Route::post('/service/{service}', [App\Http\Controllers\DynamicPaymentController::class, 'process'])->name('process');
    Route::post('/stripe/{transaction}', [App\Http\Controllers\DynamicPaymentController::class, 'processStripe'])->name('stripe');
    Route::post('/paypal/{transaction}', [App\Http\Controllers\DynamicPaymentController::class, 'processPayPal'])->name('paypal');
    Route::get('/success/{transaction}', [App\Http\Controllers\DynamicPaymentController::class, 'success'])->name('success');
    Route::get('/failed/{transaction}', [App\Http\Controllers\DynamicPaymentController::class, 'failed'])->name('failed');
    Route::get('/status/{transaction}', [App\Http\Controllers\DynamicPaymentController::class, 'status'])->name('status');
});



// Dynamic AI Services Routes - Public Access
Route::prefix('ai-services')->name('ai-service.')->group(function () {
    // Personality Analysis - Public Access
    Route::get('/personality-analysis', [App\Http\Controllers\AIServiceController::class, 'personalityAnalysis'])->name('personality-analysis');
    Route::post('/personality-analysis', [App\Http\Controllers\AIServiceController::class, 'processPersonalityAnalysis'])->name('personality-analysis.process');

    // CV Improvement - Public Access
    Route::get('/cv-improvement', [App\Http\Controllers\AIServiceController::class, 'cvImprovement'])->name('cv-improvement');
    Route::post('/cv-improvement', [App\Http\Controllers\AIServiceController::class, 'processCvImprovement'])->name('cv-improvement.process');

    // Interview Simulation - Public Access
    Route::get('/interview-simulation', [App\Http\Controllers\AIServiceController::class, 'interviewSimulation'])->name('interview-simulation');
    Route::post('/interview-simulation', [App\Http\Controllers\AIServiceController::class, 'processInterviewSimulation'])->name('interview-simulation.process');

    // Results and History - Require Auth
    Route::get('/result/{id}', [App\Http\Controllers\AIServiceController::class, 'showResult'])->name('result')->middleware('auth');
    Route::get('/history', [App\Http\Controllers\AIServiceController::class, 'history'])->name('history')->middleware('auth');
});
