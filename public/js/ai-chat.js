// AI Chat Assistant for MonOri AI
class AIChatAssistant {
    constructor() {
        this.isOpen = false;
        this.isListening = false;
        this.recognition = null;
        this.synthesis = window.speechSynthesis;
        this.currentLanguage = document.documentElement.lang || 'ar';
        
        this.initializeElements();
        this.initializeEventListeners();
        this.initializeSpeechRecognition();
        
        // Load chat history from localStorage
        this.loadChatHistory();
    }

    initializeElements() {
        this.chatToggle = document.getElementById('chatToggle');
        this.chatWindow = document.getElementById('chatWindow');
        this.chatMessages = document.getElementById('chatMessages');
        this.chatInput = document.getElementById('chatInput');
        this.sendButton = document.getElementById('sendButton');
        this.voiceButton = document.getElementById('voiceButton');
        this.closeChat = document.getElementById('closeChat');
        this.minimizeChat = document.getElementById('minimizeChat');
    }

    initializeEventListeners() {
        this.chatToggle.addEventListener('click', () => this.toggleChat());
        this.closeChat.addEventListener('click', () => this.closeChat());
        this.minimizeChat.addEventListener('click', () => this.minimizeChat());
        this.sendButton.addEventListener('click', () => this.sendMessage());
        this.voiceButton.addEventListener('click', () => this.toggleVoiceRecognition());
        
        this.chatInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.sendMessage();
            }
        });

        // Auto-resize chat input
        this.chatInput.addEventListener('input', () => {
            this.chatInput.style.height = 'auto';
            this.chatInput.style.height = this.chatInput.scrollHeight + 'px';
        });

        // Monitor robot position changes
        this.monitorRobotPosition();
    }

    initializeSpeechRecognition() {
        if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
            const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
            this.recognition = new SpeechRecognition();
            
            this.recognition.continuous = false;
            this.recognition.interimResults = false;
            this.recognition.lang = this.getLanguageCode();

            this.recognition.onstart = () => {
                this.isListening = true;
                this.updateVoiceButton();
            };

            this.recognition.onresult = (event) => {
                const transcript = event.results[0][0].transcript;
                this.chatInput.value = transcript;
                this.sendMessage();
            };

            this.recognition.onerror = (event) => {
                console.error('Speech recognition error:', event.error);
                this.showError('robot_no_speech');
            };

            this.recognition.onend = () => {
                this.isListening = false;
                this.updateVoiceButton();
            };
        }
    }

    getLanguageCode() {
        const langMap = {
            'ar': 'ar-SA',
            'en': 'en-US',
            'fr': 'fr-FR'
        };
        return langMap[this.currentLanguage] || 'ar-SA';
    }

    toggleChat() {
        if (this.isOpen) {
            this.closeChatWindow();
        } else {
            this.openChatWindow();
        }
    }

    openChatWindow() {
        // Position chat window next to robot
        this.positionChatNextToRobot();

        this.chatWindow.classList.remove('hidden');
        this.isOpen = true;
        this.chatInput.focus();

        // Scroll to bottom
        this.scrollToBottom();
    }

    positionChatNextToRobot() {
        const robot = document.getElementById('followingRobot');
        if (robot) {
            const robotRect = robot.getBoundingClientRect();
            const chatWindow = this.chatWindow;
            const arrow = chatWindow.querySelector('.absolute');

            // Position chat to the left of robot
            const leftPosition = robotRect.left - 320; // 320px = chat width + margin
            const topPosition = robotRect.top;

            // Check if there's enough space on the left
            if (leftPosition > 20) {
                // Position on left
                chatWindow.style.left = leftPosition + 'px';
                chatWindow.style.right = 'auto';

                // Arrow pointing right to robot
                if (arrow) {
                    arrow.className = 'absolute -right-2 top-6 w-4 h-4 bg-white dark:bg-gray-800 border-r border-b border-gray-200 dark:border-gray-700 transform rotate-45';
                }
            } else {
                // If not enough space on left, position on right
                chatWindow.style.left = (robotRect.right + 20) + 'px';
                chatWindow.style.right = 'auto';

                // Arrow pointing left to robot
                if (arrow) {
                    arrow.className = 'absolute -left-2 top-6 w-4 h-4 bg-white dark:bg-gray-800 border-l border-t border-gray-200 dark:border-gray-700 transform rotate-45';
                }
            }

            // Set top position
            chatWindow.style.top = Math.max(20, topPosition) + 'px';
            chatWindow.style.bottom = 'auto';

            // Make sure chat doesn't go off screen
            const maxTop = window.innerHeight - 400; // 400px = chat height
            if (topPosition > maxTop) {
                chatWindow.style.top = maxTop + 'px';
            }
        }
    }

    monitorRobotPosition() {
        // Update chat position when robot moves
        const robot = document.getElementById('followingRobot');
        if (robot) {
            // Use MutationObserver to watch for style changes
            const observer = new MutationObserver(() => {
                if (this.isOpen) {
                    this.positionChatNextToRobot();
                }
            });

            observer.observe(robot, {
                attributes: true,
                attributeFilter: ['style']
            });

            // Also update on window resize
            window.addEventListener('resize', () => {
                if (this.isOpen) {
                    this.positionChatNextToRobot();
                }
            });

            // Update on scroll
            window.addEventListener('scroll', () => {
                if (this.isOpen) {
                    this.positionChatNextToRobot();
                }
            });
        }
    }

    closeChatWindow() {
        this.chatWindow.classList.add('hidden');
        this.isOpen = false;
    }

    minimizeChatWindow() {
        this.closeChatWindow();
    }

    toggleVoiceRecognition() {
        if (!this.recognition) {
            this.showError('Speech recognition not supported');
            return;
        }

        if (this.isListening) {
            this.recognition.stop();
        } else {
            this.recognition.start();
        }
    }

    updateVoiceButton() {
        if (this.isListening) {
            this.voiceButton.innerHTML = `
                <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z"/>
                    <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
                </svg>
            `;
            this.voiceButton.classList.add('bg-red-100', 'dark:bg-red-900');
        } else {
            this.voiceButton.innerHTML = `
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z"></path>
                </svg>
            `;
            this.voiceButton.classList.remove('bg-red-100', 'dark:bg-red-900');
        }
    }

    async sendMessage() {
        const message = this.chatInput.value.trim();
        if (!message) return;

        // Add user message
        this.addMessage(message, 'user');
        this.chatInput.value = '';

        // Show typing indicator
        this.showTypingIndicator();

        try {
            // Get AI response
            const response = await this.getAIResponse(message);
            
            // Remove typing indicator
            this.removeTypingIndicator();
            
            // Add AI response
            this.addMessage(response, 'ai');
            
            // Speak response if synthesis is available
            this.speakMessage(response);
            
        } catch (error) {
            console.error('Error getting AI response:', error);
            this.removeTypingIndicator();
            this.showError('robot_error');
        }

        // Save chat history
        this.saveChatHistory();
    }

    addMessage(message, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'flex items-start space-x-2';
        
        if (sender === 'user') {
            messageDiv.className += ' flex-row-reverse space-x-reverse';
            messageDiv.innerHTML = `
                <div class="w-8 h-8 bg-gray-300 dark:bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-gray-600 dark:text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                    </svg>
                </div>
                <div class="bg-blue-500 text-white rounded-lg p-3 max-w-xs">
                    <p class="text-sm">${message}</p>
                </div>
            `;
        } else {
            messageDiv.innerHTML = `
                <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                    <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3 max-w-xs border border-blue-200 dark:border-blue-700">
                    <div class="flex items-center space-x-1 mb-1">
                        <div class="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span class="text-xs text-blue-600 dark:text-blue-400 font-medium">MonOri AI</span>
                    </div>
                    <p class="text-sm text-gray-800 dark:text-gray-200 leading-relaxed">${this.formatMessage(message)}</p>
                </div>
            `;
        }

        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }

    showTypingIndicator() {
        const typingDiv = document.createElement('div');
        typingDiv.id = 'typingIndicator';
        typingDiv.className = 'flex items-start space-x-2';
        typingDiv.innerHTML = `
            <div class="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center flex-shrink-0">
                <svg class="w-4 h-4 text-white animate-pulse" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                </svg>
            </div>
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-3 border border-blue-200 dark:border-blue-700">
                <div class="flex items-center space-x-2">
                    <div class="flex space-x-1">
                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                        <div class="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                        <div class="w-2 h-2 bg-blue-500 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                    </div>
                    <span class="text-xs text-blue-600 dark:text-blue-400 font-medium">الذكاء الاصطناعي يفكر...</span>
                </div>
            </div>
        `;

        this.chatMessages.appendChild(typingDiv);
        this.scrollToBottom();
    }

    removeTypingIndicator() {
        const typingIndicator = document.getElementById('typingIndicator');
        if (typingIndicator) {
            typingIndicator.remove();
        }
    }

    async getAIResponse(message) {
        try {
            // First check for navigation requests (handle locally for instant response)
            const lowerMessage = message.toLowerCase();
            if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.navigation)) {
                return this.handleNavigation(lowerMessage);
            }

            // Get CSRF token
            const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

            // Send request to AI backend
            const response = await fetch('/ai-chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify({
                    message: message,
                    language: this.currentLanguage
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();

            if (data.success) {
                return data.response;
            } else {
                throw new Error(data.error || 'Unknown error');
            }

        } catch (error) {
            console.error('AI Response Error:', error);

            // Fallback to local responses
            return this.getFallbackResponse(message);
        }
    }

    getFallbackResponse(message) {
        const lowerMessage = message.toLowerCase();

        // Check for greetings first
        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.greeting)) {
            return this.getGreetingResponse();
        }

        // Check for login/register requests
        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.account)) {
            return this.getLoginInfo();
        }

        // Check for payment requests
        if (this.containsKeywords(lowerMessage, ['دفع', 'payment', 'pay', 'شراء', 'buy', 'payer', 'كيفاش نخلص', 'بغيت نشري'])) {
            return this.getPaymentInfo();
        }

        // Check for service-related questions
        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.services)) {
            return this.getServicesInfo();
        }

        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.pricing)) {
            return this.getPricingInfo();
        }

        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.personality)) {
            return this.getPersonalityInfo();
        }

        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.cv)) {
            return this.getCVInfo();
        }

        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.interview)) {
            return this.getInterviewInfo();
        }

        if (this.containsKeywords(lowerMessage, robotKnowledge.keywords.contact)) {
            return this.getContactInfo();
        }

        // Default response
        return this.getDefaultResponse();
    }

    containsKeywords(message, keywords) {
        return keywords.some(keyword => message.includes(keyword));
    }

    getServicesInfo() {
        return this.getResponseByLanguage({
            ar: `احنا كانقدمو ثلاث خدمات رئيسية:

🧠 تحليل الشخصية - من 19 إلى 69 درهم
📄 تحسين السيرة الذاتية - من 25 إلى 79 درهم
🎤 محاكاة المقابلات - من 29 إلى 89 درهم

كل خدمة عندها ثلاثة مستويات: أساسي، متقدم، واحترافي. شنو الخدمة اللي كاتهمك أكثر؟`,
            en: `We offer three main services:

🧠 Personality Analysis - from 19 to 69 DH
📄 CV Improvement - from 25 to 79 DH
🎤 Interview Simulation - from 29 to 89 DH

Each service has three levels: Basic, Premium, and Professional. Which service interests you most?`,
            fr: `Nous offrons trois services principaux:

🧠 Analyse de Personnalité - de 19 à 69 DH
📄 Amélioration de CV - de 25 à 79 DH
🎤 Simulation d'Entretien - de 29 à 89 DH

Chaque service a trois niveaux: Basique, Premium, et Professionnel. Quel service vous intéresse le plus?`
        });
    }

    getPricingInfo() {
        return `أسعارنا تنافسية جداً:

💰 تحليل الشخصية: 19-69 درهم
💰 تحسين السيرة الذاتية: 25-79 درهم
💰 محاكاة المقابلات: 29-89 درهم

لدينا أيضاً خطط اشتراك شهرية من 19 درهم. هل تريد معرفة المزيد عن خدمة معينة؟`;
    }

    getPersonalityInfo() {
        return `تحليل الشخصية يساعدك على:

✅ فهم نمط شخصيتك
✅ اكتشاف نقاط القوة والضعف
✅ الحصول على توصيات مهنية مخصصة
✅ تطوير خطة مهنية واضحة

المستوى الأساسي يبدأ من 19 درهم فقط! هل تريد البدء؟`;
    }

    getCVInfo() {
        return `خدمة تحسين السيرة الذاتية تشمل:

📝 تحليل السيرة الذاتية الحالية
🔧 تحسينات واقتراحات محددة
🎨 تحسين التصميم والتنسيق
🔍 إضافة الكلمات المفتاحية المناسبة

الأسعار تبدأ من 25 درهم. هل لديك سيرة ذاتية تريد تحسينها؟`;
    }

    getInterviewInfo() {
        return `محاكاة المقابلات تساعدك على:

🎯 التدرب على أسئلة المقابلات الشائعة
📊 تقييم أدائك وتحسينه
💪 زيادة ثقتك بنفسك
🏆 تحسين فرص نجاحك في المقابلات

الأسعار تبدأ من 29 درهم. هل تريد تجربة محاكاة مقابلة؟`;
    }

    getContactInfo() {
        return `يمكنك التواصل معنا عبر:

📧 البريد الإلكتروني: <EMAIL>
📞 الهاتف: +212 6XX XXX XXX
📍 العنوان: مركز الأعمال التكنولوجي، الدار البيضاء
⏰ ساعات العمل: الاثنين-الجمعة، 9:00-18:00

كيف يمكنني مساعدتك أكثر؟`;
    }

    getGreetingResponse() {
        const responses = this.getResponsesByLanguage({
            ar: [
                'أهلاً وسهلاً! كيف يمكنني مساعدتك اليوم؟ 😊',
                'مرحباً بك! أنا هنا لمساعدتك في أي شيء تحتاجه 🤖',
                'السلام عليكم! كيف الحال؟ شنو بغيتي نعاونك فيه؟ 😄'
            ],
            en: [
                'Hello! How can I help you today? 😊',
                'Welcome! I\'m here to assist you with anything you need 🤖',
                'Hi there! What can I do for you? 😄'
            ],
            fr: [
                'Bonjour! Comment puis-je vous aider aujourd\'hui? 😊',
                'Bienvenue! Je suis là pour vous aider avec tout ce dont vous avez besoin 🤖',
                'Salut! Que puis-je faire pour vous? 😄'
            ]
        });
        return responses[Math.floor(Math.random() * responses.length)];
    }

    handleNavigation(message) {
        const lang = this.currentLanguage;

        if (message.includes('خدمات') || message.includes('services') || message.includes('وديني للخدمات')) {
            setTimeout(() => {
                window.location.href = '/services';
            }, 1000);
            return this.getResponseByLanguage({
                ar: 'سأنقلك إلى صفحة الخدمات الآن... 🚀',
                en: 'Taking you to the services page now... 🚀',
                fr: 'Je vous emmène à la page des services maintenant... 🚀'
            });
        }

        if (message.includes('أسعار') || message.includes('pricing') || message.includes('بشحال') || message.includes('شحال')) {
            setTimeout(() => {
                window.location.href = '/pricing';
            }, 1000);
            return this.getResponseByLanguage({
                ar: 'سأنقلك إلى صفحة الأسعار الآن... 💰',
                en: 'Taking you to the pricing page now... 💰',
                fr: 'Je vous emmène à la page des tarifs maintenant... 💰'
            });
        }

        if (message.includes('حول') || message.includes('about')) {
            setTimeout(() => {
                window.location.href = '/about';
            }, 1000);
            return this.getResponseByLanguage({
                ar: 'سأنقلك إلى صفحة حولنا الآن... ℹ️',
                en: 'Taking you to the about page now... ℹ️',
                fr: 'Je vous emmène à la page à propos maintenant... ℹ️'
            });
        }

        if (message.includes('اتصال') || message.includes('contact') || message.includes('كيفاش نتواصل')) {
            setTimeout(() => {
                window.location.href = '/contact';
            }, 1000);
            return this.getResponseByLanguage({
                ar: 'سأنقلك إلى صفحة اتصل بنا الآن... 📞',
                en: 'Taking you to the contact page now... 📞',
                fr: 'Je vous emmène à la page de contact maintenant... 📞'
            });
        }

        if (message.includes('رئيسية') || message.includes('home') || message.includes('الصفحة الأولى')) {
            setTimeout(() => {
                window.location.href = '/';
            }, 1000);
            return this.getResponseByLanguage({
                ar: 'سأنقلك إلى الصفحة الرئيسية الآن... 🏠',
                en: 'Taking you to the home page now... 🏠',
                fr: 'Je vous emmène à la page d\'accueil maintenant... 🏠'
            });
        }

        return this.getResponseByLanguage({
            ar: 'يمكنني مساعدتك في الانتقال إلى: الصفحة الرئيسية، الخدمات، الأسعار، حولنا، أو اتصل بنا. فين بغيتي تمشي؟',
            en: 'I can help you navigate to: Home, Services, Pricing, About, or Contact. Where would you like to go?',
            fr: 'Je peux vous aider à naviguer vers: Accueil, Services, Tarifs, À propos, ou Contact. Où voulez-vous aller?'
        });
    }

    getResponseByLanguage(responses) {
        return responses[this.currentLanguage] || responses.ar;
    }

    getResponsesByLanguage(responses) {
        return responses[this.currentLanguage] || responses.ar;
    }

    getLoginInfo() {
        return this.getResponseByLanguage({
            ar: `لتسجيل الدخول أو إنشاء حساب جديد:

🔐 انقر على "تسجيل الدخول" في أعلى الصفحة
📝 أو انقر على "إنشاء حساب" للتسجيل
👤 يمكنك أيضاً استخدام حسابك في Google أو Facebook

بغيتي نوديك لصفحة تسجيل الدخول؟`,
            en: `To login or create a new account:

🔐 Click "Login" at the top of the page
📝 Or click "Register" to sign up
👤 You can also use your Google or Facebook account

Would you like me to take you to the login page?`,
            fr: `Pour vous connecter ou créer un nouveau compte:

🔐 Cliquez sur "Connexion" en haut de la page
📝 Ou cliquez sur "S'inscrire" pour vous inscrire
👤 Vous pouvez aussi utiliser votre compte Google ou Facebook

Voulez-vous que je vous emmène à la page de connexion?`
        });
    }

    getPaymentInfo() {
        return this.getResponseByLanguage({
            ar: `للدفع وشراء الخدمات:

💳 ندعم جميع بطاقات الائتمان
💰 PayPal متاح أيضاً
🔒 جميع المدفوعات آمنة ومشفرة
📱 يمكنك الدفع من الهاتف أو الكمبيوتر

باش تشري خدمة:
1. اختار الخدمة من صفحة الخدمات
2. اختار المستوى اللي بغيتي
3. كليكي على البطاقة باش تخلص

بغيتي نوديك لصفحة الخدمات؟`,
            en: `For payment and purchasing services:

💳 We support all credit cards
💰 PayPal is also available
🔒 All payments are secure and encrypted
📱 You can pay from phone or computer

To buy a service:
1. Choose the service from the services page
2. Select the level you want
3. Click on the card to pay

Would you like me to take you to the services page?`,
            fr: `Pour le paiement et l'achat de services:

💳 Nous acceptons toutes les cartes de crédit
💰 PayPal est également disponible
🔒 Tous les paiements sont sécurisés et cryptés
📱 Vous pouvez payer depuis le téléphone ou l'ordinateur

Pour acheter un service:
1. Choisissez le service depuis la page des services
2. Sélectionnez le niveau que vous voulez
3. Cliquez sur la carte pour payer

Voulez-vous que je vous emmène à la page des services?`
        });
    }

    getDefaultResponse() {
        const responses = this.getResponsesByLanguage({
            ar: [
                "شكراً لسؤالك! يمكنني مساعدتك في معرفة المزيد عن خدماتنا، الأسعار، أو أي استفسار آخر. شنو بغيتي تعرف؟",
                "أنا هنا لمساعدتك! بغيتي تعرف أكثر على تحليل الشخصية، تحسين السيرة الذاتية، أو محاكاة المقابلات؟",
                "يسعدني مساعدتك! يمكنك سؤالي عن أي شيء متعلق بخدماتنا أو الموقع. كيفاش يمكنني نعاونك؟",
                "يمكنني مساعدتك في:\n• التنقل في الموقع\n• معرفة الخدمات والأسعار\n• تسجيل الدخول والدفع\n• أي أسئلة أخرى\n\nشنو محتاج؟"
            ],
            en: [
                "Thanks for your question! I can help you learn more about our services, pricing, or any other inquiry. What would you like to know?",
                "I'm here to help! Would you like to know more about personality analysis, CV improvement, or interview simulation?",
                "I'm happy to help! You can ask me about anything related to our services or website. How can I assist you?",
                "I can help you with:\n• Website navigation\n• Services and pricing info\n• Login and payment\n• Any other questions\n\nWhat do you need?"
            ],
            fr: [
                "Merci pour votre question! Je peux vous aider à en savoir plus sur nos services, tarifs, ou toute autre demande. Que voulez-vous savoir?",
                "Je suis là pour vous aider! Voulez-vous en savoir plus sur l'analyse de personnalité, l'amélioration de CV, ou la simulation d'entretien?",
                "Je suis ravi de vous aider! Vous pouvez me poser des questions sur nos services ou le site web. Comment puis-je vous aider?",
                "Je peux vous aider avec:\n• Navigation du site\n• Informations sur les services et tarifs\n• Connexion et paiement\n• Toute autre question\n\nDe quoi avez-vous besoin?"
            ]
        });
        return responses[Math.floor(Math.random() * responses.length)];
    }

    speakMessage(message) {
        if (this.synthesis && this.synthesis.speaking === false) {
            const utterance = new SpeechSynthesisUtterance(message);
            utterance.lang = this.getLanguageCode();
            utterance.rate = 0.9;
            utterance.pitch = 1;
            this.synthesis.speak(utterance);
        }
    }

    showError(errorKey) {
        // You can implement error message translation here
        this.addMessage("عذراً، حدث خطأ. يرجى المحاولة مرة أخرى.", 'ai');
    }

    formatMessage(message) {
        // Convert line breaks to HTML
        message = message.replace(/\n/g, '<br>');

        // Make bullet points prettier
        message = message.replace(/•/g, '<span class="text-blue-500">•</span>');

        // Make numbers in lists prettier
        message = message.replace(/(\d+\.)/g, '<span class="font-semibold text-purple-600">$1</span>');

        // Make prices stand out
        message = message.replace(/(\d+\s*درهم)/g, '<span class="font-bold text-green-600">$1</span>');
        message = message.replace(/(\d+\s*DH)/g, '<span class="font-bold text-green-600">$1</span>');

        return message;
    }

    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }

    saveChatHistory() {
        // Save chat history to localStorage
        const messages = Array.from(this.chatMessages.children).map(msg => ({
            content: msg.textContent,
            sender: msg.querySelector('.bg-blue-500') ? 'user' : 'ai',
            timestamp: Date.now()
        }));
        localStorage.setItem('monori_chat_history', JSON.stringify(messages));
    }

    loadChatHistory() {
        // Load chat history from localStorage
        const history = localStorage.getItem('monori_chat_history');
        if (history) {
            try {
                const messages = JSON.parse(history);
                // Only load recent messages (last 24 hours)
                const oneDayAgo = Date.now() - (24 * 60 * 60 * 1000);
                const recentMessages = messages.filter(msg => msg.timestamp > oneDayAgo);
                
                // Clear current messages except welcome message
                const welcomeMessage = this.chatMessages.firstElementChild;
                this.chatMessages.innerHTML = '';
                this.chatMessages.appendChild(welcomeMessage);
                
                // Add recent messages
                recentMessages.forEach(msg => {
                    if (msg.content.trim()) {
                        this.addMessage(msg.content, msg.sender);
                    }
                });
            } catch (error) {
                console.error('Error loading chat history:', error);
            }
        }
    }
}

// Initialize AI Chat when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    window.aiChat = new AIChatAssistant();
});
