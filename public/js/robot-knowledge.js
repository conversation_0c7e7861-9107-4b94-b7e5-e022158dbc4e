// MonOri AI Robot Knowledge Base
const robotKnowledge = {
    // Website Information
    website: {
        name: "MonOri AI",
        description: "منصة ذكية لتوجيه الطلاب والخريجين نحو المسار المهني المناسب",
        languages: ["العربية", "English", "Français"],
        features: [
            "تحليل الشخصية بالذكاء الاصطناعي",
            "إنشاء وتحسين السيرة الذاتية",
            "محاكاة المقابلات الوظيفية",
            "توصيات وظيفية مخصصة",
            "تدريب مهني شخصي"
        ]
    },

    // Services Information
    services: {
        personality_analysis: {
            name: "تحليل الشخصية",
            description: "اكتشف نمط شخصيتك ونقاط قوتك من خلال تحليل علمي متقدم",
            levels: {
                basic: {
                    name: "تحليل أساسي",
                    price: "19 درهم",
                    duration: "15-20 دقيقة",
                    features: [
                        "تحليل الشخصية الأساسي",
                        "تقرير مبسط (5 صفحات)",
                        "نصائح عامة",
                        "دعم عبر البريد الإلكتروني"
                    ]
                },
                premium: {
                    name: "تحليل متقدم",
                    price: "39 درهم",
                    duration: "25-30 دقيقة",
                    features: [
                        "تحليل شخصية شامل",
                        "تقرير مفصل (15 صفحة)",
                        "نصائح مخصصة",
                        "تحليل نقاط القوة والضعف",
                        "توصيات مهنية",
                        "دعم مباشر"
                    ]
                },
                professional: {
                    name: "تحليل احترافي",
                    price: "69 درهم",
                    duration: "35-40 دقيقة",
                    features: [
                        "تحليل شخصية احترافي كامل",
                        "تقرير مفصل (25+ صفحة)",
                        "استشارة شخصية مع خبير",
                        "خطة تطوير شخصية",
                        "تحليل التوافق المهني",
                        "متابعة لمدة 3 أشهر",
                        "دعم أولوية 24/7"
                    ]
                }
            }
        },
        cv_improvement: {
            name: "تحسين السيرة الذاتية",
            description: "احصل على سيرة ذاتية احترافية محسنة بالذكاء الاصطناعي",
            levels: {
                basic: {
                    name: "تحسين أساسي",
                    price: "25 درهم",
                    duration: "10-15 دقيقة",
                    features: [
                        "تحسين أساسي للسيرة الذاتية",
                        "نصائح عامة",
                        "تحليل بسيط",
                        "تقرير أساسي"
                    ]
                },
                premium: {
                    name: "تحسين متقدم",
                    price: "49 درهم",
                    duration: "20-25 دقيقة",
                    features: [
                        "تحسين شامل للسيرة الذاتية",
                        "تحليل مفصل",
                        "اقتراحات محددة",
                        "تحسين الكلمات المفتاحية",
                        "تقرير مفصل"
                    ]
                },
                professional: {
                    name: "تحسين احترافي",
                    price: "79 درهم",
                    duration: "30-35 دقيقة",
                    features: [
                        "تحسين احترافي مع إعادة كتابة كاملة",
                        "تحليل عميق",
                        "استشارة شخصية",
                        "تحسين التصميم",
                        "متابعة مستمرة"
                    ]
                }
            }
        },
        interview_simulation: {
            name: "محاكاة المقابلات",
            description: "تدرب على المقابلات الوظيفية مع الذكاء الاصطناعي",
            levels: {
                basic: {
                    name: "محاكاة أساسية",
                    price: "29 درهم",
                    duration: "20-25 دقيقة",
                    features: [
                        "محاكاة مقابلة أساسية",
                        "أسئلة عامة",
                        "تقييم بسيط",
                        "نصائح أساسية"
                    ]
                },
                premium: {
                    name: "محاكاة متقدمة",
                    price: "55 درهم",
                    duration: "30-35 دقيقة",
                    features: [
                        "محاكاة مقابلة شاملة",
                        "أسئلة متخصصة",
                        "تقييم مفصل",
                        "تحليل الأداء",
                        "نصائح مخصصة"
                    ]
                },
                professional: {
                    name: "محاكاة احترافية",
                    price: "89 درهم",
                    duration: "40-45 دقيقة",
                    features: [
                        "محاكاة مقابلة احترافية",
                        "تدريب شخصي",
                        "تحليل شامل",
                        "خطة تحسين",
                        "متابعة مستمرة"
                    ]
                }
            }
        }
    },

    // Subscription Plans
    subscriptions: {
        basic: {
            name: "الخطة الأساسية",
            price: "19 درهم/شهر",
            features: [
                "اختبارات شخصية غير محدودة",
                "منشئ السيرة الذاتية الأساسي",
                "توصيات وظيفية",
                "دعم عبر البريد الإلكتروني"
            ]
        },
        gold: {
            name: "الخطة الذهبية",
            price: "49 درهم/شهر",
            features: [
                "جميع مميزات الخطة الأساسية",
                "تحليلات متقدمة",
                "دعم أولوية",
                "تدريب المقابلات بالذكاء الاصطناعي",
                "تقارير مخصصة"
            ]
        },
        premium: {
            name: "الخطة المتميزة",
            price: "99 درهم/شهر",
            features: [
                "جميع مميزات الخطة الذهبية",
                "تصدير PDF عالي الجودة",
                "مقابلات صوتية تفاعلية",
                "جلسات تدريب شخصية",
                "دعم أولوية 24/7",
                "وصول للـ API"
            ]
        }
    },

    // FAQ
    faq: [
        {
            question: "كيف يمكنني البدء في استخدام المنصة؟",
            answer: "يمكنك إنشاء حساب مجاني والبدء في اختبار تحليل الشخصية فوراً."
        },
        {
            question: "هل خدماتكم مجانية؟",
            answer: "نوفر خدمات أساسية مجانية، مع خطط مدفوعة للمميزات المتقدمة."
        },
        {
            question: "كم من الوقت يستغرق التحليل؟",
            answer: "تحليل الشخصية يستغرق حوالي 15-20 دقيقة، والنتائج فورية."
        },
        {
            question: "ما هي طرق الدفع المتاحة؟",
            answer: "نقبل جميع بطاقات الائتمان والتحويلات البنكية والمدفوعات الإلكترونية."
        },
        {
            question: "هل يمكنني إلغاء اشتراكي في أي وقت؟",
            answer: "نعم، يمكنك إلغاء اشتراكك في أي وقت دون أي رسوم إضافية."
        }
    ],

    // Contact Information
    contact: {
        email: "<EMAIL>",
        phone: "+212 6XX XXX XXX",
        address: "مركز الأعمال التكنولوجي، الدار البيضاء، المغرب",
        hours: "الاثنين إلى الجمعة، 9:00 - 18:00"
    },

    // Common Keywords for AI matching (including Moroccan Darija)
    keywords: {
        services: ["خدمات", "خدمة", "services", "service", "خدماتكم", "شنو كاتقدمو", "شنو عندكم", "واش كاين"],
        pricing: ["أسعار", "سعر", "تكلفة", "pricing", "price", "cost", "بشحال", "شحال", "كيفاش الثمن", "غالي", "رخيص"],
        personality: ["شخصية", "تحليل", "personality", "analysis", "شخصيتي", "كيف شخصيتي", "شكون أنا"],
        cv: ["سيرة", "ذاتية", "cv", "resume", "السيفي", "الورقة ديالي", "كيفاش نكتب السيفي"],
        interview: ["مقابلة", "مقابلات", "interview", "interviews", "انترفيو", "مقابلة شغل", "كيفاش نجاوب"],
        account: ["حساب", "حسابي", "account", "profile", "كونت", "كيفاش ندخل", "نسيت الباسوورد"],
        help: ["مساعدة", "help", "support", "aide", "عاونوني", "ما فهمتش", "مشكل", "مساعدة"],
        contact: ["تواصل", "اتصال", "contact", "كيفاش نتواصل معكم", "فين نلقاكم", "رقم التيليفون"],
        subscription: ["اشتراك", "خطة", "subscription", "plan", "كيفاش نشترك", "شنو الخطط"],
        navigation: ["اذهب", "انتقل", "go", "navigate", "aller", "دير", "وديني", "نمشي", "صفحة"],
        greeting: ["مرحبا", "السلام", "أهلا", "hello", "hi", "bonjour", "سلام", "كيفاش الحال", "لاباس"],
        darija_questions: ["شنو", "كيفاش", "فين", "واش", "علاش", "امتى", "شكون", "بشحال", "وقتاش"]
    }
};

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
    module.exports = robotKnowledge;
}
