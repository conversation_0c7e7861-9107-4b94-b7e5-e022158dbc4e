<svg width="32" height="32" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Outer Circle with Gradient -->
  <circle cx="50" cy="50" r="42" stroke="url(#gradient1)" stroke-width="4" fill="white"/>

  <!-- Top Point -->
  <path d="M50 8 L56 18 L44 18 Z" fill="url(#gradient1)"/>

  <!-- Bottom Point -->
  <path d="M50 92 L56 82 L44 82 Z" fill="url(#gradient2)"/>

  <!-- Left Point -->
  <path d="M8 50 L18 44 L18 56 Z" fill="url(#gradient1)"/>

  <!-- Right Point -->
  <path d="M92 50 L82 44 L82 56 Z" fill="url(#gradient2)"/>

  <!-- Brain Left Side -->
  <path d="M28 32 C23 27, 18 32, 23 37 C18 42, 23 47, 28 42 C23 52, 28 57, 33 52 C28 62, 33 67, 38 62 C33 72, 38 77, 48 72 L48 28 C43 23, 33 28, 28 32 Z" fill="url(#gradient1)" opacity="0.9"/>

  <!-- Brain Right Side -->
  <path d="M72 32 C77 27, 82 32, 77 37 C82 42, 77 47, 72 42 C77 52, 72 57, 67 52 C72 62, 67 67, 62 62 C67 72, 62 77, 52 72 L52 28 C57 23, 67 28, 72 32 Z" fill="url(#gradient2)" opacity="0.9"/>

  <!-- Brain Center Division -->
  <line x1="50" y1="28" x2="50" y2="72" stroke="rgba(255,255,255,0.7)" stroke-width="2"/>

  <!-- Brain Details - Left -->
  <path d="M33 37 C28 35, 31 42, 35 39" stroke="rgba(255,255,255,0.8)" stroke-width="1.5" fill="none"/>
  <path d="M38 47 C33 45, 36 52, 40 49" stroke="rgba(255,255,255,0.8)" stroke-width="1.5" fill="none"/>
  <path d="M35 57 C30 55, 33 62, 37 59" stroke="rgba(255,255,255,0.8)" stroke-width="1.5" fill="none"/>

  <!-- Brain Details - Right -->
  <path d="M67 37 C72 35, 69 42, 65 39" stroke="rgba(255,255,255,0.8)" stroke-width="1.5" fill="none"/>
  <path d="M62 47 C67 45, 64 52, 60 49" stroke="rgba(255,255,255,0.8)" stroke-width="1.5" fill="none"/>
  <path d="M65 57 C70 55, 67 62, 63 59" stroke="rgba(255,255,255,0.8)" stroke-width="1.5" fill="none"/>
  
  <!-- Gradients -->
  <defs>
    <linearGradient id="gradient1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3B82F6;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#1D4ED8;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366F1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="gradient2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#6366F1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#8B5CF6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#A855F7;stop-opacity:1" />
    </linearGradient>
  </defs>
</svg>
